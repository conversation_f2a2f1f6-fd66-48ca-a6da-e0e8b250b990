import { Injectable, NotFoundException } from '@nestjs/common';
import { PermissionRepository } from '@modules/employee/repositories';
import { Permission } from '@modules/employee/entities';
import { CreatePermissionDto } from '../dto/create-permission.dto';

/**
 * Service xử lý logic liên quan đến permission
 */
@Injectable()
export class PermissionService {
  constructor(private readonly permissionRepository: PermissionRepository) {}

  /**
   * L<PERSON>y tất cả quyền
   * @returns Danh sách tất cả quyền
   */
  async getAllPermissions(): Promise<Permission[]> {
    return this.permissionRepository.findAll();
  }

  /**
   * L<PERSON>y quyền theo ID
   * @param id ID của quyền
   * @returns Quyền
   */
  async getPermissionById(id: number): Promise<Permission> {
    return this.permissionRepository.findById(id);
  }

  /**
   * Tạo quyền mới
   * @param dto DTO chứa thông tin quyền mới
   * @returns Quyền đã được tạo
   */
  async createPermission(dto: CreatePermissionDto): Promise<Permission> {
    // Kiểm tra xem quyền đã tồn tại chưa
    const existingPermission = await this.permissionRepository.findByModuleAndAction(dto.module, dto.action);
    if (existingPermission) {
      throw new Error(`Quyền với module "${dto.module}" và action "${dto.action}" đã tồn tại`);
    }

    // Tạo quyền mới
    return this.permissionRepository.create(dto);
  }

  /**
   * Xóa quyền
   * @param id ID của quyền
   * @returns Thông báo xóa thành công
   */
  async deletePermission(id: number): Promise<{ success: boolean }> {
    // Kiểm tra xem quyền có tồn tại không
    const permission = await this.permissionRepository.findById(id);
    if (!permission) {
      throw new NotFoundException(`Quyền với ID "${id}" không tồn tại`);
    }

    // Xóa quyền
    await this.permissionRepository.delete(id);
    return { success: true };
  }
}