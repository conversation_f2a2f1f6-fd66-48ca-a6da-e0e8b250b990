{"aiAgents": {"common": {"save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "delete": "Xóa", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON><PERSON>p", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "update": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> mới", "select": "<PERSON><PERSON><PERSON>", "configure": "<PERSON><PERSON><PERSON> h<PERSON>nh", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "loading": "<PERSON><PERSON> tả<PERSON>...", "error": "Đ<PERSON> xảy ra lỗi", "success": "<PERSON><PERSON><PERSON><PERSON> công", "confirm": "<PERSON><PERSON><PERSON>", "confirmDelete": "Bạn có chắc chắn muốn xóa?", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "close": "Đ<PERSON><PERSON>", "back": "Quay lại", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON> đó", "finish": "<PERSON><PERSON><PERSON> th<PERSON>"}, "agentCreate": {"title": "Tạo Agent", "customAgentButton": "Tạo Agent tùy chỉnh", "selectAgentType": "<PERSON><PERSON><PERSON>", "selectAgentDescription": "<PERSON><PERSON><PERSON> một loại Agent từ danh sách dưới đây hoặc tạo Agent tùy chỉnh của riêng bạn.", "configureAgent": "<PERSON><PERSON><PERSON> h<PERSON>nh {name}", "sortBy": "<PERSON><PERSON><PERSON> xếp theo", "sortName": "<PERSON><PERSON><PERSON>", "sortDate": "<PERSON><PERSON><PERSON>", "order": "<PERSON><PERSON><PERSON> tự", "orderAsc": "<PERSON><PERSON><PERSON>", "orderDesc": "<PERSON><PERSON><PERSON><PERSON>", "agentTypeDescription": "Chọn loại agent phù hợp với nhu cầu của bạn. Mỗi loại agent có những khả năng và đặc điểm khác nhau."}, "profileConfig": {"title": "Thông tin cá nhân", "name": "<PERSON><PERSON><PERSON>", "birthDate": "<PERSON><PERSON><PERSON>", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "language": "<PERSON><PERSON><PERSON>", "education": "<PERSON><PERSON><PERSON><PERSON> độ học vấn", "country": "Quốc gia", "position": "<PERSON><PERSON><PERSON> v<PERSON>", "skills": "<PERSON><PERSON> n<PERSON>ng", "personality": "<PERSON><PERSON><PERSON>", "avatar": "Ảnh đại diện", "male": "Nam", "female": "<PERSON><PERSON>", "other": "K<PERSON><PERSON><PERSON>", "highSchool": "<PERSON><PERSON> học", "college": "<PERSON> đẳng", "university": "<PERSON><PERSON><PERSON>", "postgraduate": "<PERSON><PERSON> <PERSON><PERSON><PERSON> học", "addSkill": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> n<PERSON>ng", "addPersonality": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> c<PERSON>ch", "skillPlaceholder": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> enter", "personalityPlaceholder": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> enter"}, "modelConfig": {"title": "<PERSON><PERSON><PERSON>", "provider": "<PERSON><PERSON><PERSON> cung cấp", "model": "Model", "vectorStore": "Vector Store", "advancedSettings": "Cài đặt nâng cao", "maxTokens": "<PERSON>ố <PERSON> tối đa", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "instructions": "Hướng dẫn", "instructionsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> dẫn cho model..."}, "integrationConfig": {"title": "<PERSON><PERSON><PERSON>", "facebook": "Facebook", "website": "Website", "addFacebook": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "addWebsite": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON> Website", "noFacebookIntegration": "Chưa có tích hợp Facebook nào", "noWebsiteIntegration": "Chưa có tích hợp Website nào", "selectFacebook": "<PERSON><PERSON><PERSON> t<PERSON>", "selectWebsite": "Chọn Website", "facebookPageName": "<PERSON><PERSON><PERSON> t<PERSON>", "websiteName": "Tên Website", "websiteUrl": "URL Website"}, "strategyConfig": {"title": "<PERSON><PERSON><PERSON>", "selectStrategy": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> cho <PERSON>", "selectStrategyDescription": "<PERSON><PERSON><PERSON> một chiến lư<PERSON><PERSON> và cấu hình các bước xử lý", "basicStrategy": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> c<PERSON> bản", "basicStrategyDescription": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> đơn giản với các cài đặt mặc định", "advancedStrategy": "<PERSON><PERSON><PERSON> nâng cao", "advancedStrategyDescription": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> với các tùy chọn nâng cao và xử lý phức tạp", "customStrategy": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> tùy chỉnh", "customStrategyDescription": "<PERSON><PERSON><PERSON> chiến lược riêng với các cài đặt tùy chỉnh hoàn toàn", "configureStrategy": "<PERSON><PERSON><PERSON> hình ch<PERSON>", "step": "B<PERSON><PERSON><PERSON> {number}", "input": "Input"}, "convertConfig": {"title": "<PERSON><PERSON><PERSON> hình chuyển đổi", "configureFields": "<PERSON><PERSON><PERSON> hình các trường dữ liệu cần thu thập", "configureFieldsDescription": "<PERSON><PERSON><PERSON> các trường dữ liệu mà Agent sẽ thu thập từ người dùng", "noFields": "<PERSON><PERSON><PERSON> có trường dữ liệu nào đ<PERSON><PERSON><PERSON> cấu hình", "addField": "<PERSON>h<PERSON><PERSON> trư<PERSON><PERSON> mới", "editField": "Chỉnh sửa trường", "fieldName": "<PERSON><PERSON><PERSON> tr<PERSON>", "fieldDescription": "<PERSON><PERSON>", "fieldType": "<PERSON><PERSON><PERSON> dữ liệu", "fieldRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "fieldNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên tr<PERSON> (vd: email)", "fieldDescriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả của trường (vd: <PERSON><PERSON><PERSON> tất cả email của người dùng)", "pleaseEnterAllFields": "<PERSON><PERSON> lòng nhập đ<PERSON>y đủ thông tin trường", "text": "<PERSON><PERSON><PERSON>", "email": "Email", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "number": "Số", "date": "<PERSON><PERSON><PERSON>", "address": "Địa chỉ", "name": "<PERSON><PERSON> tên"}, "responseConfig": {"title": "<PERSON><PERSON><PERSON> nguyên ph<PERSON>n hồi", "configureResources": "<PERSON><PERSON><PERSON> hình tài nguyên phản hồi cho <PERSON>", "configureResourcesDescription": "<PERSON><PERSON><PERSON> các tài nguyên mà Agent có thể sử dụng để phản hồi người dùng", "media": "T<PERSON>i <PERSON>n <PERSON>", "url": "<PERSON><PERSON><PERSON> ng<PERSON>n URL", "product": "<PERSON><PERSON><PERSON> ng<PERSON>m", "noMedia": "Chưa có tài nguyên Media nào", "noUrl": "Chưa có tài nguyên URL nào", "noProduct": "<PERSON><PERSON><PERSON> có tài nguyên <PERSON>ản phẩm nào", "selectMedia": "Chọn media", "selectUrl": "Chọn URL", "selectProduct": "<PERSON><PERSON><PERSON> sản ph<PERSON>m"}}}