import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * DTO para consultar templates de email con paginación y filtros
 */
export class TemplateEmailQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Filtrar por nombre del template',
    example: 'bienvenida',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Filtrar por tag',
    example: 'registro',
    required: false,
  })
  @IsString()
  @IsOptional()
  tag?: string;

  constructor() {
    super();
    this.sortBy = 'createdAt';
    this.sortDirection = SortDirection.DESC;
  }
}
