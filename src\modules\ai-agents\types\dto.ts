/**
 * DTO Types cho AI Agents module
 * File này chứa các interface cho Data Transfer Objects
 * Tuân thủ quy tắc ProductGuide.md - Section 3: TYPESCRIPT
 */

import { TypeAgentStatus, TypeProviderEnum, BaseModelSortByEnum } from './enums';
import { ConversionField, Module, Resource, Vector } from './agent.types';
import { SortDirection } from './query';

/**
 * Interface cho cấu hình Type Agent
 */
export interface TypeAgentConfig {
  hasProfile?: boolean;
  hasOutput?: boolean;
  hasConversion?: boolean;
  hasResources?: boolean;
  hasStrategy?: boolean;
  hasMultiAgent?: boolean;
}

/**
 * Interface cho Model Config
 * ✅ ĐÚNG: Specific types thay vì any
 */
export interface ModelConfigDto {
  modelId: string;
  temperature?: number;
  top_p?: number;
  top_k?: number;
  max_tokens?: number;
  provider_id?: string;
  instructions?: string;
}

/**
 * Interface cho Profile
 * ✅ ĐÚNG: Union types cho specific values
 */
export interface ProfileDto {
  name?: string;
  avatar?: string;
  gender?: 'male' | 'female' | 'other';
  dateOfBirth?: string; // ISO date string
  position?: string;
  education?: string;
  skills?: string[];
  personality?: string[];
  languages?: string[];
  country?: string;
}

/**
 * Interface cho Vector Store
 */
export interface VectorStoreDto {
  id: string;
  name: string;
}

/**
 * Interface cho Group Tool
 */
export interface GroupToolDto {
  id: number;
  name: string;
  description: string | null;
}

/**
 * Interface cho Agent List Item
 */
export interface AgentListItemDto {
  id: string;
  name: string;
  avatar: string | null;
  typeId: number;
  typeName: string;
  exp: number;
  expMax: number;
  level: number;
  badge_url: string;
  model_id: string;
  active: boolean;
  createdAt: number;
  updatedAt: number;
}

/**
 * Interface cho Agent Detail
 */
export interface AgentDetailDto extends AgentListItemDto {
  modelConfig: ModelConfigDto;
  provider_type?: TypeProviderEnum;
  instruction: string;
  profile: ProfileDto;
  vectorStores?: VectorStoreDto;
}

/**
 * Interface cho Type Agent List Item
 */
export interface TypeAgentListItemDto {
  id: number;
  name: string;
  description: string | null;
  config: TypeAgentConfig;
  createdAt: number;
}

/**
 * Interface cho Type Agent Detail
 */
export interface TypeAgentDetailDto extends TypeAgentListItemDto {
  updatedAt: number;
  status: TypeAgentStatus;
  isSystem: boolean;
  groupTools: GroupToolDto[];
}

/**
 * Interface cho Base Model List Item
 */
export interface BaseModelListItemDto {
  id: string;
  model_id: string;
  description: string;
  providerType: TypeProviderEnum;
  config: {
    // Old format
    top_p?: boolean;
    top_k?: boolean;
    function?: boolean;
    file_search?: boolean;
    temperature?: boolean;
    response_format?: string[];
    code_interpreter?: boolean;
    reasoning_effort?: string[];

    // New format
    hasText?: boolean;
    hasTopK?: boolean;
    hasTopP?: boolean;
    hasAudio?: boolean;
    hasImage?: boolean;
    hasVideo?: boolean;
    hasFunction?: boolean;
    hasTemperature?: boolean;
    hasReasoningEffort?: string[];
    hasParallelToolCall?: boolean;
  };
  createdAt: number;
}

/**
 * Interface cho query params của Base Models
 */
export interface GetBaseModelsQueryDto {
  page?: number;
  limit?: number;
  provider_type?: TypeProviderEnum;
  sortBy?: BaseModelSortByEnum;
  sortDirection?: SortDirection;
}

/**
 * Interface cho tạo Agent
 */
export interface CreateAgentDto {
  name: string;
  typeId: number;
  instruction?: string;
  modelConfig?: Partial<ModelConfigDto>;
  profile?: Partial<ProfileDto>;
  vectorStoreId?: string;
}

/**
 * Interface cho cập nhật Agent
 */
export interface UpdateAgentDto {
  name?: string;
  instruction?: string;
  modelConfig?: Partial<ModelConfigDto>;
  profile?: Partial<ProfileDto>;
  vectorStoreId?: string;
  // Thêm các field bị thiếu để khắc phục lỗi TypeScript
  conversionFields?: ConversionField[];
  modules?: Module[];
  resources?: Resource[];
  vector?: Vector;
}

/**
 * Interface cho tạo Type Agent
 */
export interface CreateTypeAgentDto {
  name: string;
  description?: string;
  config: TypeAgentConfig;
  groupToolIds: number[];
}

/**
 * Interface cho cập nhật Type Agent
 */
export interface UpdateTypeAgentDto {
  name?: string;
  description?: string;
  config?: TypeAgentConfig;
  groupToolIds?: number[];
}

/**
 * Response cho cập nhật Vector Store
 */
export interface UpdateAgentVectorStoreDto {
  vectorStoreId: string;
}
