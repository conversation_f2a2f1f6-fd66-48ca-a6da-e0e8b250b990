import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

/**
 * DTO cho việc cập nhật permission
 */
export class UpdatePermissionDto {
  /**
   * Hành động
   */
  @ApiProperty({
    description: 'Hành động',
    example: 'create',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Hành động phải là chuỗi' })
  action?: string;

  /**
   * Module
   */
  @ApiProperty({
    description: 'Module',
    example: 'user',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Module phải là chuỗi' })
  module?: string;

  /**
   * <PERSON>ô tả quyền
   */
  @ApiProperty({
    description: 'Mô tả quyền',
    example: 'Tạo người dùng mới',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON>ô tả phải là chuỗi' })
  description?: string;
}
