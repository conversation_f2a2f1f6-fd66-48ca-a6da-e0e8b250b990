import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { UserTagService } from '../services/user-tag.service';
import { CreateTagDto, UpdateTagDto, TagResponseDto } from '../dto/tag';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';

/**
 * Controller xử lý API liên quan đến tag
 */
@ApiTags(SWAGGER_API_TAGS.USER_TAG)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/tags')
export class UserTagController {
  constructor(private readonly userTagService: UserTagService) {}

  /**
   * Tạo tag mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo tag mới' })
  @ApiResponse({ status: 201, description: 'Tag đã được tạo thành công', type: TagResponseDto })
  async create(@CurrentUser() user: JwtPayload, @Body() createTagDto: CreateTagDto): Promise<AppApiResponse<TagResponseDto>> {
    const result = await this.userTagService.create(user.id, createTagDto);
    return wrapResponse(result, 'Tag đã được tạo thành công');
  }

  /**
   * Lấy danh sách tag
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tag' })
  @ApiResponse({ status: 200, description: 'Danh sách tag', type: [TagResponseDto] })
  async findAll(@CurrentUser() user: JwtPayload): Promise<AppApiResponse<TagResponseDto[]>> {
    const result = await this.userTagService.findAll(user.id);
    return wrapResponse(result, 'Danh sách tag');
  }

  /**
   * Lấy thông tin tag theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin tag theo ID' })
  @ApiResponse({ status: 200, description: 'Thông tin tag', type: TagResponseDto })
  @ApiResponse({ status: 404, description: 'Tag không tồn tại' })
  async findOne(@CurrentUser() user: JwtPayload, @Param('id') id: string): Promise<AppApiResponse<TagResponseDto>> {
    const result = await this.userTagService.findOne(user.id, +id);
    return wrapResponse(result, 'Thông tin tag');
  }

  /**
   * Cập nhật tag
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật tag' })
  @ApiResponse({ status: 200, description: 'Tag đã được cập nhật thành công', type: TagResponseDto })
  @ApiResponse({ status: 404, description: 'Tag không tồn tại' })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() updateTagDto: UpdateTagDto,
  ): Promise<AppApiResponse<TagResponseDto>> {
    const result = await this.userTagService.update(user.id, +id, updateTagDto);
    return wrapResponse(result, 'Tag đã được cập nhật thành công');
  }

  /**
   * Xóa tag
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa tag' })
  @ApiResponse({ status: 200, description: 'Tag đã được xóa thành công' })
  @ApiResponse({ status: 404, description: 'Tag không tồn tại' })
  async remove(@CurrentUser() user: JwtPayload, @Param('id') id: string): Promise<AppApiResponse<{ success: boolean }>> {
    const result = await this.userTagService.remove(user.id, +id);
    return wrapResponse({ success: result }, 'Tag đã được xóa thành công');
  }
}
