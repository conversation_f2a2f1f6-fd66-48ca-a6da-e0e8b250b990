import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography } from '@/shared/components/common';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Trang tổng quan Marketplace
 */
const MarketplacePage: React.FC = () => {
  const { t } = useTranslation('marketplace');

  return (
    <div>
      <Typography variant="h1" className="mb-6">
        {t('title', 'Sàn giao dịch')}
      </Typography>
      <Typography variant="body1" className="mb-8">
        {t('description', 'Quản lý sản phẩm và giao dịch trên sàn giao dịch')}
      </Typography>

      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Marketplace Card */}
        <ModuleCard
          title={t('marketplace.title', 'Sàn giao dịch')}
          description={t('marketplace.description', 'Khám phá và mua sắm sản phẩm trên sàn giao dịch')}
          icon="shopping-cart"
          linkTo="/marketplace/products"
        />

        {/* Products for Sale Card */}
        <ModuleCard
          title={t('productsForSale.title', 'Sản phẩm đăng bán')}
          description={t('productsForSale.description', 'Quản lý các sản phẩm bạn đang bán trên sàn')}
          icon="tag"
          linkTo="/marketplace/products-for-sale"
        />

        {/* Purchased Products Card */}
        <ModuleCard
          title={t('purchasedProducts.title', 'Sản phẩm đã mua')}
          description={t('purchasedProducts.description', 'Xem và quản lý các sản phẩm bạn đã mua')}
          icon="shopping-bag"
          linkTo="/marketplace/purchased-products"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default MarketplacePage;
