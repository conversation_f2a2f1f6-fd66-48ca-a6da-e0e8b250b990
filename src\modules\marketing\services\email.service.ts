import { apiClient } from '@/shared/api';
import type { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import type {
  EmailTemplateDto,
  EmailCampaignDto,
  EmailDto,
  EmailAutomationDto,
  EmailAnalyticsDto,
  EmailMetricsResponse,
  CreateEmailTemplateDto,
  UpdateEmailTemplateDto,
  CreateEmailCampaignDto,
  CreateEmailAutomationDto,
  SendTestEmailDto,
  EmailTemplateQueryDto,
  EmailCampaignQueryDto,
  EmailAnalyticsQueryDto,
} from '../types/email.types';

/**
 * Service cho Email Marketing APIs
 * TODO: Implement proper API calls when backend is ready
 */
export class EmailService {
  private static readonly BASE_URL = '/v1/marketing/email';

  /**
   * Lấy danh sách email templates
   */
  static async getTemplates(query?: EmailTemplateQueryDto): Promise<ApiResponseDto<PaginatedResult<EmailTemplateDto>>> {
    return apiClient.get(`${this.BASE_URL}/templates`, { params: query });
  }

  /**
   * Lấy chi tiết email template
   */
  static async getTemplate(id: string): Promise<ApiResponseDto<EmailTemplateDto>> {
    return apiClient.get(`${this.BASE_URL}/templates/${id}`);
  }

  /**
   * Tạo email template mới
   */
  static async createTemplate(data: CreateEmailTemplateDto): Promise<ApiResponseDto<EmailTemplateDto>> {
    return apiClient.post(`${this.BASE_URL}/templates`, data);
  }

  /**
   * Cập nhật email template
   */
  static async updateTemplate(id: string, data: UpdateEmailTemplateDto): Promise<ApiResponseDto<EmailTemplateDto>> {
    return apiClient.put(`${this.BASE_URL}/templates/${id}`, data);
  }

  /**
   * Xóa email template
   */
  static async deleteTemplate(id: string): Promise<ApiResponseDto<boolean>> {
    return apiClient.delete(`${this.BASE_URL}/templates/${id}`);
  }

  /**
   * Duplicate email template
   */
  static async duplicateTemplate(id: string, name: string): Promise<ApiResponseDto<EmailTemplateDto>> {
    return apiClient.post(`${this.BASE_URL}/templates/${id}/duplicate`, { name });
  }

  /**
   * Preview email template
   */
  static async previewTemplate(id: string, variables?: Record<string, string>): Promise<ApiResponseDto<{
    htmlContent: string;
    textContent: string;
    subject: string;
  }>> {
    return apiClient.post(`${this.BASE_URL}/templates/${id}/preview`, { variables });
  }

  /**
   * Gửi test email
   */
  static async sendTestEmail(data: SendTestEmailDto): Promise<ApiResponseDto<{ sentCount: number; failedCount: number }>> {
    return apiClient.post(`${this.BASE_URL}/templates/test`, data);
  }

  /**
   * Lấy danh sách email campaigns
   */
  static async getCampaigns(query?: EmailCampaignQueryDto): Promise<ApiResponseDto<PaginatedResult<EmailCampaignDto>>> {
    return apiClient.get(`${this.BASE_URL}/campaigns`, { params: query });
  }

  /**
   * Lấy chi tiết email campaign
   */
  static async getCampaign(id: string): Promise<ApiResponseDto<EmailCampaignDto>> {
    return apiClient.get(`${this.BASE_URL}/campaigns/${id}`);
  }

  /**
   * Tạo email campaign mới
   */
  static async createCampaign(data: CreateEmailCampaignDto): Promise<ApiResponseDto<EmailCampaignDto>> {
    return apiClient.post(`${this.BASE_URL}/campaigns`, data);
  }

  /**
   * Cập nhật email campaign
   */
  static async updateCampaign(id: string, data: Partial<CreateEmailCampaignDto>): Promise<ApiResponseDto<EmailCampaignDto>> {
    return apiClient.put(`${this.BASE_URL}/campaigns/${id}`, data);
  }

  /**
   * Xóa email campaign
   */
  static async deleteCampaign(id: string): Promise<ApiResponseDto<boolean>> {
    return apiClient.delete(`${this.BASE_URL}/campaigns/${id}`);
  }

  /**
   * Gửi email campaign
   */
  static async sendCampaign(id: string): Promise<ApiResponseDto<{ campaignId: string; totalRecipients: number }>> {
    return apiClient.post(`${this.BASE_URL}/campaigns/${id}/send`);
  }

  /**
   * Pause email campaign
   */
  static async pauseCampaign(id: string): Promise<ApiResponseDto<EmailCampaignDto>> {
    return apiClient.post(`${this.BASE_URL}/campaigns/${id}/pause`);
  }

  /**
   * Resume email campaign
   */
  static async resumeCampaign(id: string): Promise<ApiResponseDto<EmailCampaignDto>> {
    return apiClient.post(`${this.BASE_URL}/campaigns/${id}/resume`);
  }

  /**
   * Cancel email campaign
   */
  static async cancelCampaign(id: string): Promise<ApiResponseDto<EmailCampaignDto>> {
    return apiClient.post(`${this.BASE_URL}/campaigns/${id}/cancel`);
  }

  /**
   * Lấy analytics cho campaign
   */
  static async getCampaignAnalytics(id: string): Promise<ApiResponseDto<{
    totalSent: number;
    delivered: number;
    opened: number;
    clicked: number;
    bounced: number;
    unsubscribed: number;
    openRate: number;
    clickRate: number;
    bounceRate: number;
    unsubscribeRate: number;
    chartData: Array<{
      date: string;
      sent: number;
      delivered: number;
      opened: number;
      clicked: number;
    }>;
  }>> {
    return apiClient.get(`${this.BASE_URL}/campaigns/${id}/analytics`);
  }

  /**
   * Lấy danh sách emails của campaign
   */
  static async getCampaignEmails(id: string, query?: { page?: number; limit?: number; status?: string }): Promise<ApiResponseDto  <PaginatedResult<EmailDto>>> {
    return apiClient.get(`${this.BASE_URL}/campaigns/${id}/emails`, { params: query });
  }

  /**
   * Lấy danh sách email automations
   */
  static async getAutomations(query?: { page?: number; limit?: number; search?: string; status?: string }): Promise<ApiResponseDto<PaginatedResult<EmailAutomationDto>>> {
    return apiClient.get(`${this.BASE_URL}/automations`, { params: query });
  }

  /**
   * Lấy chi tiết email automation
   */
  static async getAutomation(id: string): Promise<ApiResponseDto<EmailAutomationDto>> {
    return apiClient.get(`${this.BASE_URL}/automations/${id}`);
  }

  /**
   * Tạo email automation mới
   */
  static async createAutomation(data: CreateEmailAutomationDto): Promise<ApiResponseDto<EmailAutomationDto>> {
    return apiClient.post(`${this.BASE_URL}/automations`, data);
  }

  /**
   * Cập nhật email automation
   */
  static async updateAutomation(id: string, data: Partial<CreateEmailAutomationDto>): Promise<ApiResponseDto<EmailAutomationDto>> {
    return apiClient.put(`${this.BASE_URL}/automations/${id}`, data);
  }

  /**
   * Xóa email automation
   */
  static async deleteAutomation(id: string): Promise<ApiResponseDto<boolean>> {
    return apiClient.delete(`${this.BASE_URL}/automations/${id}`);
  }

  /**
   * Activate email automation
   */
  static async activateAutomation(id: string): Promise<ApiResponseDto<EmailAutomationDto>> {
    return apiClient.post(`${this.BASE_URL}/automations/${id}/activate`);
  }

  /**
   * Deactivate email automation
   */
  static async deactivateAutomation(id: string): Promise<ApiResponseDto<EmailAutomationDto>> {
    return apiClient.post(`${this.BASE_URL}/automations/${id}/deactivate`);
  }

  /**
   * Lấy tổng quan analytics
   */
  static async getAnalytics(query?: EmailAnalyticsQueryDto): Promise<ApiResponseDto<EmailAnalyticsDto>> {
    return apiClient.get(`${this.BASE_URL}/analytics`, { params: query });
  }

  /**
   * Lấy metrics theo thời gian
   */
  static async getMetrics(query?: EmailAnalyticsQueryDto): Promise<ApiResponseDto<EmailMetricsResponse>> {
    return apiClient.get(`${this.BASE_URL}/metrics`, { params: query });
  }

  /**
   * Bulk operations cho campaigns
   */
  static async bulkCampaignOperation(data: {
    campaignIds: string[];
    operation: 'PAUSE' | 'RESUME' | 'CANCEL' | 'DELETE';
  }): Promise<ApiResponseDto<{ processedCount: number; failedCount: number }>> {
    return apiClient.post(`${this.BASE_URL}/campaigns/bulk`, data);
  }

  /**
   * Import email list
   */
  static async importEmailList(data: FormData): Promise<ApiResponseDto<{ importedCount: number; failedCount: number; errors: string[] }>> {
    return apiClient.post(`${this.BASE_URL}/import`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  }

  /**
   * Export campaign data
   */
  static async exportCampaignData(id: string, format: 'CSV' | 'EXCEL' = 'CSV'): Promise<ApiResponseDto<{ downloadUrl: string }>> {
    return apiClient.post(`${this.BASE_URL}/campaigns/${id}/export`, { format });
  }

  /**
   * Validate email template
   */
  static async validateTemplate(data: { htmlContent: string; variables: Record<string, string> }): Promise<ApiResponseDto<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }>> {
    return apiClient.post(`${this.BASE_URL}/templates/validate`, data);
  }

  /**
   * Get email deliverability score
   */
  static async getDeliverabilityScore(templateId: string): Promise<ApiResponseDto<{
    score: number;
    factors: Array<{
      factor: string;
      score: number;
      suggestions: string[];
    }>;
  }>> {
    return apiClient.get(`${this.BASE_URL}/templates/${templateId}/deliverability`);
  }
}
