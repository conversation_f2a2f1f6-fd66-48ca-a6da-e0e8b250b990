import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc thêm/xóa tag cho người theo dõi
 */
export class TagRequestDto {
  @ApiProperty({
    description: 'ID của người theo dõi',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  followerId: string;

  @ApiProperty({
    description: 'Tag cần thêm/xóa',
    example: 'vip',
  })
  @IsString()
  @IsNotEmpty()
  tagName: string;
}
