import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho tạo segment
 */
export class CreateSegmentDto {
  /**
   * Tên segment
   * @example "Khách hàng VIP"
   */
  @ApiProperty({
    description: 'Tên segment',
    example: 'Khách hàng VIP',
  })
  @IsNotEmpty({ message: 'Tên segment không được để trống' })
  @IsString({ message: 'Tên segment phải là chuỗi' })
  @MaxLength(255, { message: 'Tên segment không được vượt quá 255 ký tự' })
  name: string;

  /**
   * Mô tả segment
   * @example "Khách hàng có tổng chi tiêu trên 10 triệu"
   */
  @ApiProperty({
    description: 'Mô tả segment',
    example: '<PERSON>h<PERSON>ch hàng có tổng chi tiêu trên 10 triệu',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON><PERSON> tả segment phải là chuỗi' })
  description?: string;

  /**
   * Tiêu chí lọc khách hàng
   */
  @ApiProperty({
    description: 'Tiêu chí lọc khách hàng',
    example: {
      conditions: [
        { field: 'totalSpent', operator: 'greaterThan', value: 10000000 }
      ],
      operator: 'AND'
    },
  })
  @IsNotEmpty({ message: 'Tiêu chí lọc không được để trống' })
  criteria: any;
}
