import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * Enum cho trạng thái template ZNS
 */
export enum ZnsTemplateStatus {
  APPROVED = 'approved',
  PENDING = 'pending',
  REJECTED = 'rejected',
  ALL = 'all',
}

/**
 * DTO cho việc truy vấn danh sách template ZNS
 */
export class ZnsTemplateQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo tên template',
    example: 'đơn hàng',
    required: false,
  })
  @IsOptional()
  @IsString()
  templateName?: string;

  @ApiProperty({
    description: 'Lọc theo trạng thái',
    enum: ZnsTemplateStatus,
    example: ZnsTemplateStatus.APPROVED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZnsTemplateStatus)
  status?: ZnsTemplateStatus;

  constructor() {
    super();
    this.sortBy = 'createdAt';
    this.sortDirection = SortDirection.DESC;
  }
}
