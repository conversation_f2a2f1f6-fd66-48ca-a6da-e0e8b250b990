import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin vai trò trong phản hồi đăng nhập
 */
export class EmployeeRoleResponseDto {
  @ApiProperty({
    description: 'ID của vai trò',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên vai trò',
    example: 'Admin'
  })
  name: string;

  @ApiProperty({
    description: 'Mã vai trò',
    example: 'admin'
  })
  code: string;
}

/**
 * DTO cho thông tin nhân viên trong phản hồi đăng nhập
 */
export class EmployeeInfoResponseDto {
  @ApiProperty({
    description: 'ID của nhân viên',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Email của nhân viên',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'Họ tên đầy đủ của nhân viên',
    example: '<PERSON>uyễn <PERSON>ăn <PERSON>'
  })
  fullName: string;

  @ApiProperty({
    description: 'URL ảnh đại diện của nhân viên',
    example: 'https://example.com/avatar.jpg',
    required: false
  })
  avatar?: string;

  @ApiProperty({
    description: 'Danh sách vai trò của nhân viên',
    type: [EmployeeRoleResponseDto]
  })
  roles: EmployeeRoleResponseDto[];

  @ApiProperty({
    description: 'Danh sách quyền của nhân viên',
    example: ['users:read', 'users:write'],
    type: [String]
  })
  permissions: string[];
}

/**
 * DTO cho phản hồi đăng nhập nhân viên
 */
export class EmployeeLoginResponseDto {
  @ApiProperty({
    description: 'Access token JWT',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  accessToken: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của access token (giây)',
    example: 3600,
    required: false
  })
  expiresIn?: number;

  @ApiProperty({
    description: 'Thời điểm hết hạn của access token (timestamp)',
    example: 1746968772000
  })
  expiresAt: number;

  @ApiProperty({
    description: 'Thông tin nhân viên đã đăng nhập',
    type: EmployeeInfoResponseDto
  })
  employee: EmployeeInfoResponseDto;
}
