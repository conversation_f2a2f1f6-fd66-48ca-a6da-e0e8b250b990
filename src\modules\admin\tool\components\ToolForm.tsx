import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Textarea, Select, FormItem, Card } from '@/shared/components/common';
import {
  ToolStatus,
  AccessType,
  ToolDetail,
  CreateToolParams,
  UpdateToolParams,
  ToolVersion,
  UpdateToolVersionParams,
} from '../types/tool.types';
import JsonEditor from './JsonEditor';
import ToolVersionForm from './ToolVersionForm';

interface ToolFormProps {
  onSubmit: (values: CreateToolParams | UpdateToolParams) => void;
  onSubmitVersion?: (versionId: string, values: UpdateToolVersionParams) => void;
  onCancel: () => void;
  isLoading?: boolean;
  readOnly?: boolean;
  initialValues?: ToolDetail;
  isEdit?: boolean;
}

/**
 * Form tạo/chỉnh sửa tool
 */
const ToolForm: React.FC<ToolFormProps> = ({
  onSubmit,
  onSubmitVersion,
  onCancel,
  isLoading = false,
  readOnly = false,
  initialValues,
  isEdit = false,
}) => {
  const { t } = useTranslation();
  const [name, setName] = useState(initialValues?.name || '');
  const [description, setDescription] = useState(initialValues?.description || '');
  const [status, setStatus] = useState<ToolStatus>(initialValues?.status || ToolStatus.DRAFT);
  const [accessType, setAccessType] = useState<AccessType>(
    initialValues?.accessType || AccessType.PUBLIC
  );
  const [toolName, setToolName] = useState(initialValues?.defaultVersion?.toolName || '');
  const [toolDescription, setToolDescription] = useState(
    initialValues?.defaultVersion?.toolDescription || ''
  );
  const [parameters, setParameters] = useState<Record<string, unknown>>(
    initialValues?.defaultVersion?.parameters || {}
  );

  const [selectedVersionForEdit, setSelectedVersionForEdit] = useState<ToolVersion | null>(null);
  const [showVersionForm, setShowVersionForm] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Sử dụng danh sách phiên bản từ initialValues
  const versions = React.useMemo(() => initialValues?.versions || [], [initialValues]);



  // Handlers cho version management
  const handleVersionClick = (e: React.MouseEvent, version: ToolVersion) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Version clicked:', version.versionNumber);
    setSelectedVersionForEdit(version);
    setShowVersionForm(true);
  };

  const handleVersionFormCancel = () => {
    console.log('Version form cancelled');
    setShowVersionForm(false);
    setSelectedVersionForEdit(null);
  };

  const handleVersionFormSubmit = (values: UpdateToolVersionParams) => {
    console.log('Version form submitted:', values);
    if (selectedVersionForEdit && onSubmitVersion) {
      onSubmitVersion(selectedVersionForEdit.id, values);
      // Không đóng form ngay lập tức, để user có thể tiếp tục edit
      // setShowVersionForm(false);
      // setSelectedVersionForEdit(null);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = t('admin.tool.validation.nameRequired', 'Tool name is required');
    }

    if (!isEdit && !toolName.trim()) {
      newErrors.toolName = t(
        'admin.tool.validation.toolNameRequired',
        'Tool display name is required'
      );
    }

    if (!isEdit && Object.keys(parameters).length === 0) {
      newErrors.parameters = t(
        'admin.tool.validation.parametersRequired',
        'Tool parameters are required'
      );
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (isEdit) {
      // Cập nhật tool
      const updateData: UpdateToolParams = {
        name,
        description: description || undefined,
        status,
        accessType,
      };
      onSubmit(updateData);
    } else {
      // Tạo tool mới
      const createData: CreateToolParams = {
        name,
        description: description || undefined,
        toolName,
        toolDescription: toolDescription || undefined,
        parameters,
        status,
        accessType,
      };
      onSubmit(createData);
    }
  };

  // Không cần handleParametersChange vì chúng ta đang sử dụng CodeBlock chỉ để hiển thị

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-4">
      <Typography variant="h6">
        {readOnly
          ? t('admin.tool.viewTool', 'View Tool')
          : isEdit
            ? t('admin.tool.editTool', 'Edit Tool')
            : t('admin.tool.createTool', 'Create New Tool')}
      </Typography>

      <div className="space-y-4">
        {/* Thông tin cơ bản */}
        <div className="space-y-4">
          <FormItem label={t('admin.tool.name', 'Tool Name')} helpText={errors.name} required>
            <Input
              value={name}
              onChange={e => {
                setName(e.target.value);
                if (errors.name) setErrors(prev => ({ ...prev, name: '' }));
              }}
              placeholder={t('admin.tool.namePlaceholder', 'Enter tool name')}
              disabled={readOnly || isLoading}
            />
          </FormItem>

          <FormItem label={t('admin.tool.description', 'Description')}>
            <Textarea
              value={description || ''}
              onChange={e => setDescription(e.target.value)}
              placeholder={t('admin.tool.descriptionPlaceholder', 'Enter tool description')}
              disabled={readOnly || isLoading}
              rows={3}
            />
          </FormItem>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('admin.tool.status', 'Status')}>
              <Select
                value={status}
                onChange={val => setStatus(val as ToolStatus)}
                options={[
                  { value: ToolStatus.DRAFT, label: t('admin.tool.status.draft', 'Draft') },
                  {
                    value: ToolStatus.APPROVED,
                    label: t('admin.tool.status.approved', 'Approved'),
                  },
                  {
                    value: ToolStatus.DEPRECATED,
                    label: t('admin.tool.status.deprecated', 'Deprecated'),
                  },
                ]}
                disabled={readOnly || isLoading}
              />
            </FormItem>

            <FormItem label={t('admin.tool.accessType', 'Access Type')}>
              <Select
                value={accessType}
                onChange={val => setAccessType(val as AccessType)}
                options={[
                  { value: AccessType.PUBLIC, label: t('admin.tool.access.public', 'Public') },
                  { value: AccessType.PRIVATE, label: t('admin.tool.access.private', 'Private') },
                  {
                    value: AccessType.RESTRICTED,
                    label: t('admin.tool.access.restricted', 'Restricted'),
                  },
                ]}
                disabled={readOnly || isLoading}
              />
            </FormItem>
          </div>
        </div>

        {/* Thông tin phiên bản - Hiển thị khi có initialValues (view hoặc edit) */}
        {initialValues && (
          <div className="space-y-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin.tool.versionManagement', 'Version Management')}
            </Typography>

            {/* Version Buttons - Hiển thị theo chiều ngang */}
            {versions && versions.length > 0 && (
              <div className="space-y-4">
                <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                  {t('admin.tool.selectVersionToEdit', 'Select a version to edit:')}
                </Typography>
                <div className="flex flex-wrap gap-2">
                  {versions.map((version: ToolVersion) => (
                    <Button
                      key={version.id}
                      type="button"
                      variant={selectedVersionForEdit?.id === version.id ? 'primary' : 'outline'}
                      size="sm"
                      onClick={(e) => handleVersionClick(e, version)}
                      disabled={readOnly || isLoading}
                      className="min-w-[100px]"
                    >
                      v{version.versionNumber}
                      <span className="ml-1 text-xs opacity-75">
                        ({version.status === ToolStatus.APPROVED ? '✓' :
                          version.status === ToolStatus.DRAFT ? '📝' : '🗃️'})
                      </span>
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Version Form - Hiển thị khi có version được chọn */}
            {(() => {
              console.log('showVersionForm:', showVersionForm, 'selectedVersionForEdit:', selectedVersionForEdit?.versionNumber);
              return showVersionForm && selectedVersionForEdit;
            })() && (
              <Card className="p-4 bg-gray-50 dark:bg-gray-800">
                <ToolVersionForm
                  initialValues={selectedVersionForEdit!}
                  toolId={initialValues.id}
                  onSubmit={handleVersionFormSubmit}
                  onCancel={handleVersionFormCancel}
                  isLoading={isLoading}
                  readOnly={readOnly}
                  isEdit={true}
                />
              </Card>
            )}

            {/* Initial Version Form - Hiển thị khi tạo mới */}
            {!isEdit && (
              <>
                <FormItem
                  label={t('admin.tool.toolName', 'Tool Display Name')}
                  helpText={errors.toolName}
                  required
                >
                  <Input
                    value={toolName}
                    onChange={e => {
                      setToolName(e.target.value);
                      if (errors.toolName) setErrors(prev => ({ ...prev, toolName: '' }));
                    }}
                    placeholder={t('admin.tool.toolNamePlaceholder', 'Enter tool display name')}
                    disabled={readOnly || isLoading}
                  />
                </FormItem>

                <FormItem label={t('admin.tool.toolDescription', 'Tool Display Description')}>
                  <Textarea
                    value={toolDescription || ''}
                    onChange={e => setToolDescription(e.target.value)}
                    placeholder={t(
                      'admin.tool.toolDescriptionPlaceholder',
                      'Enter tool display description'
                    )}
                    disabled={readOnly || isLoading}
                    rows={3}
                  />
                </FormItem>

                <FormItem
                  label={t('admin.tool.parameters', 'Parameters')}
                  helpText={errors.parameters}
                  required
                >
                  <JsonEditor
                    value={parameters}
                    onChange={parsedParams => {
                      setParameters(parsedParams);
                      if (errors.parameters) {
                        setErrors(prev => ({ ...prev, parameters: '' }));
                      }
                    }}
                    onError={errorMessage => {
                      if (errorMessage) {
                        setErrors(prev => ({
                          ...prev,
                          parameters: t('admin.tool.validation.invalidJson', 'Invalid JSON format'),
                        }));
                      }
                    }}
                    placeholder={t(
                      'admin.tool.parametersPlaceholder',
                      'Enter tool parameters in JSON format'
                    )}
                    disabled={readOnly || isLoading}
                    minHeight={250}
                    className="font-mono text-sm"
                  />
                </FormItem>
              </>
            )}
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button variant="outline" onClick={onCancel} disabled={isLoading}>
          {t('common.cancel', 'Cancel')}
        </Button>
        {!readOnly && (
          <Button type="submit" variant="primary" disabled={isLoading}>
            {isLoading
              ? isEdit
                ? t('admin.tool.updating', 'Updating...')
                : t('admin.tool.creating', 'Creating...')
              : isEdit
                ? t('admin.tool.update', 'Update')
                : t('admin.tool.create', 'Create')}
          </Button>
        )}
      </div>
    </form>
  );
};

export default ToolForm;
