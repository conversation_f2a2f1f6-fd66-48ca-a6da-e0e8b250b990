import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO para la respuesta de un template de email
 */
export class TemplateEmailResponseDto {
  @ApiProperty({
    description: 'ID del template',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID del usuario propietario',
    example: 123,
  })
  userId: number;

  @ApiProperty({
    description: 'Nombre del template',
    example: 'Plantilla de bienvenida',
  })
  name: string;

  @ApiProperty({
    description: 'Asunto del email',
    example: 'Bienvenido a nuestra plataforma',
  })
  subject: string;

  @ApiProperty({
    description: 'Contenido HTML del email',
    example: '<h1>Bienvenido</h1><p>Gracias por registrarte en nuestra plataforma.</p>',
  })
  content: string;

  @ApiProperty({
    description: 'Tags asociados al template',
    example: ['bienvenida', 'registro'],
  })
  tags: string[];

  @ApiProperty({
    description: 'Placeholders utilizados en el template',
    example: ['userName', 'companyName', 'date'],
    type: [String],
  })
  placeholders: string[];

  @ApiProperty({
    description: 'Fecha de creación (Unix timestamp)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Fecha de última actualización (Unix timestamp)',
    example: 1625097600000,
  })
  updatedAt: number;
}
