import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Card,
  Button,
  FormItem,
  Input,
  Toggle,
  Modal,
  Icon,
  Alert,
  Typography,
  Select
} from '@/shared/components/common';
import { RootState } from '@/shared/store';
import {
  addChatKeyword,
  removeChatKeyword,
  toggleChatKeyword,
  resetChatKeywords
} from '../store/settingsSlice';
import { ChatKeyword } from '../types';
import { userMenuItems } from '@/shared/components/layout/chat-panel/menu-items';

// Form schema for adding new keyword
const keywordSchema = z.object({
  keyword: z.string().min(1, 'Từ khóa không được để trống').max(50, 'Từ khóa không được quá 50 ký tự'),
  path: z.string().min(1, 'Đường dẫn không được để trống').regex(/^\//, 'Đường dẫn phải bắt đầu bằng /'),
  description: z.string().max(200, 'Mô tả không được quá 200 ký tự').optional(),
});

type KeywordFormData = z.infer<typeof keywordSchema>;

const ChatKeywordSettings: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { chatKeywords, customKeywords } = useSelector((state: RootState) => state.settings);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Tạo options cho Select từ userMenuItems
  const pathOptions = useMemo(() => {
    return userMenuItems.map(item => ({
      value: item.path,
      label: `${item.path} - ${t(item.label, item.label)}`,
    }));
  }, [t]);

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors },
  } = useForm<KeywordFormData>({
    resolver: zodResolver(keywordSchema),
  });

  // Combine default and custom keywords
  const allKeywords = [...chatKeywords, ...customKeywords];

  const handleAddKeyword = (data: KeywordFormData) => {
    const newKeyword: ChatKeyword = {
      id: `custom_${Date.now()}`,
      keyword: data.keyword,
      path: data.path,
      description: data.description,
      enabled: true,
    };

    dispatch(addChatKeyword(newKeyword));
    reset();
    setIsAddModalOpen(false);
  };

  const handleToggleKeyword = (id: string) => {
    dispatch(toggleChatKeyword(id));
  };

  const handleRemoveKeyword = (id: string) => {
    dispatch(removeChatKeyword(id));
  };

  const handleResetKeywords = () => {
    dispatch(resetChatKeywords());
  };

  return (
    <Card title={t('settings.chatKeywords.title', 'Cài đặt từ khóa ChatPanel')} className="mb-6">
      <div className="space-y-4">
        {/* Description */}
        <Alert
          type="info"
          title={t('settings.chatKeywords.howItWorks', 'Cách hoạt động')}
          message={t('settings.chatKeywords.description', 'Khi bạn nhập từ khóa trong ChatPanel, hệ thống sẽ tự động chuyển hướng đến trang tương ứng. Bạn có thể bật/tắt hoặc thêm từ khóa tùy chỉnh.')}
          showIcon={true}
        />

        {/* Add new keyword button */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
          <Typography variant="h6" weight="medium" color="default">
            {t('settings.chatKeywords.keywordList', 'Danh sách từ khóa')}
          </Typography>
          <Button
            variant="primary"
            onClick={() => setIsAddModalOpen(true)}
            leftIcon={<Icon name="plus" size="sm" />}
            className="w-full sm:w-auto"
          >
            {t('settings.chatKeywords.addKeyword', 'Thêm từ khóa')}
          </Button>
        </div>

        {/* Keywords list */}
        <div className="space-y-2">
          {allKeywords.map((keyword) => (
            <div
              key={keyword.id}
              className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 border border-border rounded-lg gap-3"
            >
              <div className="flex-1 min-w-0">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                  <Typography variant="code" className="px-2 py-1 bg-muted rounded break-all">
                    {keyword.keyword}
                  </Typography>
                  <Typography variant="body2" color="muted" className="hidden sm:inline">→</Typography>
                  <Typography variant="code" className="px-2 py-1 bg-muted rounded break-all">
                    {keyword.path}
                  </Typography>
                </div>
                {keyword.description && (
                  <Typography variant="body2" color="muted" className="mt-2">
                    {keyword.description}
                  </Typography>
                )}
              </div>

              <div className="flex items-center justify-end sm:justify-start space-x-2 flex-shrink-0">
                <Toggle
                  checked={keyword.enabled}
                  onChange={() => handleToggleKeyword(keyword.id)}
                />
                {keyword.id.startsWith('custom_') && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveKeyword(keyword.id)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Icon name="trash" size="sm" />
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Reset button */}
        <div className="flex justify-end pt-4 border-t border-border">
          <Button
            variant="outline"
            onClick={handleResetKeywords}
            className="text-sm"
          >
            {t('settings.chatKeywords.resetToDefault', 'Đặt lại mặc định')}
          </Button>
        </div>
      </div>

      {/* Add keyword modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title={t('settings.chatKeywords.addNewKeyword', 'Thêm từ khóa mới')}
        footer={
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsAddModalOpen(false)}
            >
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button
              type="submit"
              variant="primary"
              form="add-keyword-form"
            >
              {t('common.add', 'Thêm')}
            </Button>
          </div>
        }
      >
        <form id="add-keyword-form" onSubmit={handleSubmit(handleAddKeyword)} className="space-y-4">
          <FormItem
            label={t('settings.chatKeywords.keyword', 'Từ khóa')}
            required
          >
            <Input
              {...register('keyword')}
              placeholder={t('settings.chatKeywords.keywordPlaceholder', 'Ví dụ: trang chủ, home')}
              error={errors.keyword?.message}
              className="w-full"
            />
          </FormItem>

          <FormItem
            label={t('settings.chatKeywords.path', 'Đường dẫn')}
            required
          >
            <div>
              <Controller
                name="path"
                control={control}
                render={({ field }) => (
                  <Select
                    value={field.value}
                    onChange={field.onChange}
                    options={pathOptions}
                    placeholder={t('settings.chatKeywords.pathPlaceholder', 'Chọn đường dẫn...')}
                    className="w-full"
                  />
                )}
              />
              {errors.path && (
                <Typography variant="caption" color="error" className="mt-1">
                  {errors.path.message}
                </Typography>
              )}
            </div>
          </FormItem>

          <FormItem
            label={t('settings.chatKeywords.description', 'Mô tả')}
          >
            <Input
              {...register('description')}
              placeholder={t('settings.chatKeywords.descriptionPlaceholder', 'Mô tả ngắn gọn về từ khóa này')}
              error={errors.description?.message}
              className="w-full"
            />
          </FormItem>
        </form>
      </Modal>
    </Card>
  );
};

export default ChatKeywordSettings;
