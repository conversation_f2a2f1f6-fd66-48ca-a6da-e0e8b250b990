import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  Button,
  Card,
  EmptyState,
  Icon,
  Pagination,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Interface cho item Facebook Page
 */
interface FacebookPage {
  id: string;
  name: string;
  imageUrl?: string;
  category?: string;
  followers?: number;
  isConnected?: boolean;
}

/**
 * Props cho component FacebookSlideInForm
 */
interface FacebookSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi chọn các trang Facebook
   */
  onSelect: (selectedPages: FacebookPage[]) => void;

  /**
   * <PERSON><PERSON> sách <PERSON> của các trang đã chọn
   */
  selectedPageIds?: string[];
}

/**
 * Component form trượt để chọn các trang Facebook để tích hợp
 */
const FacebookSlideInForm: React.FC<FacebookSlideInFormProps> = ({
  isVisible,
  onClose,
  onSelect,
  selectedPageIds = [],
}) => {
  // State cho dữ liệu và UI
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');
  const [pages, setPages] = useState<FacebookPage[]>([]);
  const [selectedIds, setSelectedIds] = useState<string[]>(selectedPageIds);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // State cho phân trang
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);

  // State cho sắp xếp và lọc
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'ASC' | 'DESC'>('ASC');
  const [filterCategory, setFilterCategory] = useState<string>('');

  // Cấu hình cột cho bảng
  const columns: TableColumn<FacebookPage>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'page',
      title: 'Trang Facebook',
      dataIndex: 'name',
      width: '40%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.imageUrl ? (
            <img
              src={record.imageUrl}
              alt={record.name}
              className="w-10 h-10 rounded-full mr-3 object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
              <Icon name="facebook" size="md" className="text-blue-600" />
            </div>
          )}
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              ID: {record.id}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'category',
      title: 'Danh mục',
      dataIndex: 'category',
      width: '20%',
    },
    {
      key: 'followers',
      title: 'Người theo dõi',
      dataIndex: 'followers',
      width: '20%',
      render: (followers) => followers?.toLocaleString() || '0',
    },
    {
      key: 'status',
      title: 'Trạng thái',
      width: '20%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.isConnected ? (
            <span className="text-green-500 text-sm flex items-center">
              <Icon name="check-circle" size="sm" className="mr-1" />
              Đã kết nối
            </span>
          ) : (
            <span className="text-gray-500 text-sm flex items-center">
              <Icon name="circle" size="sm" className="mr-1" />
              Chưa kết nối
            </span>
          )}
        </div>
      ),
    },
  ];

  // Giả lập dữ liệu trang Facebook
  const mockFacebookPages: FacebookPage[] = useMemo(() => [
    { id: '4010684376243', name: 'Hiệu sách Thái Sơn', category: 'Cửa hàng sách', followers: 12500, isConnected: true, imageUrl: 'https://picsum.photos/200/200?random=1' },
    { id: '4010685437243', name: 'Chăm sóc da, phục hồi khỏi phục đồ gia', category: 'Làm đẹp', followers: 8700, isConnected: true, imageUrl: 'https://picsum.photos/200/200?random=2' },
    { id: '4010685437244', name: 'Test', category: 'Công nghệ', followers: 5600, isConnected: false, imageUrl: 'https://picsum.photos/200/200?random=3' },
    { id: '4010685437245', name: 'Thời trang Nam', category: 'Thời trang', followers: 15800, isConnected: false, imageUrl: 'https://picsum.photos/200/200?random=4' },
    { id: '4010685437246', name: 'Ẩm thực Việt', category: 'Ẩm thực', followers: 22300, isConnected: false, imageUrl: 'https://picsum.photos/200/200?random=5' },
    { id: '4010685437247', name: 'Du lịch Việt Nam', category: 'Du lịch', followers: 31200, isConnected: false, imageUrl: 'https://picsum.photos/200/200?random=6' },
    { id: '4010685437248', name: 'Công nghệ 4.0', category: 'Công nghệ', followers: 9800, isConnected: false, imageUrl: 'https://picsum.photos/200/200?random=7' },
    { id: '4010685437249', name: 'Sức khỏe và Đời sống', category: 'Sức khỏe', followers: 18500, isConnected: false, imageUrl: 'https://picsum.photos/200/200?random=8' },
    { id: '4010685437250', name: 'Giáo dục trực tuyến', category: 'Giáo dục', followers: 7600, isConnected: false, imageUrl: 'https://picsum.photos/200/200?random=9' },
    { id: '4010685437251', name: 'Nội thất hiện đại', category: 'Nội thất', followers: 11200, isConnected: false, imageUrl: 'https://picsum.photos/200/200?random=10' },
    { id: '4010685437252', name: 'Thể thao Việt', category: 'Thể thao', followers: 25600, isConnected: false, imageUrl: 'https://picsum.photos/200/200?random=11' },
    { id: '4010685437253', name: 'Âm nhạc trẻ', category: 'Âm nhạc', followers: 42300, isConnected: false, imageUrl: 'https://picsum.photos/200/200?random=12' },
  ], []);

  // Giả lập việc tải dữ liệu
  useEffect(() => {
    const fetchData = async () => {
      // setIsLoading(true);
      try {
        // Giả lập API call
        await new Promise(resolve => setTimeout(resolve, 500));

        // Lọc dữ liệu theo tìm kiếm và danh mục
        let filteredData = [...mockFacebookPages];

        if (search) {
          filteredData = filteredData.filter(page =>
            page.name.toLowerCase().includes(search.toLowerCase()) ||
            page.id.includes(search)
          );
        }

        if (filterCategory) {
          filteredData = filteredData.filter(page =>
            page.category?.toLowerCase() === filterCategory.toLowerCase()
          );
        }

        // Sắp xếp dữ liệu
        filteredData.sort((a, b) => {
          if (sortBy === 'name') {
            return sortDirection === 'ASC'
              ? a.name.localeCompare(b.name)
              : b.name.localeCompare(a.name);
          } else if (sortBy === 'followers') {
            const followersA = a.followers || 0;
            const followersB = b.followers || 0;
            return sortDirection === 'ASC'
              ? followersA - followersB
              : followersB - followersA;
          }
          return 0;
        });

        // Phân trang
        const startIndex = (currentPage - 1) * itemsPerPage;
        const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage);

        setPages(paginatedData);
        setTotalItems(filteredData.length);
      } catch (error) {
        console.error('Error fetching Facebook pages:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [search, currentPage, itemsPerPage, sortBy, sortDirection, filterCategory, mockFacebookPages]);

  // Kiểm tra có thay đổi chưa lưu không
  useEffect(() => {
    const hasUnsavedChanges =
      selectedIds.length !== selectedPageIds.length ||
      selectedIds.some(id => !selectedPageIds.includes(id)) ||
      selectedPageIds.some(id => !selectedIds.includes(id));

    setHasChanges(hasUnsavedChanges);
  }, [selectedIds, selectedPageIds]);

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setSearch(term);
    setCurrentPage(1);
  };

  // Xử lý chọn tất cả
  // const handleSelectAll = () => {
  //   if (selectedIds.length === pages.length) {
  //     setSelectedIds([]);
  //   } else {
  //     setSelectedIds(pages.map(page => page.id));
  //   }
  // };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = (column: string, direction: 'ASC' | 'DESC') => {
    setSortBy(column);
    setSortDirection(direction);
  };

  // Xử lý lưu
  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      // Giả lập API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Lấy thông tin đầy đủ của các trang đã chọn
      const selectedPages = mockFacebookPages.filter(page =>
        selectedIds.includes(page.id)
      );

      onSelect(selectedPages);
      onClose();
    } catch (error) {
      console.error('Error saving selected Facebook pages:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    if (hasChanges) {
      const confirmed = window.confirm(
        'Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn đóng form?'
      );
      if (!confirmed) return;
    }

    setSearch('');
    onClose();
  }, [hasChanges, onClose]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'sort',
      label: 'Sắp xếp theo',
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-name',
      label: 'Tên',
      onClick: () => handleSortChange('name', sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-followers',
      label: 'Người theo dõi',
      onClick: () => handleSortChange('followers', sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'divider',
      divider: true,
    },
    {
      id: 'filter',
      label: 'Lọc theo',
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'filter-all',
      label: 'Tất cả',
      onClick: () => setFilterCategory(''),
    },
    {
      id: 'filter-connected',
      label: 'Đã kết nối',
      onClick: () => { },
    },
    {
      id: 'filter-not-connected',
      label: 'Chưa kết nối',
      onClick: () => { },
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-6xl">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">Chọn trang Facebook</Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            Đóng
          </Button>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Bảng dữ liệu */}
        <div className="mb-4 w-full overflow-x-auto">
          <Table
            data={pages}
            columns={columns}
            loading={isLoading}
            rowKey="id"
            size="lg"
            hoverable
            bordered={false}
            selectable={true}
            className="w-full table-fixed"
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
          />

          {/* Hiển thị khi không có dữ liệu */}
          {!isLoading && pages.length === 0 && (
            <EmptyState
              icon="search"
              title="Không có kết quả"
              description="Không tìm thấy trang Facebook nào phù hợp với tìm kiếm của bạn."
              className="py-8"
            />
          )}
        </div>

        {/* Phân trang */}
        <div className="mb-4">
          <Pagination
            currentPage={currentPage}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPageOptions={[5, 10, 20, 50]}
            showItemsPerPageSelector={true}
            showPageInfo={true}
          />
        </div>

        {/* Nút lưu */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            className="mr-2"
            disabled={isSubmitting}
          >
            Hủy
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            isLoading={isSubmitting}
            disabled={isLoading || !hasChanges}
          >
            Lưu
          </Button>
        </div>
      </Card>
    </SlideInForm>
  );
};

export default FacebookSlideInForm;
