/**
 * Provider Model Types
 */

import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';

export interface ProviderModel {
  id: string;
  name: string;
  type: TypeProviderEnum;
  createdAt: number;
  createdBy: {
    id: number;
    name: string;
    avatar: string | null;
  };
  updatedAt: number;
  updatedBy?: {
    id: number;
    name: string;
    avatar: string | null;
  };
}

export interface ProviderModelListItem {
  id: string;
  name: string;
  type: TypeProviderEnum;
  createdAt: number;
}

export interface CreateProviderModelDto {
  name: string;
  type: TypeProviderEnum;
  apiKey: string;
}

export interface UpdateProviderModelDto {
  name?: string;
  apiKey?: string;
}

export interface ProviderModelQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: TypeProviderEnum;
}

export interface ProviderModelFormData extends Omit<CreateProviderModelDto, 'type'> {
  type: TypeProviderEnum;
}

// Utility functions
export const getProviderDisplayName = (provider: TypeProviderEnum): string => {
  const providerNames: Record<TypeProviderEnum, string> = {
    [TypeProviderEnum.OPENAI]: 'OpenAI',
    [TypeProviderEnum.ANTHROPIC]: 'Anthropic',
    [TypeProviderEnum.GOOGLE]: 'Google',
    [TypeProviderEnum.META]: 'Meta',
    [TypeProviderEnum.DEEPSEEK]: 'DeepSeek',
    [TypeProviderEnum.XAI]: 'XAI',
  };

  return providerNames[provider] || provider;
};

export const getProviderIcon = (provider: TypeProviderEnum): string => {
  const providerIcons: Record<TypeProviderEnum, string> = {
    [TypeProviderEnum.OPENAI]: 'openai',
    [TypeProviderEnum.ANTHROPIC]: 'anthropic',
    [TypeProviderEnum.GOOGLE]: 'google',
    [TypeProviderEnum.META]: 'meta',
    [TypeProviderEnum.DEEPSEEK]: 'robot',
    [TypeProviderEnum.XAI]: 'robot',
  };

  return providerIcons[provider] || 'robot';
};

export const getProviderOptions = () => {
  return Object.values(TypeProviderEnum).map(type => ({
    value: type,
    label: getProviderDisplayName(type),
    icon: getProviderIcon(type),
  }));
};
