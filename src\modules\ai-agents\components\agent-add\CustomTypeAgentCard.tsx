import React from 'react';
import { Card, IconCard } from '@/shared/components/common';

export interface CustomAgentCardProps {
  isSelected?: boolean;
  onClick?: () => void;
}

/**
 * Component hiển thị card để tạo Custom Agent
 */
const CustomTypeAgentCard: React.FC<CustomAgentCardProps> = ({ isSelected = false, onClick }) => {
  return (
    <Card
      className={`h-full overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer border-dashed border-2 ${
        isSelected 
          ? 'ring-2 ring-primary ring-offset-2 dark:ring-offset-gray-800 bg-primary-50 dark:bg-primary-900/20 border-primary' 
          : 'border-gray-300 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-700'
      }`}
      variant="elevated"
      onClick={onClick}
    >
      <div className="p-4">
        <div className="flex flex-col space-y-4 items-center justify-center text-center h-full">
          {/* Icon */}
          <div className="relative w-12 h-12 flex-shrink-0">
            <div className="w-full h-full relative">
              <div className="absolute inset-0 flex items-center justify-center z-0 bg-primary-100 dark:bg-primary-900 rounded-full">
                <IconCard
                  icon="plus"
                  variant="primary"
                  size="md"
                  className="text-primary-500"
                />
              </div>
            </div>
          </div>

          {/* Tên */}
          <div className="min-w-0">
            <h3 className="font-semibold text-gray-900 dark:text-white">
              Custom Type Agent
            </h3>
          </div>

          {/* Mô tả */}
          <div className="text-sm text-gray-600 dark:text-gray-300">
            Tạo loại agent tùy chỉnh theo nhu cầu của bạn
          </div>
        </div>
      </div>
    </Card>
  );
};

export default CustomTypeAgentCard;
