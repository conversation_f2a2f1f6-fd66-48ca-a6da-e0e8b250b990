import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeleteResult, FindManyOptions, FindOneOptions, FindOptionsWhere, Repository } from 'typeorm';
import { AdminCampaignHistory } from '@modules/marketing/admin/entities';

/**
 * Repository cho AdminCampaignHistory
 */
@Injectable()
export class AdminCampaignHistoryRepository {
  constructor(
    @InjectRepository(AdminCampaignHistory)
    private readonly repository: Repository<AdminCampaignHistory>,
  ) {}

  /**
   * Tìm kiếm nhiều lịch sử campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách lịch sử campaign
   */
  async find(options?: FindManyOptions<AdminCampaignHistory>): Promise<AdminCampaignHistory[]> {
    return this.repository.find(options);
  }

  /**
   * Tì<PERSON> kiếm một lịch sử campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Lịch sử campaign hoặc null
   */
  async findOne(options?: FindOneOptions<AdminCampaignHistory>): Promise<AdminCampaignHistory | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Lưu lịch sử campaign
   * @param history Lịch sử campaign cần lưu
   * @returns Lịch sử campaign đã lưu
   */
  async save(history: AdminCampaignHistory): Promise<AdminCampaignHistory>;
  async save(history: AdminCampaignHistory[]): Promise<AdminCampaignHistory[]>;
  async save(history: AdminCampaignHistory | AdminCampaignHistory[]): Promise<AdminCampaignHistory | AdminCampaignHistory[]> {
    return this.repository.save(history as any);
  }

  /**
   * Xóa lịch sử campaign
   * @param criteria Điều kiện xóa
   * @returns Kết quả xóa
   */
  async delete(criteria: string | number | string[] | number[] | FindOptionsWhere<AdminCampaignHistory>): Promise<DeleteResult> {
    return this.repository.delete(criteria);
  }

  /**
   * Xóa lịch sử campaign
   * @param history Lịch sử campaign cần xóa
   * @returns Lịch sử campaign đã xóa
   */
  async remove(history: AdminCampaignHistory): Promise<AdminCampaignHistory>;
  async remove(history: AdminCampaignHistory[]): Promise<AdminCampaignHistory[]>;
  async remove(history: AdminCampaignHistory | AdminCampaignHistory[]): Promise<AdminCampaignHistory | AdminCampaignHistory[]> {
    return this.repository.remove(history as any);
  }

  /**
   * Đếm số lượng lịch sử campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng lịch sử campaign
   */
  async count(options?: FindManyOptions<AdminCampaignHistory>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }
}
