import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  MessageCircle,
  Mail,
  Users,
  TrendingUp,
  ArrowRight,
  BarChart3,
  Target,
  Tag
} from 'lucide-react';
import { Card } from '@/shared/components/common';
import { Button } from '@/shared/components/common';
import { MarketingViewHeader } from '../components/common/MarketingViewHeader';

/**
 * Trang Dashboard tổng quan Marketing
 */
export function MarketingDashboardPage() {
  const { t } = useTranslation('marketing');
  const navigate = useNavigate();

  // Mock data cho demo
  const stats = {
    totalAudience: 12450,
    totalSegments: 24,
    activeCampaigns: 8,
    totalTags: 156,
  };

  const channels = [
    {
      id: 'zalo',
      name: 'Zalo Marketing',
      description: 'Quản lý Zalo OA và ZNS campaigns',
      icon: MessageCircle,
      color: 'from-blue-500 to-blue-600',
      stats: {
        accounts: 3,
        followers: 8420,
        messagesSent: 1234,
      },
      path: '/marketing/zalo/overview',
    },
    {
      id: 'email',
      name: 'Email Marketing',
      description: 'Tạo và quản lý email campaigns',
      icon: Mail,
      color: 'from-orange-500 to-red-600',
      stats: {
        templates: 15,
        campaigns: 24,
        emailsSent: 15420,
      },
      path: '/marketing/email/overview',
    },
  ];

  const quickActions = [
    {
      title: 'Quản lý Audience',
      description: 'Tạo và phân đoạn khách hàng',
      icon: Users,
      path: '/marketing/audience',
      color: 'text-blue-600',
    },
    {
      title: 'Quản lý Segments',
      description: 'Tạo nhóm khách hàng theo tiêu chí',
      icon: Target,
      path: '/marketing/segments',
      color: 'text-green-600',
    },
    {
      title: 'Quản lý Tags',
      description: 'Gắn nhãn và phân loại khách hàng',
      icon: Tag,
      path: '/marketing/tags',
      color: 'text-purple-600',
    },
    {
      title: 'Báo cáo Analytics',
      description: 'Xem báo cáo hiệu quả marketing',
      icon: BarChart3,
      path: '/marketing/reports',
      color: 'text-orange-600',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <MarketingViewHeader
        title={t('dashboard.title', 'Marketing Dashboard')}
        description={t('dashboard.description', 'Tổng quan và quản lý tất cả hoạt động marketing')}
        actions={
          <Button onClick={() => navigate('/marketing/campaigns/create')} className="gap-2">
            <TrendingUp className="h-4 w-4" />
            {t('dashboard.createCampaign', 'Tạo Chiến dịch')}
          </Button>
        }
      />

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Tổng Audience</span>
              <Users className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">{stats.totalAudience.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">+245 tuần này</p>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Segments</span>
              <Target className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">{stats.totalSegments}</div>
          <p className="text-xs text-muted-foreground">+3 segment mới</p>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Chiến dịch đang chạy</span>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">{stats.activeCampaigns}</div>
          <p className="text-xs text-muted-foreground">Trên 3 kênh</p>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Tags</span>
              <Tag className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">{stats.totalTags}</div>
          <p className="text-xs text-muted-foreground">+12 tag mới</p>
        </Card>
      </div>

      {/* Marketing Channels */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Kênh Marketing</h2>
        <div className="grid gap-4 md:grid-cols-2">
          {channels.map((channel) => {
            const IconComponent = channel.icon;
            return (
              <Card
                key={channel.id}
                className="cursor-pointer hover:shadow-md transition-all duration-200"
                onClick={() => navigate(channel.path)}
                hoverable
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`h-12 w-12 rounded-lg bg-gradient-to-r ${channel.color} flex items-center justify-center text-white`}>
                      <IconComponent className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="font-semibold">{channel.name}</h3>
                      <p className="text-sm text-muted-foreground">{channel.description}</p>
                    </div>
                  </div>
                  <ArrowRight className="h-5 w-5 text-muted-foreground" />
                </div>

                <div className="mt-4 grid grid-cols-3 gap-4">
                  {Object.entries(channel.stats).map(([key, value]) => (
                    <div key={key} className="text-center">
                      <div className="text-lg font-semibold">{typeof value === 'number' ? value.toLocaleString() : value}</div>
                      <div className="text-xs text-muted-foreground capitalize">{key}</div>
                    </div>
                  ))}
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Thao tác nhanh</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {quickActions.map((action) => {
            const IconComponent = action.icon;
            return (
              <Card
                key={action.title}
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => navigate(action.path)}
                hoverable
              >
                <div className="flex items-center space-x-3">
                  <IconComponent className={`h-8 w-8 ${action.color}`} />
                  <div>
                    <h4 className="font-medium">{action.title}</h4>
                    <p className="text-xs text-muted-foreground">{action.description}</p>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Recent Activity */}
      <Card
        title="Hoạt động gần đây"
        subtitle="Các thay đổi và cập nhật mới nhất"
      >
        <div className="space-y-4">
          {[
            {
              action: 'Tạo segment mới',
              description: 'Segment "Khách hàng VIP" với 245 thành viên',
              time: '2 giờ trước',
              type: 'segment',
            },
            {
              action: 'Gửi email campaign',
              description: 'Campaign "Newsletter tháng 1" đã gửi đến 1,250 người',
              time: '4 giờ trước',
              type: 'email',
            },
            {
              action: 'Kết nối Zalo OA',
              description: 'Đã kết nối thành công OA "Shop ABC"',
              time: '1 ngày trước',
              type: 'zalo',
            },
            {
              action: 'Thêm tags mới',
              description: 'Đã thêm 12 tags cho phân loại khách hàng',
              time: '2 ngày trước',
              type: 'tag',
            },
          ].map((activity, index) => (
            <div key={index} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-muted/50 transition-colors">
              <div className="h-2 w-2 rounded-full bg-blue-500" />
              <div className="flex-1">
                <h4 className="font-medium">{activity.action}</h4>
                <p className="text-sm text-muted-foreground">{activity.description}</p>
              </div>
              <div className="text-xs text-muted-foreground">{activity.time}</div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}

export default MarketingDashboardPage;
