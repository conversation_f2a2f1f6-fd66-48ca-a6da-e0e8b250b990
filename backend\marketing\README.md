# Module Marketing

## Tổng quan

Module Marketing cung cấp các chức năng quản lý tiếp thị và quảng cáo trong hệ thống. Module này cho phép người dùng tạo và quản lý chiến dịch tiếp thị, phân đoạn khách hàng, quản lý tag và gửi email marketing.

## Cấu trúc module

```
marketing/
├── admin/                  # Chức năng quản lý dành cho admin
│   ├── controllers/        # Controllers xử lý request từ admin
│   ├── services/           # Services xử lý logic nghiệp vụ cho admin
│   └── entities/           # Entities dành riêng cho admin
├── user/                   # Chức năng dành cho người dùng
│   ├── controllers/        # Controllers xử lý request từ người dùng
│   ├── services/           # Services xử lý logic nghiệp vụ cho người dùng
│   ├── entities/           # Entities dành riêng cho người dùng
│   └── repositories/       # Repositories tương tác với database
└── marketing.module.ts     # Module definition
```

## Các entity chính

1. **UserTag**: Tag để phân loại khách hàng
2. **UserSegment**: Phân đoạn khách hàng
3. **UserAudience**: Đối tượng khách hàng
4. **UserAudienceCustomField**: Trường tùy chỉnh cho đối tượng khách hàng
5. **UserCampaign**: Chiến dịch tiếp thị
6. **UserCampaignHistory**: Lịch sử chiến dịch
7. **UserTemplateEmail**: Mẫu email

## Chức năng chính

### Quản lý tag
- Tạo, cập nhật, xóa tag
- Gắn tag cho khách hàng
- Phân loại khách hàng theo tag

### Quản lý phân đoạn
- Tạo, cập nhật, xóa phân đoạn
- Định nghĩa điều kiện phân đoạn
- Phân tích phân đoạn

### Quản lý đối tượng khách hàng
- Tạo, cập nhật, xóa đối tượng khách hàng
- Nhập/xuất danh sách khách hàng
- Quản lý trường tùy chỉnh

### Quản lý chiến dịch
- Tạo, cập nhật, xóa chiến dịch
- Lên lịch gửi chiến dịch
- Theo dõi hiệu suất chiến dịch

### Quản lý mẫu email
- Tạo, cập nhật, xóa mẫu email
- Thiết kế email với trình soạn thảo
- Sử dụng biến động trong mẫu

## API Endpoints

### User Endpoints

- `GET /marketing/tags` - Lấy danh sách tag
- `POST /marketing/tags` - Tạo tag mới
- `GET /marketing/segments` - Lấy danh sách phân đoạn
- `POST /marketing/segments` - Tạo phân đoạn mới
- `GET /marketing/audiences` - Lấy danh sách đối tượng khách hàng
- `POST /marketing/audiences` - Tạo đối tượng khách hàng mới
- `GET /marketing/campaigns` - Lấy danh sách chiến dịch
- `POST /marketing/campaigns` - Tạo chiến dịch mới
- `GET /marketing/templates` - Lấy danh sách mẫu email
- `POST /marketing/templates` - Tạo mẫu email mới

### Admin Endpoints

- `GET /admin/marketing/tags` - Lấy danh sách tag (admin)
- `GET /admin/marketing/segments` - Lấy danh sách phân đoạn (admin)
- `GET /admin/marketing/audiences` - Lấy danh sách đối tượng khách hàng (admin)
- `GET /admin/marketing/campaigns` - Lấy danh sách chiến dịch (admin)
- `GET /admin/marketing/templates` - Lấy danh sách mẫu email (admin)

## Cách sử dụng

### Tạo tag

```typescript
// Tạo tag mới
const tag = await userTagService.create({
  name: 'VIP Customer',
  color: '#FF5733',
  description: 'Customers who spend more than $1000'
});
```

### Tạo phân đoạn

```typescript
// Tạo phân đoạn mới
const segment = await userSegmentService.create({
  name: 'High Value Customers',
  description: 'Customers with high lifetime value',
  conditions: [
    { field: 'total_spent', operator: '>', value: 1000 },
    { field: 'purchase_count', operator: '>', value: 5 }
  ]
});
```

### Tạo chiến dịch

```typescript
// Tạo chiến dịch mới
const campaign = await userCampaignService.create({
  name: 'Summer Sale 2023',
  subject: 'Exclusive Summer Deals Just For You!',
  segmentId: 1,
  templateId: 2,
  scheduledAt: new Date('2023-06-15T10:00:00Z')
});
```

## Liên kết với các module khác

- **User Module**: Quản lý thông tin người dùng
- **Auth Module**: Xác thực và phân quyền
- **Email Module**: Gửi email marketing
- **Integration Module**: Tích hợp với các dịch vụ bên ngoài
