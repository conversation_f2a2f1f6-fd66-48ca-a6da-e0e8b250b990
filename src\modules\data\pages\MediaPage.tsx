import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  Tooltip,
  IconCard,
  ConfirmDeleteModal,
  Loading,
} from '@/shared/components/common';
import ImageGallery from '@/shared/components/common/ImageGallery/ImageGallery';
import { GalleryImage } from '@/shared/components/common/ImageGallery/types';
import { CreateMediaFormValues } from '@/modules/data/components/forms/FileForm';

import { useMediaList, useDeleteMedia } from '@/modules/data/media/hooks/useMediaQuery';
import { MediaDto, MediaStatusEnum, MediaQueryDto } from '@/modules/data/media/types/media.types';
import { SortDirection } from '@/shared/dto/request/query.dto';

// Component hiển thị ảnh thu nhỏ
const MediaThumbnail: React.FC<{ media: MediaDto; onClick: (media: MediaDto) => void }> = ({
  media,
  onClick,
}) => {
  const [hasError, setHasError] = useState(false);

  return (
    <div
      className="w-16 h-16 rounded overflow-hidden cursor-pointer flex items-center justify-center bg-gray-100 dark:bg-gray-800"
      onClick={() => onClick(media)}
    >
      {media.viewUrl ? (
        <>
          <img
            src={media.viewUrl}
            alt={media.name}
            className="w-full h-full object-cover"
            onError={() => {
              setHasError(true);
            }}
          />
          {hasError && <IconCard icon="file-image" size="lg" variant="default" />}
        </>
      ) : (
        <IconCard icon="file-image" size="lg" variant="default" />
      )}
    </div>
  );
};

import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { FileForm } from '@/modules/data/components/forms';
import useMediaUpload from '@/modules/data/hooks/useMediaUpload';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang hiển thị danh sách file
 */
const MediaPage: React.FC = () => {
  const { t } = useTranslation();
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  // State cho xác nhận xóa
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<MediaDto | null>(null);

  // State cho chọn nhiều và xóa nhiều
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // State cho hiển thị ảnh/video
  const [showImageGallery, setShowImageGallery] = useState(false);
  const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([]);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // State cho loading ảnh trong gallery
  const [isLoadingGallery, setIsLoadingGallery] = useState(false);

  // Xử lý hiển thị ảnh/video
  const handleShowMedia = useCallback(
    async (media: MediaDto) => {
      if (!media.viewUrl) return;

      try {
        setIsLoadingGallery(true);

        // Tạo đối tượng GalleryImage từ MediaDto
        const galleryImage: GalleryImage = {
          src: media.viewUrl,
          alt: media.name || 'Media',
          caption: media.description || media.name,
          thumbnail: media.viewUrl,
        };

        // Chỉ hiển thị ảnh hiện tại
        setGalleryImages([galleryImage]);
        setShowImageGallery(true);

        // Đảm bảo ảnh đã được tải trước khi hiển thị
        const img = new Image();
        img.src = media.viewUrl;

        img.onload = () => {
          setIsLoadingGallery(false);
        };

        img.onerror = () => {
          setIsLoadingGallery(false);
          NotificationUtil.error({
            message: t('data.files.imageLoadError', 'Không thể tải ảnh'),
            duration: 3000,
          });
        };
      } catch {
        setIsLoadingGallery(false);
        NotificationUtil.error({
          message: t('data.files.imageLoadError', 'Không thể tải ảnh'),
          duration: 3000,
        });
      }
    },
    [t]
  );

  // Định nghĩa columns cho bảng
  const columns = useMemo(
    () => [
      {
        key: 'thumbnail',
        title: t('data.files.table.thumbnail', 'Dữ liệu'),
        width: '80px',
        render: (_: unknown, record: MediaDto) =>
          record.viewUrl ? <MediaThumbnail media={record} onClick={handleShowMedia} /> : null,
      },
      {
        key: 'name',
        title: t('data.files.table.name', 'Tên'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown) => (
          <div className="flex items-center">
            <span>{String(value || '')}</span>
          </div>
        ),
      },
      {
        key: 'description',
        title: t('data.files.table.description', 'Mô tả'),
        dataIndex: 'description',
        sortable: true,
      },
      {
        key: 'viewUrl',
        title: t('data.files.table.viewUrl', 'URL'),
        render: (_: unknown, record: MediaDto) => (
          <div className="flex items-center space-x-2">
            <Tooltip content={t('common.copy', 'Sao chép')}>
              <IconCard
                icon="copy"
                variant="default"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(record.viewUrl || '');
                }}
              />
            </Tooltip>
          </div>
        ),
      },
      {
        key: 'status',
        title: t('data.files.table.status', 'Trạng thái'),
        dataIndex: 'status',
        sortable: true,
        render: (_: unknown, record: MediaDto) => (
          <div className="flex items-center">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                record.status === MediaStatusEnum.APPROVED
                  ? 'bg-green-100 text-green-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}
            >
              {record.status === MediaStatusEnum.APPROVED
                ? t('common.status.active', 'Hoạt động')
                : t('common.status.pending', 'Chờ duyệt')}
            </span>
          </div>
        ),
      },
    ],
    [t, handleShowMedia]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common.all', 'Tất cả'), icon: 'list', value: 'all' },
      {
        id: 'active',
        label: t('common.status.active', 'Hoạt động'),
        icon: 'check',
        value: MediaStatusEnum.APPROVED,
      },
      {
        id: 'pending',
        label: t('common.status.pending', 'Chờ duyệt'),
        icon: 'eye-off',
        value: MediaStatusEnum.PENDING,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): MediaQueryDto => {
      const queryParams: MediaQueryDto = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as MediaStatusEnum;
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<MediaDto, MediaQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Hooks để gọi API
  const { data: mediaData, isLoading: isLoadingMedia } = useMediaList(dataTable.queryParams);

  // Sử dụng hook để upload media và xóa media
  const { uploadMedia } = useMediaUpload();
  const { mutateAsync: deleteMedia } = useDeleteMedia();

  // Lưu trữ tham chiếu đến hàm updateTableData
  const updateTableDataRef = React.useRef(dataTable.updateTableData);

  // Cập nhật tham chiếu khi dataTable thay đổi
  useEffect(() => {
    updateTableDataRef.current = dataTable.updateTableData;
  }, [dataTable]);

  // Cập nhật dữ liệu bảng khi có dữ liệu từ API
  useEffect(() => {
    if (mediaData) {
      // Sử dụng tham chiếu để tránh vòng lặp vô hạn
      updateTableDataRef.current(mediaData, isLoadingMedia);
    }
  }, [mediaData, isLoadingMedia]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        [MediaStatusEnum.APPROVED]: t('common.status.active', 'Hoạt động'),
        [MediaStatusEnum.PENDING]: t('common.status.pending', 'Chờ duyệt'),
      },
      t,
    });

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setFileToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!fileToDelete) return;

    try {
      // Gọi API xóa file
      await deleteMedia(fileToDelete.id);

      // Đóng popup
      setShowDeleteConfirm(false);
      setFileToDelete(null);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('data.files.deleteSuccess', 'Đã xóa file thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting file:', error);
      NotificationUtil.error({
        message: t('data.files.deleteError', 'Có lỗi xảy ra khi xóa file'),
        duration: 3000,
      });
    }
  }, [fileToDelete, deleteMedia, t]);

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    // Kiểm tra cả số lượng mục đã chọn và dữ liệu có tồn tại không
    if (selectedRowKeys.length === 0 || (mediaData?.items?.length ?? 0) === 0) {
      NotificationUtil.warning({
        message: t('data.files.selectFilesToDelete', 'Vui lòng chọn ít nhất một file để xóa'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, mediaData?.items?.length, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Lưu số lượng file đã chọn trước khi xóa để hiển thị thông báo
      const deletedCount = selectedRowKeys.length;

      // Gọi API xóa nhiều file cùng lúc
      // API yêu cầu truyền một mảng các ID dưới dạng body
      await deleteMedia(selectedRowKeys as string[]);

      // Đảm bảo đặt lại selectedRowKeys thành mảng rỗng trước khi đóng modal
      setSelectedRowKeys([]);
      setShowBulkDeleteConfirm(false);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t(
          'data.files.bulkDeleteSuccess',
          `Đã xóa ${deletedCount} file thành công`
        ),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting files:', error);
      NotificationUtil.error({
        message: t('data.files.bulkDeleteError', 'Có lỗi xảy ra khi xóa file'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteMedia, t]);

  // Xử lý submit form tạo media mới
  const handleSubmitCreateMedia = async (values: CreateMediaFormValues) => {
    try {
      setIsUploading(true);
      setUploadProgress(10);

      console.log('Form values:', values);

      // Chuẩn bị dữ liệu theo định dạng yêu cầu của API
      const mediaList = values.files.map(fileData => ({
        name: fileData.name,
        description: fileData.description,
        size: fileData.file.size,
        tags: fileData.tags ? fileData.tags.split(',').map((tag: string) => tag.trim()) : [],
        type: fileData.file.type,
        viewUrl: 'test/test-' + Date.now() + '-' + Math.random().toString(36).substring(2, 8),
        file: fileData.file, // Thêm file vào đây để sử dụng trong uploadMedia
      }));

      // Upload media với TaskQueue
      await uploadMedia(mediaList);

      // Đóng form sau 1.5s
      setTimeout(() => {
        hideForm();
        setIsUploading(false);
      }, 1500);
    } catch (err) {
      console.error('Upload error:', err);
      NotificationUtil.error({
        message: 'Có lỗi xảy ra khi tải lên media. Vui lòng thử lại sau.',
        duration: 5000,
      });
      setIsUploading(false);
    }
  };

  return (
    <div>
      <div className="space-y-4">
        <div>
          {/* Thêm MenuIconBar */}
          <MenuIconBar
            onSearch={dataTable.tableData.handleSearch}
            onAdd={() => showForm()}
            items={dataTable.menuItems}
            onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
            columns={dataTable.columnVisibility.visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
            additionalIcons={[
              {
                icon: 'trash',
                tooltip: t('common.bulkDelete', 'Xóa nhiều'),
                variant: 'primary',
                onClick: () => {
                  // Kiểm tra lại số lượng mục đã chọn và dữ liệu có tồn tại không
                  if (selectedRowKeys.length > 0 && (mediaData?.items?.length ?? 0) > 0) {
                    handleShowBulkDeleteConfirm();
                  } else {
                    NotificationUtil.info({
                      message: t('data.files.selectFilesToDelete', 'Vui lòng chọn ít nhất một file để xóa'),
                      duration: 3000,
                    });
                  }
                },
                className: 'text-red-500',
                // Chỉ hiển thị khi có dữ liệu và có mục được chọn
                condition: selectedRowKeys.length > 0 && (mediaData?.items?.length ?? 0) > 0,
              }
            ]}
          />
        </div>

        {/* Hiển thị ActiveFilters */}
        <ActiveFilters
          searchTerm={dataTable.tableData.searchTerm}
          onClearSearch={handleClearSearch}
          filterValue={dataTable.filter.selectedValue}
          filterLabel={getFilterLabel()}
          onClearFilter={handleClearFilter}
          sortBy={dataTable.tableData.sortBy}
          sortDirection={dataTable.tableData.sortDirection}
          onClearSort={handleClearSort}
          onClearAll={handleClearAll}
        />

        {/* SlideInForm cho form thêm mới */}
        <SlideInForm isVisible={isVisible}>
          <FileForm
            onSubmit={handleSubmitCreateMedia}
            onCancel={hideForm}
            isUploading={isUploading}
            uploadProgress={uploadProgress}
          />
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<MediaDto>
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={mediaData?.items || []}
            rowKey="id"
            loading={isLoadingMedia}
            sortable={true}
            selectable={true}
            rowSelection={{
              selectedRowKeys,
              onChange: keys => setSelectedRowKeys(keys),
            }}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={{
              current: mediaData?.meta.currentPage || 1,
              pageSize: dataTable.tableData.pageSize,
              total: mediaData?.meta.totalItems || 0,
              onChange: dataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        message={t('data.files.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa tài liệu này?')}
        itemName={fileToDelete?.name}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        message={
          selectedRowKeys.length > 0
            ? t(
                'data.files.confirmBulkDeleteMessage',
                `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} file đã chọn?`
              )
            : t('data.files.noFilesSelected', 'Không có file nào được chọn')
        }
      />

      {/* ImageGallery cho hiển thị ảnh/video */}
      {showImageGallery && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80"
          onClick={() => setShowImageGallery(false)}
        >
          <div
            className="relative w-full h-full max-w-6xl max-h-[90vh] p-4"
            onClick={e => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 z-10 bg-gray-800 bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-70"
              onClick={() => setShowImageGallery(false)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>

            {isLoadingGallery ? (
              <div className="flex items-center justify-center h-full">
                <Loading size="lg" />
              </div>
            ) : galleryImages.length > 0 ? (
              <ImageGallery
                images={galleryImages}
                layout="carousel"
                lightbox={true}
                thumbnails={true}
                thumbnailsConfig={{
                  position: 'bottom',
                  size: 60,
                  gap: 8,
                  visibleItems: 10,
                  autoScroll: true,
                }}
                zoom={true}
                zoomConfig={{
                  maxZoom: 3,
                  minZoom: 1,
                  zoomStep: 0.5,
                  showControls: true,
                }}
                lazyLoad={false}
              />
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-white">
                <IconCard icon="file-image" size="lg" variant="default" />
                <p className="mt-4">{t('data.files.noImageAvailable', 'Không có ảnh')}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaPage;
