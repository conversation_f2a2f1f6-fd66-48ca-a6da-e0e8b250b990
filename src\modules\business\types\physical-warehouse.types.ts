import { QueryDto, SortDirection } from '@/shared/dto/request/query.dto';
import { WarehouseTypeEnum } from './warehouse.types';

/**
 * Interface cho trường tùy chỉnh của kho vật lý
 */
export interface PhysicalWarehouseCustomFieldDto {
  id: number;
  warehouseId: number;
  name: string;
  type: string;
  value: unknown;
  required: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho thông tin kho vật lý
 */
export interface PhysicalWarehouseDto {
  warehouseId: number;
  name: string;
  description: string;
  type: WarehouseTypeEnum;
  address: string;
  capacity: number | null;
  customFields?: PhysicalWarehouseCustomFieldDto[];
}

/**
 * Interface cho danh sách kho vật lý
 */
export interface PhysicalWarehouseListItemDto {
  warehouseId: number;
  name: string;
  description?: string;
  type: WarehouseTypeEnum;
  address: string;
  capacity?: number | null;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface cho dữ liệu tạo kho vật lý
 */
export interface CreatePhysicalWarehouseDto {
  warehouseId: number;
  address: string;
  capacity?: number;
}

/**
 * Interface cho dữ liệu cập nhật kho vật lý
 */
export interface UpdatePhysicalWarehouseDto {
  address?: string;
  capacity?: number;
}

/**
 * Interface cho tham số truy vấn kho vật lý
 */
export interface QueryPhysicalWarehouseDto extends QueryDto {
  sortBy?: string;
  sortDirection?: SortDirection;
  offset?: number;
}
