import { ApiProperty } from '@nestjs/swagger';
import { CampaignPlatform } from './create-campaign.dto';
import { CampaignServerDto } from './campaign-server.dto';
import { CampaignStatus } from './update-campaign.dto';

/**
 * DTO cho thống kê campaign
 */
export class CampaignStatsDto {
  /**
   * Tổng số đối tượng
   * @example 100
   */
  @ApiProperty({
    description: 'Tổng số đối tượng',
    example: 100,
  })
  totalRecipients: number;

  /**
   * Số lượng đã gửi
   * @example 80
   */
  @ApiProperty({
    description: 'Số lượng đã gửi',
    example: 80,
  })
  sent: number;

  /**
   * Số lượng đã nhận
   * @example 75
   */
  @ApiProperty({
    description: 'Số lượng đã nhận',
    example: 75,
  })
  delivered: number;

  /**
   * Số lượng đã mở (chỉ áp dụng cho email)
   * @example 50
   */
  @ApiProperty({
    description: 'Số lượng đã mở (chỉ áp dụng cho email)',
    example: 50,
  })
  opened: number;

  /**
   * Số lượng đã nhấp (chỉ áp dụng cho email)
   * @example 20
   */
  @ApiProperty({
    description: 'Số lượng đã nhấp (chỉ áp dụng cho email)',
    example: 20,
  })
  clicked: number;

  /**
   * Số lượng thất bại
   * @example 5
   */
  @ApiProperty({
    description: 'Số lượng thất bại',
    example: 5,
  })
  failed: number;
}

/**
 * DTO cho phản hồi thông tin campaign
 */
export class CampaignResponseDto {
  /**
   * ID của campaign
   * @example 1
   */
  @ApiProperty({
    description: 'ID của campaign',
    example: 1,
  })
  id: number;

  /**
   * Tiêu đề chiến dịch
   * @example "Khuyến mãi tháng 5"
   */
  @ApiProperty({
    description: 'Tiêu đề chiến dịch',
    example: 'Khuyến mãi tháng 5',
  })
  title: string;

  /**
   * Mô tả chiến dịch
   * @example "Chiến dịch khuyến mãi dành cho khách hàng VIP"
   */
  @ApiProperty({
    description: 'Mô tả chiến dịch',
    example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP',
  })
  description: string;

  /**
   * Nền tảng gửi
   * @example "email"
   */
  @ApiProperty({
    description: 'Nền tảng gửi',
    enum: CampaignPlatform,
    example: CampaignPlatform.EMAIL,
  })
  platform: CampaignPlatform;

  /**
   * Nội dung chiến dịch
   * @example "<p>Xin chào quý khách,</p><p>Chúng tôi xin gửi đến quý khách chương trình khuyến mãi...</p>"
   */
  @ApiProperty({
    description: 'Nội dung chiến dịch',
    example:
      '<p>Xin chào quý khách,</p><p>Chúng tôi xin gửi đến quý khách chương trình khuyến mãi...</p>',
  })
  content: string;

  /**
   * Thông tin máy chủ gửi
   */
  @ApiProperty({
    description: 'Thông tin máy chủ gửi',
    type: CampaignServerDto,
  })
  server: CampaignServerDto;

  /**
   * Thời gian dự kiến gửi chiến dịch (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian dự kiến gửi chiến dịch (Unix timestamp)',
    example: 1619171200,
  })
  scheduledAt: number;

  /**
   * Tiêu đề email (chỉ áp dụng cho chiến dịch email)
   * @example "Khuyến mãi đặc biệt dành cho bạn"
   */
  @ApiProperty({
    description: 'Tiêu đề email (chỉ áp dụng cho chiến dịch email)',
    example: 'Khuyến mãi đặc biệt dành cho bạn',
  })
  subject: string;

  /**
   * Trạng thái chiến dịch
   * @example "draft"
   */
  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    enum: CampaignStatus,
    example: CampaignStatus.DRAFT,
  })
  status: CampaignStatus;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1619171200,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;

  /**
   * Thống kê chiến dịch
   */
  @ApiProperty({
    description: 'Thống kê chiến dịch',
    type: CampaignStatsDto,
    required: false,
  })
  stats?: CampaignStatsDto;
}
