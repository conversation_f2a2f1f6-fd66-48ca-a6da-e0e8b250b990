export * from './customer.types';
export * from './conversion.types';
export * from './virtual-warehouse.types';
export * from './warehouse.types';
export * from './physical-warehouse.types';

// Export product types
export * from './product.types';

// Export custom field types with explicit naming to avoid conflicts
export {
  CustomFieldComponentEnum,
  CustomFieldTypeEnum,
} from './custom-field.types';

export type {
  CustomFieldConfig,
  CustomFieldListItemDto,
  CustomFieldDetailResponseDto,
  QueryCustomFieldDto,
  QueryCustomGroupFormDto,
  CreateCustomFieldDto,
  UpdateCustomFieldDto,
  CreateCustomGroupFormDto,
  UpdateCustomGroupFormDto,
  CustomGroupFormListItemDto,
  CustomGroupFormResponseDto,
} from './custom-field.types';

// Re-export with aliases to avoid conflicts
export type {
  CustomFieldDto as BusinessCustomFieldDto,
  CustomGroupFormDto as BusinessCustomGroupFormDto,
} from './custom-field.types';