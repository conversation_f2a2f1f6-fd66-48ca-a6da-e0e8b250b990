import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { ApiResponseDto as AppApiResponse } from '@common/response/api-response-dto';
import { Body, Controller, Delete, Get, Param, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CreatePermissionDto } from '../dto/create-permission.dto';
import { Permission } from '../entities';
import { PermissionService } from '../services/permission.service';

@ApiTags(SWAGGER_API_TAGS.EMPLOYEE_PERMISSIONS)
@ApiBearerAuth('JWT-auth')
@Controller('employee/permissions')
@UseGuards(JwtEmployeeGuard)
export class PermissionController {
  constructor(private readonly permissionService: PermissionService) { }

  @Get()
  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách tất cả quyền' })
  @ApiResponse({ status: 200, description: '<PERSON>h sách quyền' })
  async getAllPermissions(): Promise<AppApiResponse<Permission[]>> {
    const permissions = await this.permissionService.getAllPermissions();
    return AppApiResponse.success(permissions);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin quyền theo ID' })
  @ApiParam({ name: 'id', description: 'ID của quyền' })
  @ApiResponse({ status: 200, description: 'Thông tin quyền' })
  @ApiResponse({ status: 404, description: 'Quyền không tồn tại' })
  async getPermissionById(@Param('id') id: number): Promise<AppApiResponse<Permission>> {
    const permission = await this.permissionService.getPermissionById(id);
    return AppApiResponse.success(permission);
  }

  @Post()
  @ApiOperation({ summary: 'Tạo quyền mới' })
  @ApiResponse({ status: 201, description: 'Quyền đã được tạo' })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  async createPermission(@Body() dto: CreatePermissionDto): Promise<AppApiResponse<Permission>> {
    const permission = await this.permissionService.createPermission(dto);
    return AppApiResponse.success(permission);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Xóa quyền' })
  @ApiParam({ name: 'id', description: 'ID của quyền' })
  @ApiResponse({ status: 200, description: 'Quyền đã được xóa' })
  @ApiResponse({ status: 404, description: 'Quyền không tồn tại' })
  async deletePermission(@Param('id') id: number): Promise<AppApiResponse<{ success: boolean }>> {
    const result = await this.permissionService.deletePermission(id);
    return AppApiResponse.success(result);
  }
}
