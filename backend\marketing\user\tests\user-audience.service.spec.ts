import { Test, TestingModule } from '@nestjs/testing';
import { UserAudienceService } from '../services/user-audience.service';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import { UserAudienceCustomFieldRepository } from '../repositories/user-audience-custom-field.repository';
import { UserTagRepository } from '../repositories/user-tag.repository';
import { NotFoundException } from '@nestjs/common';
import { CreateAudienceDto, UpdateAudienceDto, CustomFieldType, AudienceQueryDto, AudienceSortField, SortOrder } from '../dto/audience';
// Các entity được sử dụng trong các mock
import { In } from 'typeorm';

describe('UserAudienceService', () => {
  let service: UserAudienceService;
  // Repository instances - used in the service

  const mockUserAudienceRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
    count: jest.fn(),
  };

  const mockUserAudienceCustomFieldRepository = {
    find: jest.fn(),
    save: jest.fn(),
    delete: jest.fn(),
  };

  const mockUserTagRepository = {
    find: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserAudienceService,
        {
          provide: UserAudienceRepository,
          useValue: mockUserAudienceRepository,
        },
        {
          provide: UserAudienceCustomFieldRepository,
          useValue: mockUserAudienceCustomFieldRepository,
        },
        {
          provide: UserTagRepository,
          useValue: mockUserTagRepository,
        },
      ],
    }).compile();

    service = module.get<UserAudienceService>(UserAudienceService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new audience with custom fields and tags', async () => {
      // Arrange
      const userId = 1;
      const createAudienceDto: CreateAudienceDto = {
        email: '<EMAIL>',
        phone: '+84912345678',
        customFields: [
          {
            fieldName: 'Address',
            fieldValue: 'Hanoi, Vietnam',
            fieldType: CustomFieldType.TEXT,
          },
        ],
        tagIds: [1, 2],
      };

      const savedAudience = {
        id: 1,
        userId,
        email: createAudienceDto.email,
        phone: createAudienceDto.phone,
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      const savedCustomFields = [
        {
          id: 1,
          audienceId: savedAudience.id,
          fieldName: createAudienceDto.customFields?.[0].fieldName || '',
          fieldValue: createAudienceDto.customFields?.[0].fieldValue || '',
          fieldType: createAudienceDto.customFields?.[0].fieldType || CustomFieldType.TEXT,
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
      ];

      const tags = [
        {
          id: 1,
          userId,
          name: 'Tag 1',
          color: '#FF5733',
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
        {
          id: 2,
          userId,
          name: 'Tag 2',
          color: '#33FF57',
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
      ];

      mockUserAudienceRepository.save.mockResolvedValue(savedAudience);
      mockUserAudienceCustomFieldRepository.save.mockResolvedValue(savedCustomFields);
      mockUserTagRepository.find.mockResolvedValue(tags);

      // Act
      const result = await service.create(userId, createAudienceDto);

      // Assert
      expect(mockUserAudienceRepository.save).toHaveBeenCalled();
      expect(mockUserAudienceCustomFieldRepository.save).toHaveBeenCalled();
      expect(mockUserTagRepository.find).toHaveBeenCalledWith({
        where: {
          id: In(createAudienceDto.tagIds || []),
          userId,
        },
      });
      expect(result.id).toEqual(savedAudience.id);
      expect(result.email).toEqual(savedAudience.email);
      expect(result.phone).toEqual(savedAudience.phone);
      expect(result.customFields).toHaveLength(1);
      expect(result.tags).toHaveLength(2);
    });
  });

  describe('findAll', () => {
    it('should return paginated audiences', async () => {
      // Arrange
      const userId = 1;
      const query: AudienceQueryDto = {
        page: 1,
        limit: 10,
        sortBy: AudienceSortField.CREATED_AT,
        sortOrder: SortOrder.DESC,
      };

      const audiences = [
        {
          id: 1,
          userId,
          email: '<EMAIL>',
          phone: '+84912345678',
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
        {
          id: 2,
          userId,
          email: '<EMAIL>',
          phone: '+84912345679',
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
      ];

      const customFields = [
        {
          id: 1,
          audienceId: 1,
          fieldName: 'Address',
          fieldValue: 'Hanoi, Vietnam',
          fieldType: CustomFieldType.TEXT,
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
      ];

      mockUserAudienceRepository.find.mockResolvedValue(audiences);
      mockUserAudienceRepository.count.mockResolvedValue(2);
      mockUserAudienceCustomFieldRepository.find.mockResolvedValue(customFields);

      // Act
      const result = await service.findAll(userId, query);

      // Assert
      expect(mockUserAudienceRepository.find).toHaveBeenCalled();
      expect(mockUserAudienceRepository.count).toHaveBeenCalled();
      expect(result.data).toHaveLength(2);
      expect(result.data[0].id).toEqual(audiences[0].id);
      expect(result.data[1].id).toEqual(audiences[1].id);
      expect(result.meta.total).toEqual(2);
      expect(result.meta.page).toEqual(1);
      expect(result.meta.limit).toEqual(10);
    });

    it('should filter audiences by email', async () => {
      // Arrange
      const userId = 1;
      const query: AudienceQueryDto = {
        page: 1,
        limit: 10,
        email: 'test1',
        sortBy: AudienceSortField.CREATED_AT,
        sortOrder: SortOrder.DESC,
      };

      const audiences = [
        {
          id: 1,
          userId,
          email: '<EMAIL>',
          phone: '+84912345678',
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
      ];

      mockUserAudienceRepository.find.mockResolvedValue(audiences);
      mockUserAudienceRepository.count.mockResolvedValue(1);
      mockUserAudienceCustomFieldRepository.find.mockResolvedValue([]);

      // Act
      const result = await service.findAll(userId, query);

      // Assert
      expect(mockUserAudienceRepository.find).toHaveBeenCalled();
      expect(result.data).toHaveLength(1);
      expect(result.data[0].id).toEqual(audiences[0].id);
      expect(result.meta.total).toEqual(1);
    });
  });

  describe('findOne', () => {
    it('should return an audience by id', async () => {
      // Arrange
      const userId = 1;
      const audienceId = 1;
      const audience = {
        id: audienceId,
        userId,
        email: '<EMAIL>',
        phone: '+84912345678',
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      const customFields = [
        {
          id: 1,
          audienceId,
          fieldName: 'Address',
          fieldValue: 'Hanoi, Vietnam',
          fieldType: CustomFieldType.TEXT,
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
      ];

      mockUserAudienceRepository.findOne.mockResolvedValue(audience);
      mockUserAudienceCustomFieldRepository.find.mockResolvedValue(customFields);

      // Act
      const result = await service.findOne(userId, audienceId);

      // Assert
      expect(mockUserAudienceRepository.findOne).toHaveBeenCalledWith({ where: { id: audienceId, userId } });
      expect(mockUserAudienceCustomFieldRepository.find).toHaveBeenCalledWith({ where: { audienceId } });
      expect(result.id).toEqual(audience.id);
      expect(result.email).toEqual(audience.email);
      expect(result.customFields).toHaveLength(1);
    });

    it('should throw NotFoundException if audience not found', async () => {
      // Arrange
      const userId = 1;
      const audienceId = 999;

      mockUserAudienceRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findOne(userId, audienceId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update an audience', async () => {
      // Arrange
      const userId = 1;
      const audienceId = 1;
      const updateAudienceDto: UpdateAudienceDto = {
        email: '<EMAIL>',
        customFields: [
          {
            fieldName: 'Address',
            fieldValue: 'Updated Address',
            fieldType: CustomFieldType.TEXT,
          },
        ],
      };

      const existingAudience = {
        id: audienceId,
        userId,
        email: '<EMAIL>',
        phone: '+84912345678',
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      const updatedAudience = {
        ...existingAudience,
        email: updateAudienceDto.email,
        updatedAt: 1619171300,
      };

      const updatedCustomFields = [
        {
          id: 1,
          audienceId,
          fieldName: updateAudienceDto.customFields?.[0].fieldName || '',
          fieldValue: updateAudienceDto.customFields?.[0].fieldValue || '',
          fieldType: updateAudienceDto.customFields?.[0].fieldType || CustomFieldType.TEXT,
          createdAt: 1619171200,
          updatedAt: 1619171300,
        },
      ];

      mockUserAudienceRepository.findOne.mockResolvedValue(existingAudience);
      mockUserAudienceRepository.save.mockResolvedValue(updatedAudience);
      mockUserAudienceCustomFieldRepository.delete.mockResolvedValue({});
      mockUserAudienceCustomFieldRepository.save.mockResolvedValue(updatedCustomFields);

      // Act
      const result = await service.update(userId, audienceId, updateAudienceDto);

      // Assert
      expect(mockUserAudienceRepository.findOne).toHaveBeenCalledWith({ where: { id: audienceId, userId } });
      expect(mockUserAudienceRepository.save).toHaveBeenCalled();
      expect(mockUserAudienceCustomFieldRepository.delete).toHaveBeenCalledWith({ audienceId });
      expect(mockUserAudienceCustomFieldRepository.save).toHaveBeenCalled();
      expect(result.email).toEqual(updateAudienceDto.email);
      expect(result.customFields).toHaveLength(1);
    });

    it('should throw NotFoundException if audience not found', async () => {
      // Arrange
      const userId = 1;
      const audienceId = 999;
      const updateAudienceDto: UpdateAudienceDto = {
        email: '<EMAIL>',
      };

      mockUserAudienceRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.update(userId, audienceId, updateAudienceDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should remove an audience', async () => {
      // Arrange
      const userId = 1;
      const audienceId = 1;
      const audience = {
        id: audienceId,
        userId,
        email: '<EMAIL>',
        phone: '+84912345678',
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      mockUserAudienceRepository.findOne.mockResolvedValue(audience);
      mockUserAudienceRepository.remove.mockResolvedValue(audience);
      mockUserAudienceCustomFieldRepository.delete.mockResolvedValue({});

      // Act
      const result = await service.remove(userId, audienceId);

      // Assert
      expect(mockUserAudienceRepository.findOne).toHaveBeenCalledWith({ where: { id: audienceId, userId } });
      expect(mockUserAudienceCustomFieldRepository.delete).toHaveBeenCalledWith({ audienceId });
      expect(mockUserAudienceRepository.remove).toHaveBeenCalledWith(audience);
      expect(result).toBe(true);
    });

    it('should throw NotFoundException if audience not found', async () => {
      // Arrange
      const userId = 1;
      const audienceId = 999;

      mockUserAudienceRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.remove(userId, audienceId)).rejects.toThrow(NotFoundException);
    });
  });
});
