import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  FacebookPageDto,
  FacebookPageQueryDto,
  FacebookAuthResponseDto,
  FacebookCallbackDto,
  CallbackResponseDto,
  CreateFacebookUrlAuthDto,
  DeleteFacebookPagesDto,
  ConnectAgentToFacebookPageDto,
  DisconnectAgentFromFacebookPageDto,
} from '../types/facebook.types';

/**
 * Facebook Integration API
 * Các API functions cho Facebook integration
 */

/**
 * L<PERSON>y danh sách Facebook Pages với phân trang và lọc
 * @param params Query parameters
 * @returns Promise với response từ API
 */
export const getFacebookPages = async (
  params?: FacebookPageQueryDto
): Promise<ApiResponseDto<PaginatedResult<FacebookPageDto>>> => {
  return apiClient.get('/integration/facebook/pages', { params });
};

/**
 * Tạo URL xác thực Facebook
 * @param params Parameters cho tạo auth URL
 * @returns Promise với response từ API
 */
export const createFacebookAuthUrl = async (
  params: CreateFacebookUrlAuthDto
): Promise<ApiResponseDto<FacebookAuthResponseDto>> => {
  return apiClient.get('/integration/facebook/auth/url', { params });
};

/**
 * Xử lý callback từ Facebook
 * @param data Callback data từ Facebook
 * @returns Promise với response từ API
 */
export const handleFacebookCallback = async (
  data: FacebookCallbackDto
): Promise<ApiResponseDto<CallbackResponseDto>> => {
  return apiClient.post('/integration/facebook/auth/callback', data);
};

/**
 * Xóa một Facebook Page
 * @param pageId ID của Facebook Page cần xóa
 * @returns Promise với response từ API
 */
export const deleteFacebookPage = async (
  pageId: string
): Promise<ApiResponseDto<null>> => {
  return apiClient.delete(`/integration/facebook/pages/${pageId}`);
};

/**
 * Xóa nhiều Facebook Pages cùng lúc
 * @param data Danh sách page IDs cần xóa
 * @returns Promise với response từ API
 */
export const deleteManyFacebookPages = async (
  data: DeleteFacebookPagesDto
): Promise<ApiResponseDto<{ deletedCount: number; errorPages?: string[] }>> => {
  return apiClient.delete('/integration/facebook/pages', { data });
};

/**
 * Xóa Facebook Personal account
 * @param personalId ID của Facebook Personal account
 * @returns Promise với response từ API
 */
export const deleteFacebookPersonal = async (
  personalId: string
): Promise<ApiResponseDto<null>> => {
  return apiClient.delete(`/integration/facebook/personals/${personalId}`);
};

/**
 * Kết nối Agent với Facebook Page
 * @param data Data để kết nối agent
 * @returns Promise với response từ API
 */
export const connectAgentToFacebookPage = async (
  data: ConnectAgentToFacebookPageDto
): Promise<ApiResponseDto<FacebookPageDto>> => {
  return apiClient.post('/integration/facebook/pages/connect-agent', data);
};

/**
 * Ngắt kết nối Agent khỏi Facebook Page
 * @param data Data để ngắt kết nối agent
 * @returns Promise với response từ API
 */
export const disconnectAgentFromFacebookPage = async (
  data: DisconnectAgentFromFacebookPageDto
): Promise<ApiResponseDto<FacebookPageDto>> => {
  return apiClient.post('/integration/facebook/pages/disconnect-agent', data);
};

/**
 * Lấy thông tin chi tiết một Facebook Page
 * @param pageId ID của Facebook Page
 * @returns Promise với response từ API
 */
export const getFacebookPageDetail = async (
  pageId: string
): Promise<ApiResponseDto<FacebookPageDto>> => {
  return apiClient.get(`/integration/facebook/pages/${pageId}`);
};

/**
 * Cập nhật trạng thái Facebook Page
 * @param pageId ID của Facebook Page
 * @param isActive Trạng thái active
 * @returns Promise với response từ API
 */
export const updateFacebookPageStatus = async (
  pageId: string,
  isActive: boolean
): Promise<ApiResponseDto<FacebookPageDto>> => {
  return apiClient.patch(`/integration/facebook/pages/${pageId}/status`, {
    isActive,
  });
};

/**
 * Đồng bộ lại Facebook Pages
 * @param personalId ID của Facebook Personal account
 * @returns Promise với response từ API
 */
export const syncFacebookPages = async (
  personalId: string
): Promise<ApiResponseDto<CallbackResponseDto>> => {
  return apiClient.post(`/integration/facebook/personals/${personalId}/sync`);
};
