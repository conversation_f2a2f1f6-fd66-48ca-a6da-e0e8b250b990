import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin tag
 */
export class TagResponseDto {
  /**
   * ID của tag
   * @example 1
   */
  @ApiProperty({
    description: 'ID của tag',
    example: 1,
  })
  id: number;

  /**
   * Tên tag
   * @example "Khách hàng VIP"
   */
  @ApiProperty({
    description: 'Tên tag',
    example: 'Khách hàng VIP',
  })
  name: string;

  /**
   * Mã màu của tag (định dạng HEX)
   * @example "#FF5733"
   */
  @ApiProperty({
    description: 'Mã màu của tag (định dạng HEX)',
    example: '#FF5733',
  })
  color: string;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1619171200,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}
