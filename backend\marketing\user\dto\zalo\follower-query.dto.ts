import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * Enum cho trạng thái người theo dõi
 */
export enum FollowerStatus {
  ACTIVE = 'active',
  UNFOLLOWED = 'unfollowed',
  ALL = 'all',
}

/**
 * DTO cho việc truy vấn danh sách người theo dõi
 */
export class FollowerQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo tên hiển thị',
    example: 'Nguyễn',
    required: false,
  })
  @IsOptional()
  @IsString()
  displayName?: string;

  @ApiProperty({
    description: 'Tìm kiếm theo tag',
    example: 'vip',
    required: false,
  })
  @IsOptional()
  @IsString()
  tag?: string;

  @ApiProperty({
    description: 'Lọc theo trạng thái',
    enum: FollowerStatus,
    example: FollowerStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(FollowerStatus)
  status?: FollowerStatus;

  constructor() {
    super();
    this.sortBy = 'followedAt';
    this.sortDirection = SortDirection.DESC;
  }
}
