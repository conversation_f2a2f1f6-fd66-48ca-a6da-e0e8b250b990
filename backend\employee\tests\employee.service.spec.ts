import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeService } from '../services/employee.service';
import { EmployeeRepository } from '../repositories/employee.repository';
import { EmployeeHasRoleRepository } from '../repositories/employee-has-role.repository';
import { S3Service } from '@/shared/services/s3.service';
import { EmployeePasswordService } from '../services/employee-password.service';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { Employee } from '../entities/employee.entity';
import { CreateEmployeeDto } from '../dto/create-employee.dto';
import { EmployeeAvatarUploadDto } from '../dto/employee-avatar-upload.dto';
import { TimeIntervalEnum } from '@/shared/utils';
import { CategoryFolderEnum } from '@/shared/utils/file';
import { FileSizeEnum, ImageTypeEnum } from '../dto/employee-avatar-upload.dto';
import { mockConfigModule } from './mocks/config.mock';
import { initMockTransactionalContext } from './mocks/typeorm-transactional.mock';

// Khởi tạo context cho typeorm-transactional
initMockTransactionalContext();

// Mock Transactional decorator
jest.mock('typeorm-transactional', () => ({
  Transactional: () => (_: any, __: string, descriptor: PropertyDescriptor) => descriptor,
  initializeTransactionalContext: jest.fn(),
  patchTypeORMRepositoryWithBaseRepository: jest.fn(),
}));

// Mock the external modules
jest.mock('@/shared/utils/generators', () => ({
  generateS3Key: jest.fn().mockReturnValue('mocked-s3-key')
}));

describe('EmployeeService', () => {
  let service: EmployeeService;
  let employeeRepository: jest.Mocked<EmployeeRepository>;
  let employeeHasRoleRepository: jest.Mocked<EmployeeHasRoleRepository>;
  let s3Service: jest.Mocked<S3Service>;
  let passwordService: jest.Mocked<EmployeePasswordService>;

  const mockEmployee: Employee = {
    id: 1,
    fullName: 'Test Employee',
    email: '<EMAIL>',
    phoneNumber: '0987654321',
    password: 'hashedPassword',
    address: 'Test Address',
    createdAt: Date.now(),
    updatedAt: Date.now(),
    enable: true,
    avatar: 'avatar-url',
    roles: []
  };

  beforeEach(async () => {
    // Create mock implementations
    const employeeRepoMock = {
      findByEmail: jest.fn(),
      createEmployee: jest.fn(),
      findById: jest.fn(),
      updateAvatar: jest.fn(),
      changePassword: jest.fn(),
    };

    const employeeHasRoleRepoMock = {
      assignRolesToEmployee: jest.fn(),
      findRolesByEmployeeId: jest.fn(),
    };

    const s3ServiceMock = {
      createPresignedWithID: jest.fn(),
    };

    const passwordServiceMock = {
      validatePasswordStrength: jest.fn(),
      hashPassword: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmployeeService,
        { provide: EmployeeRepository, useValue: employeeRepoMock },
        { provide: EmployeeHasRoleRepository, useValue: employeeHasRoleRepoMock },
        { provide: S3Service, useValue: s3ServiceMock },
        { provide: EmployeePasswordService, useValue: passwordServiceMock },
        mockConfigModule,
      ],
    }).compile();

    service = module.get<EmployeeService>(EmployeeService);
    employeeRepository = module.get(EmployeeRepository) as jest.Mocked<EmployeeRepository>;
    employeeHasRoleRepository = module.get(EmployeeHasRoleRepository) as jest.Mocked<EmployeeHasRoleRepository>;
    s3Service = module.get(S3Service) as jest.Mocked<S3Service>;
    passwordService = module.get(EmployeePasswordService) as jest.Mocked<EmployeePasswordService>;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createEmployee', () => {
    it('should create a new employee successfully', async () => {
      // Arrange
      const createEmployeeDto: CreateEmployeeDto = {
        fullName: 'New Employee',
        email: '<EMAIL>',
        phoneNumber: '0123456789',
        password: 'Password123!',
        address: 'New Address',
        roleIds: [1, 2]
      };

      employeeRepository.findByEmail.mockResolvedValue(null);
      passwordService.validatePasswordStrength.mockReturnValue(undefined);
      passwordService.hashPassword.mockResolvedValue('hashedPassword');
      employeeRepository.createEmployee.mockResolvedValue(mockEmployee);
      employeeHasRoleRepository.assignRolesToEmployee.mockResolvedValue(mockEmployee);

      // Act
      const result = await service.createEmployee(createEmployeeDto);

      // Assert
      expect(employeeRepository.findByEmail).toHaveBeenCalledWith(createEmployeeDto.email);
      expect(passwordService.validatePasswordStrength).toHaveBeenCalledWith(createEmployeeDto.password);
      expect(passwordService.hashPassword).toHaveBeenCalledWith(createEmployeeDto.password);
      expect(employeeRepository.createEmployee).toHaveBeenCalledWith(createEmployeeDto, 'hashedPassword');
      expect(employeeHasRoleRepository.assignRolesToEmployee).toHaveBeenCalledWith(mockEmployee.id, createEmployeeDto.roleIds);
      expect(result).toEqual({
        id: mockEmployee.id,
        fullName: mockEmployee.fullName,
        email: mockEmployee.email,
        phoneNumber: mockEmployee.phoneNumber,
        address: mockEmployee.address,
        createdAt: mockEmployee.createdAt,
        updatedAt: mockEmployee.updatedAt,
        enable: mockEmployee.enable,
      });
    });

    it('should create a new employee with avatar upload URL', async () => {
      // Arrange
      const createEmployeeDto: CreateEmployeeDto = {
        fullName: 'New Employee',
        email: '<EMAIL>',
        phoneNumber: '0123456789',
        password: 'Password123!',
        address: 'New Address',
        roleIds: [1, 2],
        avatarImageType: ImageTypeEnum.JPEG,
        avatarMaxSize: 2097152
      };

      employeeRepository.findByEmail.mockResolvedValue(null);
      passwordService.validatePasswordStrength.mockReturnValue(undefined);
      passwordService.hashPassword.mockResolvedValue('hashedPassword');
      employeeRepository.createEmployee.mockResolvedValue(mockEmployee);
      employeeHasRoleRepository.assignRolesToEmployee.mockResolvedValue(mockEmployee);
      s3Service.createPresignedWithID.mockResolvedValue('presigned-url');

      // Mock Date.now() để có giá trị cố định cho expiresAt
      const originalDateNow = Date.now;
      const mockNow = 1682506092000;
      global.Date.now = jest.fn(() => mockNow);

      // Act
      const result = await service.createEmployee(createEmployeeDto);

      // Restore Date.now
      global.Date.now = originalDateNow;

      // Assert
      expect(employeeRepository.findByEmail).toHaveBeenCalledWith(createEmployeeDto.email);
      expect(passwordService.validatePasswordStrength).toHaveBeenCalledWith(createEmployeeDto.password);
      expect(passwordService.hashPassword).toHaveBeenCalledWith(createEmployeeDto.password);
      expect(employeeRepository.createEmployee).toHaveBeenCalledWith(createEmployeeDto, 'hashedPassword');
      expect(employeeHasRoleRepository.assignRolesToEmployee).toHaveBeenCalledWith(mockEmployee.id, createEmployeeDto.roleIds);
      expect(s3Service.createPresignedWithID).toHaveBeenCalled();
      expect(result).toHaveProperty('avatarUploadUrl', 'presigned-url');
      expect(result).toHaveProperty('avatarKey');
      expect(result).toHaveProperty('avatarUrlExpiresAt', mockNow + TimeIntervalEnum.FIVE_MINUTES * 1000);
    });

    it('should throw ConflictException when email already exists', async () => {
      // Arrange
      const createEmployeeDto: CreateEmployeeDto = {
        fullName: 'New Employee',
        email: '<EMAIL>',
        phoneNumber: '0123456789',
        password: 'Password123!',
        address: 'New Address',
        roleIds: [1, 2]
      };

      employeeRepository.findByEmail.mockResolvedValue(mockEmployee);

      // Act & Assert
      await expect(service.createEmployee(createEmployeeDto)).rejects.toThrow(ConflictException);
      expect(employeeRepository.findByEmail).toHaveBeenCalledWith(createEmployeeDto.email);
    });
  });

  describe('createAvatarUploadUrl', () => {
    it('should create a presigned URL for avatar upload', async () => {
      // Arrange
      const employeeId = 1;
      const avatarUploadDto: EmployeeAvatarUploadDto = {
        imageType: ImageTypeEnum.JPEG,
        maxSize: FileSizeEnum.TWO_MB
      };

      employeeRepository.findById.mockResolvedValue(mockEmployee);
      s3Service.createPresignedWithID.mockResolvedValue('presigned-url');

      // Act
      const result = await service.createAvatarUploadUrl(employeeId, avatarUploadDto);

      // Assert
      expect(employeeRepository.findById).toHaveBeenCalledWith(employeeId);
      expect(s3Service.createPresignedWithID).toHaveBeenCalledWith(
        'mocked-s3-key',
        TimeIntervalEnum.FIVE_MINUTES,
        avatarUploadDto.imageType,
        avatarUploadDto.maxSize
      );
      expect(result).toEqual({
        uploadUrl: 'presigned-url',
        avatarKey: 'mocked-s3-key',
        expiresIn: TimeIntervalEnum.FIVE_MINUTES
      });
    });

    it('should throw NotFoundException when employee is not found', async () => {
      // Arrange
      const employeeId = 999;
      const avatarUploadDto: EmployeeAvatarUploadDto = {
        imageType: ImageTypeEnum.JPEG,
        maxSize: FileSizeEnum.TWO_MB
      };

      employeeRepository.findById.mockRejectedValue(new NotFoundException(`Nhân viên với ID "${employeeId}" không tồn tại`));

      // Act & Assert
      await expect(service.createAvatarUploadUrl(employeeId, avatarUploadDto)).rejects.toThrow(NotFoundException);
      expect(employeeRepository.findById).toHaveBeenCalledWith(employeeId);
    });
  });

  describe('updateAvatar', () => {
    it('should update employee avatar successfully', async () => {
      // Arrange
      const employeeId = 1;
      const updateAvatarDto = { avatarKey: 'new-avatar-key' };

      employeeRepository.updateAvatar.mockResolvedValue({
        ...mockEmployee,
        avatar: updateAvatarDto.avatarKey
      });

      // Act
      const result = await service.updateAvatar(employeeId, updateAvatarDto);

      // Assert
      expect(employeeRepository.updateAvatar).toHaveBeenCalledWith(employeeId, updateAvatarDto.avatarKey);
      expect(result.avatar).toEqual(updateAvatarDto.avatarKey);
    });

    it('should throw NotFoundException when employee is not found', async () => {
      // Arrange
      const employeeId = 999;
      const updateAvatarDto = { avatarKey: 'new-avatar-key' };

      employeeRepository.updateAvatar.mockRejectedValue(new NotFoundException(`Nhân viên với ID "${employeeId}" không tồn tại`));

      // Act & Assert
      await expect(service.updateAvatar(employeeId, updateAvatarDto)).rejects.toThrow(NotFoundException);
      expect(employeeRepository.updateAvatar).toHaveBeenCalledWith(employeeId, updateAvatarDto.avatarKey);
    });
  });

  describe('changePassword', () => {
    it('should change employee password successfully', async () => {
      // Arrange
      const employeeId = 1;
      const changePasswordDto = { newPassword: 'NewPassword123!' };

      passwordService.validatePasswordStrength.mockReturnValue(undefined);
      passwordService.hashPassword.mockResolvedValue('newHashedPassword');
      employeeRepository.changePassword.mockResolvedValue(mockEmployee);

      // Act
      const result = await service.changePassword(employeeId, changePasswordDto);

      // Assert
      expect(passwordService.validatePasswordStrength).toHaveBeenCalledWith(changePasswordDto.newPassword);
      expect(passwordService.hashPassword).toHaveBeenCalledWith(changePasswordDto.newPassword);
      expect(employeeRepository.changePassword).toHaveBeenCalledWith(employeeId, 'newHashedPassword');
      expect(result).toEqual({ message: 'Đổi mật khẩu thành công' });
    });
  });

  describe('assignRoles', () => {
    it('should assign roles to employee successfully', async () => {
      // Arrange
      const employeeId = 1;
      const assignRoleDto = { roleIds: [1, 2, 3] };

      employeeHasRoleRepository.assignRolesToEmployee.mockResolvedValue(mockEmployee);

      // Act
      const result = await service.assignRoles(employeeId, assignRoleDto);

      // Assert
      expect(employeeHasRoleRepository.assignRolesToEmployee).toHaveBeenCalledWith(employeeId, assignRoleDto.roleIds);
      expect(result).toEqual(mockEmployee);
    });
  });

  describe('getEmployeeRoles', () => {
    it('should get employee roles successfully', async () => {
      // Arrange
      const employeeId = 1;
      const mockRoles = [{ id: 1, name: 'Admin', description: 'Administrator', permissions: [] }];

      employeeHasRoleRepository.findRolesByEmployeeId.mockResolvedValue(mockRoles);

      // Act
      const result = await service.getEmployeeRoles(employeeId);

      // Assert
      expect(employeeHasRoleRepository.findRolesByEmployeeId).toHaveBeenCalledWith(employeeId);
      expect(result).toEqual(mockRoles);
    });

    it('should throw NotFoundException when employee is not found', async () => {
      // Arrange
      const employeeId = 999;

      employeeHasRoleRepository.findRolesByEmployeeId.mockRejectedValue(
        new Error(`Nhân viên với ID "${employeeId}" không tồn tại`)
      );

      // Act & Assert
      await expect(service.getEmployeeRoles(employeeId)).rejects.toThrow(Error);
      expect(employeeHasRoleRepository.findRolesByEmployeeId).toHaveBeenCalledWith(employeeId);
    });
  });
});
