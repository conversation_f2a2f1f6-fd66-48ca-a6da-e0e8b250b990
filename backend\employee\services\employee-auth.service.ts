import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { JwtUtilService, TokenType } from '@modules/auth/guards/jwt.util';
import { EmployeeRepository } from '../repositories/employee.repository';
import { EmployeeLoginDto } from '../dto/employee-login.dto';
import * as bcrypt from 'bcrypt';
import { Employee } from '../entities/employee.entity';
import { EmployeeHasRoleRepository } from '../repositories/employee-has-role.repository';
import { AppException, ErrorCode } from '@/common';
import { AUTH_ERROR_CODE } from '@/modules/auth/errors';

/**
 * Service xử lý xác thực nhân viên
 */
@Injectable()
export class EmployeeAuthService {
  private readonly logger = new Logger(EmployeeAuthService.name);

  constructor(
    private readonly employeeRepository: EmployeeRepository,
    private readonly employeeHasRoleRepository: EmployeeHasRoleRepository,
    private readonly jwtUtilService: JwtUtilService,
  ) {}

  /**
   * Đăng nhập nhân viên
   * @param loginDto Thông tin đăng nhập
   * @returns Token JWT và thông tin nhân viên
   */
  async login(loginDto: EmployeeLoginDto) {

    try {
      if (loginDto.recaptchaToken) {
        // Bỏ comment dòng dưới đây khi triển khai thực tế
        // const recaptchaResponse = await this.recaptchaService.verifyRecaptcha(loginDto.recaptchaToken);
        //
        // if (!recaptchaResponse.success) {
        //   throw new AppException(ErrorCode.RECAPTCHA_VERIFICATION_FAILED);
        // }
      }
    } catch (error) {
      throw new AppException(
        AUTH_ERROR_CODE.TOO_MANY_REQUESTS,
        'Xác thực reCAPTCHA thất bại',
      );
    }

    try {
      // Xác thực nhân viên
      const employee = await this.validateEmployee(loginDto.email, loginDto.password);

      // Lấy danh sách vai trò của nhân viên
      const roles = await this.employeeHasRoleRepository.findRolesByEmployeeId(employee.id);

      // Lấy danh sách quyền từ vai trò
      const permissions = roles.flatMap(role =>
        role.permissions ? role.permissions.map(permission => `${permission.module}:${permission.action}`) : []
      );

      // Tạo JWT token
      const { token: accessToken, expiresInSeconds } = this.jwtUtilService.generateEmployeeAccessToken({
        sub: employee.id,
        username: employee.email,
        permissions: permissions,
      });

      // Tạo refresh token
      const { token: refreshToken, expiresInSeconds: refreshExpiresIn } = this.jwtUtilService.generateRefreshToken({
        id: employee.id,
        sub: employee.id,
        username: employee.email,
        permissions: permissions,
        isAdmin: true,
        typeToken: TokenType.REFRESH,
      });

      // Tính toán thời điểm hết hạn thực tế (timestamp)
      const expiresAt = Date.now() + expiresInSeconds * 1000;
      const refreshExpiresAt = Date.now() + refreshExpiresIn * 1000;

      // Trả về thông tin đăng nhập
      return {
        accessToken,
        refreshToken,
        expiresIn: expiresInSeconds, // Giữ lại để tương thích ngược
        expiresAt,
        refreshExpiresIn,
        refreshExpiresAt,
        employee: {
          id: employee.id,
          email: employee.email,
          fullName: employee.fullName,
          avatar: employee.avatar,
          roles: roles.map(role => ({
            id: role.id,
            name: role.name,
            // code property is not available in the current EmployeeRole entity
            // Using role name as a code substitute
            code: role.name.toLowerCase().replace(/\s+/g, '_'),
          })),
          permissions,
        },
      };
    } catch (error) {
      this.logger.error(`Error during employee login: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xác thực nhân viên bằng email và mật khẩu
   * @param email Email nhân viên
   * @param password Mật khẩu nhân viên
   * @returns Thông tin nhân viên nếu xác thực thành công
   */
  private async validateEmployee(email: string, password: string): Promise<Employee> {
    // Tìm nhân viên theo email
    const employee = await this.employeeRepository.findByEmail(email);
    if (!employee) {
      throw new AppException(ErrorCode.EMAIL_OR_PASSWORD_NOT_VALID);
    }

    // Kiểm tra mật khẩu
    const isPasswordValid = await this.comparePasswords(password, employee.password);
    if (!isPasswordValid) {
      throw new AppException(ErrorCode.EMAIL_OR_PASSWORD_NOT_VALID);
    }

    // Kiểm tra trạng thái tài khoản
    if (!employee.enable) {
      throw new AppException(ErrorCode.EMPLOYEE_HAS_BLOCKED);
    }

    return employee;
  }

  /**
   * So sánh mật khẩu
   * @param plainPassword Mật khẩu gốc
   * @param hashedPassword Mật khẩu đã mã hóa
   * @returns true nếu mật khẩu khớp, ngược lại false
   */
  private async comparePasswords(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }
}
