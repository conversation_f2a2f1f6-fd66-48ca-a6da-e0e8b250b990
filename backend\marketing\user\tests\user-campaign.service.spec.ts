import { Test, TestingModule } from '@nestjs/testing';
import { UserCampaignService } from '../services/user-campaign.service';
import { UserCampaignRepository } from '../repositories/user-campaign.repository';
import { UserCampaignHistoryRepository } from '../repositories/user-campaign-history.repository';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import { UserSegmentRepository } from '../repositories/user-segment.repository';
import { UserSegmentService } from '../services/user-segment.service';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { CreateCampaignDto, UpdateCampaignDto, CampaignPlatform, ServerType, CampaignStatus, SendStatus } from '../dto/campaign';
// Các entity được sử dụng trong các mock
import { In } from 'typeorm';

describe('UserCampaignService', () => {
  let service: UserCampaignService;
  let campaignRepository: UserCampaignRepository;
  let campaignHistoryRepository: UserCampaignHistoryRepository;
  let audienceRepository: UserAudienceRepository;
  let segmentRepository: UserSegmentRepository;
  let segmentService: UserSegmentService;

  const mockUserCampaignRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
  };

  const mockUserCampaignHistoryRepository = {
    find: jest.fn(),
    save: jest.fn(),
    delete: jest.fn(),
  };

  const mockUserAudienceRepository = {
    find: jest.fn(),
  };

  const mockUserSegmentRepository = {
    findOne: jest.fn(),
  };

  const mockUserSegmentService = {
    getAudiencesInSegment: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserCampaignService,
        {
          provide: UserCampaignRepository,
          useValue: mockUserCampaignRepository,
        },
        {
          provide: UserCampaignHistoryRepository,
          useValue: mockUserCampaignHistoryRepository,
        },
        {
          provide: UserAudienceRepository,
          useValue: mockUserAudienceRepository,
        },
        {
          provide: UserSegmentRepository,
          useValue: mockUserSegmentRepository,
        },
        {
          provide: UserSegmentService,
          useValue: mockUserSegmentService,
        },
      ],
    }).compile();

    service = module.get<UserCampaignService>(UserCampaignService);
    campaignRepository = module.get<UserCampaignRepository>(UserCampaignRepository);
    campaignHistoryRepository = module.get<UserCampaignHistoryRepository>(UserCampaignHistoryRepository);
    audienceRepository = module.get<UserAudienceRepository>(UserAudienceRepository);
    segmentRepository = module.get<UserSegmentRepository>(UserSegmentRepository);
    segmentService = module.get<UserSegmentService>(UserSegmentService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new campaign with segment', async () => {
      // Arrange
      const userId = 1;
      const createCampaignDto: CreateCampaignDto = {
        title: 'Test Campaign',
        description: 'Test Description',
        platform: CampaignPlatform.EMAIL,
        content: '<p>Test Content</p>',
        server: {
          type: ServerType.SMTP,
          name: 'Test Server',
          config: {
            host: 'smtp.example.com',
            port: 587,
            secure: false,
            auth: {
              user: '<EMAIL>',
              pass: 'password',
            },
          },
          fromEmail: '<EMAIL>',
          fromName: 'Test Sender',
        },
        subject: 'Test Subject',
        segmentId: 1,
      };

      const savedCampaign = {
        id: 1,
        userId,
        title: createCampaignDto.title,
        description: createCampaignDto.description,
        platform: createCampaignDto.platform,
        content: createCampaignDto.content,
        server: createCampaignDto.server,
        scheduledAt: null,
        subject: createCampaignDto.subject,
        status: CampaignStatus.DRAFT,
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      mockUserCampaignRepository.save.mockResolvedValue(savedCampaign);

      // Act
      const result = await service.create(userId, createCampaignDto);

      // Assert
      expect(mockUserCampaignRepository.save).toHaveBeenCalled();
      expect(result).toEqual({
        id: savedCampaign.id,
        title: savedCampaign.title,
        description: savedCampaign.description,
        platform: savedCampaign.platform,
        content: savedCampaign.content,
        server: savedCampaign.server,
        scheduledAt: savedCampaign.scheduledAt,
        subject: savedCampaign.subject,
        status: savedCampaign.status,
        createdAt: savedCampaign.createdAt,
        updatedAt: savedCampaign.updatedAt,
      });
    });

    it('should create a new campaign with audience IDs', async () => {
      // Arrange
      const userId = 1;
      const createCampaignDto: CreateCampaignDto = {
        title: 'Test Campaign',
        description: 'Test Description',
        platform: CampaignPlatform.EMAIL,
        content: '<p>Test Content</p>',
        server: {
          type: ServerType.SMTP,
          name: 'Test Server',
          config: {
            host: 'smtp.example.com',
            port: 587,
            secure: false,
            auth: {
              user: '<EMAIL>',
              pass: 'password',
            },
          },
          fromEmail: '<EMAIL>',
          fromName: 'Test Sender',
        },
        subject: 'Test Subject',
        audienceIds: [1, 2, 3],
      };

      const savedCampaign = {
        id: 1,
        userId,
        title: createCampaignDto.title,
        description: createCampaignDto.description,
        platform: createCampaignDto.platform,
        content: createCampaignDto.content,
        server: createCampaignDto.server,
        scheduledAt: null,
        subject: createCampaignDto.subject,
        status: CampaignStatus.DRAFT,
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      mockUserCampaignRepository.save.mockResolvedValue(savedCampaign);

      // Act
      const result = await service.create(userId, createCampaignDto);

      // Assert
      expect(mockUserCampaignRepository.save).toHaveBeenCalled();
      expect(result).toEqual({
        id: savedCampaign.id,
        title: savedCampaign.title,
        description: savedCampaign.description,
        platform: savedCampaign.platform,
        content: savedCampaign.content,
        server: savedCampaign.server,
        scheduledAt: savedCampaign.scheduledAt,
        subject: savedCampaign.subject,
        status: savedCampaign.status,
        createdAt: savedCampaign.createdAt,
        updatedAt: savedCampaign.updatedAt,
      });
    });

    it('should throw BadRequestException if neither segmentId nor audienceIds are provided', async () => {
      // Arrange
      const userId = 1;
      const createCampaignDto: CreateCampaignDto = {
        title: 'Test Campaign',
        description: 'Test Description',
        platform: CampaignPlatform.EMAIL,
        content: '<p>Test Content</p>',
        server: {
          type: ServerType.SMTP,
          name: 'Test Server',
          config: {
            host: 'smtp.example.com',
            port: 587,
            secure: false,
            auth: {
              user: '<EMAIL>',
              pass: 'password',
            },
          },
          fromEmail: '<EMAIL>',
          fromName: 'Test Sender',
        },
        subject: 'Test Subject',
      };

      // Act & Assert
      await expect(service.create(userId, createCampaignDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findAll', () => {
    it('should return an array of campaigns', async () => {
      // Arrange
      const userId = 1;
      const campaigns = [
        {
          id: 1,
          userId,
          title: 'Campaign 1',
          description: 'Description 1',
          platform: CampaignPlatform.EMAIL,
          content: '<p>Content 1</p>',
          server: {
            type: ServerType.SMTP,
            name: 'Server 1',
            config: {},
          },
          scheduledAt: null,
          subject: 'Subject 1',
          status: CampaignStatus.DRAFT,
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
        {
          id: 2,
          userId,
          title: 'Campaign 2',
          description: 'Description 2',
          platform: CampaignPlatform.SMS,
          content: 'Content 2',
          server: {
            type: ServerType.API,
            name: 'Server 2',
            config: {},
          },
          scheduledAt: 1619171300,
          subject: null,
          status: CampaignStatus.SCHEDULED,
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
      ];

      mockUserCampaignRepository.find.mockResolvedValue(campaigns);

      // Act
      const result = await service.findAll(userId);

      // Assert
      expect(mockUserCampaignRepository.find).toHaveBeenCalledWith({ where: { userId } });
      expect(result).toHaveLength(2);
      expect(result[0].id).toEqual(campaigns[0].id);
      expect(result[1].id).toEqual(campaigns[1].id);
    });
  });

  describe('findOne', () => {
    it('should return a campaign by id with stats', async () => {
      // Arrange
      const userId = 1;
      const campaignId = 1;
      const campaign = {
        id: campaignId,
        userId,
        title: 'Test Campaign',
        description: 'Test Description',
        platform: CampaignPlatform.EMAIL,
        content: '<p>Test Content</p>',
        server: {
          type: ServerType.SMTP,
          name: 'Test Server',
          config: {},
        },
        scheduledAt: null,
        subject: 'Test Subject',
        status: CampaignStatus.DRAFT,
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      const history = [
        {
          id: 1,
          campaignId,
          audienceId: 1,
          status: SendStatus.SENT,
          sentAt: 1619171300,
          createdAt: 1619171200,
        },
        {
          id: 2,
          campaignId,
          audienceId: 2,
          status: SendStatus.DELIVERED,
          sentAt: 1619171300,
          createdAt: 1619171200,
        },
        {
          id: 3,
          campaignId,
          audienceId: 3,
          status: SendStatus.FAILED,
          sentAt: 1619171300,
          createdAt: 1619171200,
        },
      ];

      mockUserCampaignRepository.findOne.mockResolvedValue(campaign);
      mockUserCampaignHistoryRepository.find.mockResolvedValue(history);

      // Act
      const result = await service.findOne(userId, campaignId);

      // Assert
      expect(mockUserCampaignRepository.findOne).toHaveBeenCalledWith({ where: { id: campaignId, userId } });
      expect(mockUserCampaignHistoryRepository.find).toHaveBeenCalledWith({ where: { campaignId } });
      expect(result.id).toEqual(campaign.id);
      expect(result.title).toEqual(campaign.title);
      expect(result.stats).toBeDefined();

      // Kiểm tra stats chỉ khi nó được định nghĩa
      if (result.stats) {
        expect(result.stats.totalRecipients).toEqual(history.length);
        expect(result.stats.sent).toEqual(2);
        expect(result.stats.delivered).toEqual(1);
        expect(result.stats.failed).toEqual(1);
      }
    });

    it('should throw NotFoundException if campaign not found', async () => {
      // Arrange
      const userId = 1;
      const campaignId = 999;

      mockUserCampaignRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findOne(userId, campaignId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a campaign', async () => {
      // Arrange
      const userId = 1;
      const campaignId = 1;
      const updateCampaignDto: UpdateCampaignDto = {
        title: 'Updated Campaign',
      };

      const existingCampaign = {
        id: campaignId,
        userId,
        title: 'Test Campaign',
        description: 'Test Description',
        platform: CampaignPlatform.EMAIL,
        content: '<p>Test Content</p>',
        server: {
          type: ServerType.SMTP,
          name: 'Test Server',
          config: {},
        },
        scheduledAt: null,
        subject: 'Test Subject',
        status: CampaignStatus.DRAFT,
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      const updatedCampaign = {
        ...existingCampaign,
        title: updateCampaignDto.title,
        updatedAt: 1619171300,
      };

      mockUserCampaignRepository.findOne.mockResolvedValue(existingCampaign);
      mockUserCampaignRepository.save.mockResolvedValue(updatedCampaign);

      // Act
      const result = await service.update(userId, campaignId, updateCampaignDto);

      // Assert
      expect(mockUserCampaignRepository.findOne).toHaveBeenCalledWith({ where: { id: campaignId, userId } });
      expect(mockUserCampaignRepository.save).toHaveBeenCalled();
      expect(result.title).toEqual(updateCampaignDto.title);
    });

    it('should throw NotFoundException if campaign not found', async () => {
      // Arrange
      const userId = 1;
      const campaignId = 999;
      const updateCampaignDto: UpdateCampaignDto = {
        title: 'Updated Campaign',
      };

      mockUserCampaignRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.update(userId, campaignId, updateCampaignDto)).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if campaign is not in DRAFT or SCHEDULED status', async () => {
      // Arrange
      const userId = 1;
      const campaignId = 1;
      const updateCampaignDto: UpdateCampaignDto = {
        title: 'Updated Campaign',
      };

      const existingCampaign = {
        id: campaignId,
        userId,
        title: 'Test Campaign',
        description: 'Test Description',
        platform: CampaignPlatform.EMAIL,
        content: '<p>Test Content</p>',
        server: {
          type: ServerType.SMTP,
          name: 'Test Server',
          config: {},
        },
        scheduledAt: null,
        subject: 'Test Subject',
        status: CampaignStatus.RUNNING,
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      mockUserCampaignRepository.findOne.mockResolvedValue(existingCampaign);

      // Act & Assert
      await expect(service.update(userId, campaignId, updateCampaignDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('remove', () => {
    it('should remove a campaign', async () => {
      // Arrange
      const userId = 1;
      const campaignId = 1;
      const campaign = {
        id: campaignId,
        userId,
        title: 'Test Campaign',
        description: 'Test Description',
        platform: CampaignPlatform.EMAIL,
        content: '<p>Test Content</p>',
        server: {
          type: ServerType.SMTP,
          name: 'Test Server',
          config: {},
        },
        scheduledAt: null,
        subject: 'Test Subject',
        status: CampaignStatus.DRAFT,
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      mockUserCampaignRepository.findOne.mockResolvedValue(campaign);
      mockUserCampaignRepository.remove.mockResolvedValue(campaign);
      mockUserCampaignHistoryRepository.delete.mockResolvedValue({});

      // Act
      const result = await service.remove(userId, campaignId);

      // Assert
      expect(mockUserCampaignRepository.findOne).toHaveBeenCalledWith({ where: { id: campaignId, userId } });
      expect(mockUserCampaignHistoryRepository.delete).toHaveBeenCalledWith({ campaignId });
      expect(mockUserCampaignRepository.remove).toHaveBeenCalledWith(campaign);
      expect(result).toBe(true);
    });

    it('should throw NotFoundException if campaign not found', async () => {
      // Arrange
      const userId = 1;
      const campaignId = 999;

      mockUserCampaignRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.remove(userId, campaignId)).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if campaign is in RUNNING status', async () => {
      // Arrange
      const userId = 1;
      const campaignId = 1;
      const campaign = {
        id: campaignId,
        userId,
        title: 'Test Campaign',
        description: 'Test Description',
        platform: CampaignPlatform.EMAIL,
        content: '<p>Test Content</p>',
        server: {
          type: ServerType.SMTP,
          name: 'Test Server',
          config: {},
        },
        scheduledAt: null,
        subject: 'Test Subject',
        status: CampaignStatus.RUNNING,
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      mockUserCampaignRepository.findOne.mockResolvedValue(campaign);

      // Act & Assert
      await expect(service.remove(userId, campaignId)).rejects.toThrow(BadRequestException);
    });
  });

  describe('runCampaign', () => {
    it('should run a campaign successfully', async () => {
      // Arrange
      const userId = 1;
      const campaignId = 1;
      const campaign = {
        id: campaignId,
        userId,
        title: 'Test Campaign',
        description: 'Test Description',
        platform: CampaignPlatform.EMAIL,
        content: '<p>Test Content</p>',
        server: {
          type: ServerType.SMTP,
          name: 'Test Server',
          config: {},
        },
        scheduledAt: null,
        subject: 'Test Subject',
        status: CampaignStatus.DRAFT,
        segmentId: 1,
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      const segment = {
        id: 1,
        userId,
        name: 'Test Segment',
        description: 'Test Description',
        criteria: {},
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      const audiences = [
        {
          id: 1,
          userId,
          email: '<EMAIL>',
          phone: '+84912345678',
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
        {
          id: 2,
          userId,
          email: '<EMAIL>',
          phone: '+84912345679',
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
      ];

      mockUserCampaignRepository.findOne.mockResolvedValue(campaign);
      mockUserSegmentRepository.findOne.mockResolvedValue(segment);
      mockUserSegmentService.getAudiencesInSegment.mockResolvedValue(audiences);
      mockUserCampaignHistoryRepository.save.mockResolvedValue([]);
      mockUserCampaignRepository.save.mockImplementation(c => c);

      // Act
      const result = await service.runCampaign(userId, campaignId);

      // Assert
      expect(mockUserCampaignRepository.findOne).toHaveBeenCalledWith({ where: { id: campaignId, userId } });
      expect(mockUserSegmentRepository.findOne).toHaveBeenCalledWith({ where: { id: campaign.segmentId, userId } });
      expect(mockUserSegmentService.getAudiencesInSegment).toHaveBeenCalledWith(userId, segment);
      expect(mockUserCampaignHistoryRepository.save).toHaveBeenCalled();
      expect(mockUserCampaignRepository.save).toHaveBeenCalledTimes(2);
      expect(result.status).toEqual(CampaignStatus.COMPLETED);
    });

    it('should throw NotFoundException if campaign not found', async () => {
      // Arrange
      const userId = 1;
      const campaignId = 999;

      mockUserCampaignRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.runCampaign(userId, campaignId)).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if campaign is not in DRAFT or SCHEDULED status', async () => {
      // Arrange
      const userId = 1;
      const campaignId = 1;
      const campaign = {
        id: campaignId,
        userId,
        title: 'Test Campaign',
        description: 'Test Description',
        platform: CampaignPlatform.EMAIL,
        content: '<p>Test Content</p>',
        server: {
          type: ServerType.SMTP,
          name: 'Test Server',
          config: {},
        },
        scheduledAt: null,
        subject: 'Test Subject',
        status: CampaignStatus.COMPLETED,
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      mockUserCampaignRepository.findOne.mockResolvedValue(campaign);

      // Act & Assert
      await expect(service.runCampaign(userId, campaignId)).rejects.toThrow(BadRequestException);
    });
  });
});
