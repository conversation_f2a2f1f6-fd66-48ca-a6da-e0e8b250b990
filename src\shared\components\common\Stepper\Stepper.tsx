import React from 'react';
import { useTranslation } from 'react-i18next';
import { Icon, Typography, Button } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';

// Utility function for combining class names
const cn = (...classes: (string | boolean | undefined)[]) => {
  return classes.filter(Boolean).join(' ');
};

export interface StepItem {
  id: string | number;
  title: string;
  titleKey?: string; // For i18n
  description?: string;
  descriptionKey?: string; // For i18n
  icon?: IconName;
  status?: 'waiting' | 'processing' | 'completed' | 'error' | 'skipped';
  optional?: boolean;
  disabled?: boolean;
  content?: React.ReactNode;
}

export type StepperVariant =
  | 'default'
  | 'minimal'
  | 'outlined'
  | 'filled'
  | 'dots'
  | 'arrows'
  | 'cards'
  | 'progress'
  | 'timeline'
  | 'numbered'
  | 'icons';

export type StepperSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type StepperOrientation = 'horizontal' | 'vertical';
export type StepperColorScheme = 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';

export interface StepperProps {
  steps: StepItem[];
  currentStep?: number;
  variant?: StepperVariant;
  size?: StepperSize;
  orientation?: StepperOrientation;
  colorScheme?: StepperColorScheme;
  showConnector?: boolean;
  showStepNumbers?: boolean;
  showStepIcons?: boolean;
  showStepContent?: boolean;
  showNavigation?: boolean;
  allowStepClick?: boolean;
  responsive?: boolean;
  animated?: boolean;
  className?: string;
  stepClassName?: string;
  connectorClassName?: string;
  contentClassName?: string;
  onStepClick?: (stepId: string | number, stepIndex: number) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  onComplete?: () => void;
  nextLabel?: string;
  previousLabel?: string;
  completeLabel?: string;
}

const Stepper: React.FC<StepperProps> = ({
  steps,
  currentStep = 0,
  variant = 'default',
  size = 'md',
  orientation = 'horizontal',
  colorScheme = 'primary',
  showConnector = true,
  showStepNumbers = true,
  showStepIcons = false,
  showStepContent = false,
  showNavigation = false,
  allowStepClick = false,
  responsive = true,
  animated = true,
  className = '',
  stepClassName = '',
  connectorClassName = '',
  contentClassName = '',
  onStepClick,
  onNext,
  onPrevious,
  onComplete,
  nextLabel,
  previousLabel,
  completeLabel,
}) => {
  const { t } = useTranslation();

  // Get step status
  const getStepStatus = (stepIndex: number, step: StepItem) => {
    if (step.status) return step.status;
    if (stepIndex < currentStep) return 'completed';
    if (stepIndex === currentStep) return 'processing';
    return 'waiting';
  };

  // Get color scheme classes
  const getColorClasses = () => {
    const colors = {
      primary: {
        active: 'bg-primary text-primary-foreground border-primary',
        completed: 'bg-primary text-primary-foreground border-primary',
        waiting: 'bg-muted text-muted-foreground border-border',
        error: 'bg-destructive text-destructive-foreground border-destructive',
        connector: 'bg-primary',
        connectorInactive: 'bg-border',
      },
      secondary: {
        active: 'bg-secondary text-secondary-foreground border-secondary',
        completed: 'bg-secondary text-secondary-foreground border-secondary',
        waiting: 'bg-muted text-muted-foreground border-border',
        error: 'bg-destructive text-destructive-foreground border-destructive',
        connector: 'bg-secondary',
        connectorInactive: 'bg-border',
      },
      success: {
        active: 'bg-green-500 text-white border-green-500',
        completed: 'bg-green-500 text-white border-green-500',
        waiting: 'bg-muted text-muted-foreground border-border',
        error: 'bg-destructive text-destructive-foreground border-destructive',
        connector: 'bg-green-500',
        connectorInactive: 'bg-border',
      },
      warning: {
        active: 'bg-yellow-500 text-white border-yellow-500',
        completed: 'bg-yellow-500 text-white border-yellow-500',
        waiting: 'bg-muted text-muted-foreground border-border',
        error: 'bg-destructive text-destructive-foreground border-destructive',
        connector: 'bg-yellow-500',
        connectorInactive: 'bg-border',
      },
      danger: {
        active: 'bg-destructive text-destructive-foreground border-destructive',
        completed: 'bg-destructive text-destructive-foreground border-destructive',
        waiting: 'bg-muted text-muted-foreground border-border',
        error: 'bg-destructive text-destructive-foreground border-destructive',
        connector: 'bg-destructive',
        connectorInactive: 'bg-border',
      },
      info: {
        active: 'bg-blue-500 text-white border-blue-500',
        completed: 'bg-blue-500 text-white border-blue-500',
        waiting: 'bg-muted text-muted-foreground border-border',
        error: 'bg-destructive text-destructive-foreground border-destructive',
        connector: 'bg-blue-500',
        connectorInactive: 'bg-border',
      },
    };
    return colors[colorScheme];
  };

  // Get size classes
  const getSizeClasses = () => {
    const sizes = {
      xs: {
        step: 'w-6 h-6 text-xs',
        dot: 'w-2 h-2',
        title: 'text-xs',
        description: 'text-xs',
        spacing: 'gap-2',
        padding: 'p-1',
      },
      sm: {
        step: 'w-8 h-8 text-sm',
        dot: 'w-3 h-3',
        title: 'text-sm',
        description: 'text-xs',
        spacing: 'gap-3',
        padding: 'p-2',
      },
      md: {
        step: 'w-10 h-10 text-base',
        dot: 'w-4 h-4',
        title: 'text-base',
        description: 'text-sm',
        spacing: 'gap-4',
        padding: 'p-3',
      },
      lg: {
        step: 'w-12 h-12 text-lg',
        dot: 'w-5 h-5',
        title: 'text-lg',
        description: 'text-base',
        spacing: 'gap-5',
        padding: 'p-4',
      },
      xl: {
        step: 'w-16 h-16 text-xl',
        dot: 'w-6 h-6',
        title: 'text-xl',
        description: 'text-lg',
        spacing: 'gap-6',
        padding: 'p-5',
      },
    };
    return sizes[size];
  };

  // Get step icon
  const getStepIcon = (step: StepItem, stepIndex: number, status: string) => {
    const sizeClasses = getSizeClasses();
    const iconSize = size === 'xs' ? 'sm' : size === 'xl' ? 'lg' : 'md';

    // For dots variant, return null (dots are handled by styling)
    if (variant === 'dots') {
      return null;
    }

    // For icons variant, always show icon
    if (variant === 'icons' && step.icon) {
      return <Icon name={step.icon} size={iconSize} />;
    }

    if (step.icon && showStepIcons) {
      return <Icon name={step.icon} size={iconSize} />;
    }

    switch (status) {
      case 'completed':
        return <Icon name="check" size={iconSize} />;
      case 'error':
        return <Icon name="x" size={iconSize} />;
      case 'processing':
        return animated ? (
          <Icon name="loading" size={iconSize} className="animate-spin" />
        ) : showStepNumbers ? (
          stepIndex + 1
        ) : (
          <Icon name="loading" size={iconSize} />
        );
      case 'skipped':
        return <Icon name="chevron-right" size={iconSize} />;
      default:
        return showStepNumbers ? stepIndex + 1 : <div className={cn('rounded-full', sizeClasses.dot)} />;
    }
  };

  // Get variant-specific classes
  const getVariantClasses = (status: string) => {
    const colorClasses = getColorClasses();
    const sizeClasses = getSizeClasses();

    const baseClasses = {
      processing: colorClasses.active,
      completed: colorClasses.completed,
      waiting: colorClasses.waiting,
      error: colorClasses.error,
      skipped: colorClasses.waiting,
    };

    switch (variant) {
      case 'minimal':
        return cn(
          'border-0 bg-transparent',
          status === 'processing' && 'text-primary',
          status === 'completed' && 'text-primary',
          status === 'waiting' && 'text-muted-foreground',
          status === 'error' && 'text-destructive'
        );

      case 'outlined':
        return cn(
          'bg-background border-2',
          status === 'processing' && 'border-primary text-primary',
          status === 'completed' && 'border-primary text-primary',
          status === 'waiting' && 'border-border text-muted-foreground',
          status === 'error' && 'border-destructive text-destructive'
        );

      case 'filled':
        return baseClasses[status as keyof typeof baseClasses];

      case 'dots':
        return cn(
          'border-0 rounded-full',
          sizeClasses.dot,
          baseClasses[status as keyof typeof baseClasses]
        );

      case 'arrows':
        return cn(
          'relative bg-background border-2 rounded-none',
          'before:content-[""] before:absolute before:right-[-8px] before:top-1/2 before:transform before:-translate-y-1/2',
          'before:w-0 before:h-0 before:border-l-8 before:border-t-8 before:border-b-8',
          'before:border-t-transparent before:border-b-transparent',
          status === 'processing' && 'border-primary before:border-l-primary',
          status === 'completed' && 'border-primary before:border-l-primary',
          status === 'waiting' && 'border-border before:border-l-border',
          status === 'error' && 'border-destructive before:border-l-destructive'
        );

      case 'cards':
        return cn(
          'rounded-lg border shadow-sm p-2',
          baseClasses[status as keyof typeof baseClasses]
        );

      case 'progress':
        return cn(
          'relative overflow-hidden',
          baseClasses[status as keyof typeof baseClasses],
          status === 'processing' && 'after:content-[""] after:absolute after:inset-0 after:bg-gradient-to-r after:from-transparent after:via-white/20 after:to-transparent after:animate-pulse'
        );

      case 'timeline':
        return cn(
          'relative',
          baseClasses[status as keyof typeof baseClasses]
        );

      case 'numbered':
        return cn(
          'font-bold text-lg',
          baseClasses[status as keyof typeof baseClasses]
        );

      default:
        return baseClasses[status as keyof typeof baseClasses];
    }
  };

  // Handle step click
  const handleStepClick = (stepId: string | number, stepIndex: number) => {
    if (allowStepClick && !steps[stepIndex].disabled && onStepClick) {
      onStepClick(stepId, stepIndex);
    }
  };

  // Handle navigation
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      onNext?.();
    } else {
      onComplete?.();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      onPrevious?.();
    }
  };

  // Get responsive classes
  const getResponsiveClasses = () => {
    if (!responsive) return '';
    return orientation === 'horizontal'
      ? 'flex-col sm:flex-row'
      : 'flex-col';
  };

  return (
    <div className={cn('stepper', className)}>
      {/* Steps Container */}
      <div
        className={cn(
          'flex',
          orientation === 'horizontal' ? getResponsiveClasses() : 'flex-col',
          orientation === 'horizontal' ? 'items-center' : 'items-start',
          getSizeClasses().spacing
        )}
      >
        {steps.map((step, stepIndex) => {
          const status = getStepStatus(stepIndex, step);
          const colorClasses = getColorClasses();
          const sizeClasses = getSizeClasses();
          const isLast = stepIndex === steps.length - 1;
          const isClickable = allowStepClick && !step.disabled;

          return (
            <div key={step.id} className={cn(
              'flex-1',
              orientation === 'horizontal' ? 'min-w-0' : ''
            )}>
              {/* Step Item */}
              <div
                className={cn(
                  'flex items-center',
                  orientation === 'vertical' ? 'flex-row' : responsive ? 'flex-col sm:flex-row' : 'flex-row',
                  isClickable && 'cursor-pointer',
                  stepClassName
                )}
                onClick={() => handleStepClick(step.id, stepIndex)}
              >
                {/* Step Circle/Icon */}
                <div
                  className={cn(
                    'flex items-center justify-center transition-all duration-200 flex-shrink-0',
                    variant === 'dots' ? 'rounded-full' : variant === 'arrows' ? '' : 'rounded-full border-2',
                    variant !== 'dots' && sizeClasses.step,
                    getVariantClasses(status),
                    animated && 'transition-all duration-300'
                  )}
                >
                  {getStepIcon(step, stepIndex, status)}
                </div>

                {/* Step Content */}
                <div
                  className={cn(
                    'flex flex-col',
                    orientation === 'horizontal' ? 'ml-0 sm:ml-3 mt-2 sm:mt-0' : 'ml-3',
                    variant === 'minimal' && 'ml-2'
                  )}
                >
                  <Typography
                    variant="body2"
                    className={cn(
                      'font-medium',
                      sizeClasses.title,
                      // Dark theme support
                      className?.includes('stepper-dark') && 'text-white',
                      !className?.includes('stepper-dark') && status === 'processing' && 'text-foreground',
                      !className?.includes('stepper-dark') && status === 'completed' && 'text-foreground',
                      !className?.includes('stepper-dark') && status === 'waiting' && 'text-muted-foreground',
                      !className?.includes('stepper-dark') && status === 'error' && 'text-destructive',
                      // Dark theme specific colors
                      className?.includes('stepper-dark') && status === 'processing' && 'text-white',
                      className?.includes('stepper-dark') && status === 'completed' && 'text-white',
                      className?.includes('stepper-dark') && status === 'waiting' && 'text-gray-300',
                      className?.includes('stepper-dark') && status === 'error' && 'text-red-300'
                    )}
                  >
                    {step.titleKey ? t(step.titleKey) : step.title}
                    {step.optional && (
                      <span className={cn(
                        'text-xs ml-1',
                        className?.includes('stepper-dark') ? 'text-gray-400' : 'text-muted-foreground'
                      )}>
                        ({t('common.optional', 'Tùy chọn')})
                      </span>
                    )}
                  </Typography>

                  {step.description && (
                    <Typography
                      variant="caption"
                      className={cn(
                        'mt-1',
                        sizeClasses.description,
                        className?.includes('stepper-dark') ? 'text-gray-400' : 'text-muted-foreground'
                      )}
                    >
                      {step.descriptionKey ? t(step.descriptionKey) : step.description}
                    </Typography>
                  )}
                </div>

                {/* Connector */}
                {!isLast && showConnector && orientation === 'horizontal' && (
                  <div
                    className={cn(
                      'flex-1 h-0.5 mx-4 min-w-8',
                      status === 'completed' ? colorClasses.connector : colorClasses.connectorInactive,
                      connectorClassName,
                      responsive && 'hidden sm:block'
                    )}
                  />
                )}
              </div>

              {/* Vertical Connector */}
              {!isLast && showConnector && orientation === 'vertical' && (
                <div
                  className={cn(
                    'w-0.5 h-8 ml-5 my-2',
                    status === 'completed' ? colorClasses.connector : colorClasses.connectorInactive,
                    connectorClassName
                  )}
                />
              )}

              {/* Step Content */}
              {showStepContent && step.content && stepIndex === currentStep && (
                <div className={cn('mt-4', sizeClasses.padding, contentClassName)}>
                  {step.content}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Navigation */}
      {showNavigation && (
        <div className="flex justify-between mt-6">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 0}
            leftIcon={<Icon name="chevron-left" size="sm" />}
          >
            {previousLabel || t('common.previous', 'Quay lại')}
          </Button>

          <Button
            variant="primary"
            onClick={handleNext}
            rightIcon={
              currentStep === steps.length - 1 ? (
                <Icon name="check" size="sm" />
              ) : (
                <Icon name="chevron-right" size="sm" />
              )
            }
          >
            {currentStep === steps.length - 1
              ? completeLabel || t('common.complete', 'Hoàn thành')
              : nextLabel || t('common.next', 'Tiếp theo')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default Stepper;
