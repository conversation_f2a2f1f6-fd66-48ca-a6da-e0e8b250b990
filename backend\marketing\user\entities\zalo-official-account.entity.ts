import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng zalo_official_accounts trong cơ sở dữ liệu
 * Lưu trữ thông tin về các Official Account của Zalo mà người dùng đã kết nối với hệ thống
 */
@Entity('zalo_official_accounts')
export class ZaloOfficialAccount {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID người dùng sở hữu Official Account
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * ID của Official Account trên Zalo
   */
  @Column({ name: 'oa_id', length: 50 })
  oaId: string;

  /**
   * Tên của Official Account
   */
  @Column({ name: 'name', length: 255 })
  name: string;

  /**
   * <PERSON>ô tả của Official Account
   */
  @Column({ name: 'description', length: 500, nullable: true })
  description: string;

  /**
   * URL avatar của Official Account
   */
  @Column({ name: 'avatar_url', length: 500, nullable: true })
  avatarUrl: string;

  /**
   * Access token của Official Account
   */
  @Column({ name: 'access_token', length: 500 })
  accessToken: string;

  /**
   * Refresh token của Official Account
   */
  @Column({ name: 'refresh_token', length: 500, nullable: true })
  refreshToken: string;

  /**
   * Thời gian hết hạn của access token (Unix timestamp)
   */
  @Column({ name: 'expires_at', type: 'bigint' })
  expiresAt: number;

  /**
   * ID của agent được kết nối với Official Account
   */
  @Column({ name: 'agent_id', nullable: true })
  agentId: number;

  /**
   * Trạng thái kết nối (active, inactive)
   */
  @Column({ name: 'status', length: 20, default: 'active' })
  status: string;

  /**
   * Thời điểm tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
