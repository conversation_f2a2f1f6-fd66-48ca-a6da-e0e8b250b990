import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { UserAudienceCustomFieldDefinitionService } from '../services/user-audience-custom-field-definition.service';
import {
  CreateAudienceCustomFieldDefinitionDto,
  UpdateAudienceCustomFieldDefinitionDto,
  AudienceCustomFieldDefinitionResponseDto,
  AudienceCustomFieldDefinitionQueryDto,
} from '../dto/audience-custom-field-definition';
import { PaginatedResponseDto } from '../dto/common';
import { ApiResponseDto } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers/response.helper';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';

/**
 * Controller xử lý các API liên quan đến trường tùy chỉnh của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.MARKETING_USER)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/marketing/audience-custom-fields')
export class UserAudienceCustomFieldDefinitionController {
  constructor(private readonly customFieldService: UserAudienceCustomFieldDefinitionService) {}

  /**
   * Tạo mới trường tùy chỉnh
   * @param user Thông tin người dùng
   * @param createDto Dữ liệu tạo mới
   * @returns Thông tin trường tùy chỉnh đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới trường tùy chỉnh' })
  @ApiResponse({
    status: 201,
    description: 'Trường tùy chỉnh đã được tạo thành công',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_ALREADY_EXISTS,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
  )
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateAudienceCustomFieldDefinitionDto,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    const result = await this.customFieldService.create(user.id, createDto);
    return wrapResponse(result, 'Tạo trường tùy chỉnh thành công');
  }

  /**
   * Cập nhật trường tùy chỉnh
   * @param user Thông tin người dùng
   * @param id ID của trường tùy chỉnh
   * @param updateDto Dữ liệu cập nhật
   * @returns Thông tin trường tùy chỉnh đã cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Trường tùy chỉnh đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
  )
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: number,
    @Body() updateDto: UpdateAudienceCustomFieldDefinitionDto,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    const result = await this.customFieldService.update(user.id, id, updateDto);
    return wrapResponse(result, 'Cập nhật trường tùy chỉnh thành công');
  }

  /**
   * Xóa trường tùy chỉnh
   * @param user Thông tin người dùng
   * @param id ID của trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh đã xóa
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Trường tùy chỉnh đã được xóa thành công',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_DELETION_FAILED,
  )
  async delete(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: number,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    const result = await this.customFieldService.delete(user.id, id);
    return wrapResponse(result, 'Xóa trường tùy chỉnh thành công');
  }

  /**
   * Lấy thông tin trường tùy chỉnh
   * @param user Thông tin người dùng
   * @param id ID của trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin trường tùy chỉnh',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  @ApiErrorResponse(MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND)
  async findOne(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: number,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    const result = await this.customFieldService.findOne(user.id, id);
    return wrapResponse(result);
  }

  /**
   * Lấy danh sách trường tùy chỉnh
   * @param user Thông tin người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách trường tùy chỉnh
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách trường tùy chỉnh',
    schema: ApiResponseDto.getPaginatedSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: AudienceCustomFieldDefinitionQueryDto,
  ): Promise<ApiResponseDto<PaginatedResponseDto<AudienceCustomFieldDefinitionResponseDto>>> {
    const result = await this.customFieldService.findAll(user.id, queryDto);
    return wrapResponse(result);
  }
}
