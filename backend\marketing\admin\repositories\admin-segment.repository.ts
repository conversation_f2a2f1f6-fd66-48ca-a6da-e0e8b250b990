import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { AdminSegment } from '../entities/admin-segment.entity';

/**
 * Repository cho AdminSegment
 */
@Injectable()
export class AdminSegmentRepository {
  constructor(
    @InjectRepository(AdminSegment)
    private readonly repository: Repository<AdminSegment>,
  ) {}

  /**
   * Tìm kiếm nhiều segment
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách segment
   */
  async find(options?: FindManyOptions<AdminSegment>): Promise<AdminSegment[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một segment
   * @param options Tùy chọn tìm kiếm
   * @returns Segment hoặc null
   */
  async findOne(options?: FindOneOptions<AdminSegment>): Promise<AdminSegment | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Lưu segment
   * @param segment Segment cần lưu
   * @returns Segment đã lưu
   */
  async save(segment: AdminSegment): Promise<AdminSegment>;
  async save(segment: AdminSegment[]): Promise<AdminSegment[]>;
  async save(segment: AdminSegment | AdminSegment[]): Promise<AdminSegment | AdminSegment[]> {
    return this.repository.save(segment as any);
  }

  /**
   * Xóa segment
   * @param segment Segment cần xóa
   * @returns Segment đã xóa
   */
  async remove(segment: AdminSegment): Promise<AdminSegment>;
  async remove(segment: AdminSegment[]): Promise<AdminSegment[]>;
  async remove(segment: AdminSegment | AdminSegment[]): Promise<AdminSegment | AdminSegment[]> {
    return this.repository.remove(segment as any);
  }

  /**
   * Đếm số lượng segment
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng segment
   */
  async count(options?: FindManyOptions<AdminSegment>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }
}
