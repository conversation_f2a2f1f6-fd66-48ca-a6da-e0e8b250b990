import { ApiProperty } from '@nestjs/swagger';

/**
 * Enum cho loại trường tùy chỉnh
 */
export enum CustomFieldType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  DATE = 'DATE',
  BOOLEAN = 'BOOLEAN',
}

/**
 * DTO cho phản hồi trường tùy chỉnh
 */
export class CustomFieldResponseDto {
  /**
   * ID của trường tùy chỉnh
   * @example 1
   */
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 1,
  })
  id: number;

  /**
   * ID của audience
   * @example 1
   */
  @ApiProperty({
    description: 'ID của audience',
    example: 1,
  })
  audienceId: number;

  /**
   * Tên trường tùy chỉnh
   * @example "Địa chỉ"
   */
  @ApiProperty({
    description: 'Tên trường tùy chỉnh',
    example: 'Địa chỉ',
  })
  fieldName: string;

  /**
   * Gi<PERSON> trị trường tùy chỉnh
   * @example "Hà Nội, Việt Nam"
   */
  @ApiProperty({
    description: 'Giá trị trường tùy chỉnh',
    example: 'Hà Nội, Việt Nam',
  })
  fieldValue: string;

  /**
   * Loại trường tùy chỉnh
   * @example "TEXT"
   */
  @ApiProperty({
    description: 'Loại trường tùy chỉnh',
    enum: CustomFieldType,
    example: CustomFieldType.TEXT,
  })
  fieldType: CustomFieldType;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1619171200,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}
