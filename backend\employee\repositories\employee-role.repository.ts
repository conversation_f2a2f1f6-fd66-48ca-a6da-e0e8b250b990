import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { EmployeeRole, Permission } from '@modules/employee/entities';

/**
 * Repository cho EmployeeRole
 */
@Injectable()
export class EmployeeRoleRepository {
  constructor(
    @InjectRepository(EmployeeRole)
    private readonly repository: Repository<EmployeeRole>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
  ) {}

  /**
   * Tìm vai trò theo ID
   * @param id ID của vai trò
   * @returns Vai trò
   */
  async findById(id: number): Promise<EmployeeRole> {
    const role = await this.repository.findOne({
      where: { id },
      relations: ['permissions'],
    });

    if (!role) {
      throw new NotFoundException(`Vai trò với ID "${id}" không tồn tại`);
    }

    return role;
  }

  /**
   * <PERSON><PERSON><PERSON> tất cả vai trò
   * @returns Danh sách tất cả vai trò
   */
  async findAll(): Promise<EmployeeRole[]> {
    return this.repository.find({
      relations: ['permissions'],
    });
  }

  /**
   * Gán quyền cho vai trò
   * @param roleId ID của vai trò
   * @param permissionIds Danh sách ID của các quyền
   * @returns Vai trò đã được cập nhật với các quyền mới
   */
  async assignPermissionsToRole(roleId: number, permissionIds: number[]): Promise<EmployeeRole> {
    // Tìm vai trò theo ID
    const role = await this.findById(roleId);

    // Tìm các quyền theo danh sách ID
    const permissions = await this.permissionRepository.find({
      where: { id: In(permissionIds) },
    });

    // Kiểm tra xem tất cả các quyền có tồn tại không
    if (permissions.length !== permissionIds.length) {
      const foundIds = permissions.map(p => p.id);
      const missingIds = permissionIds.filter(id => !foundIds.includes(id));
      throw new NotFoundException(`Không tìm thấy quyền với ID: ${missingIds.join(', ')}`);
    }

    // Gán quyền mới cho vai trò
    role.permissions = permissions;

    // Lưu thay đổi vào cơ sở dữ liệu
    return this.repository.save(role);
  }

  /**
   * Lấy tất cả quyền của vai trò
   * @param roleId ID của vai trò
   * @returns Danh sách các quyền của vai trò
   */
  async findPermissionsByRoleId(roleId: number): Promise<Permission[]> {
    const role = await this.findById(roleId);
    return role.permissions;
  }
}
