import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { AdminTag } from '../entities/admin-tag.entity';

/**
 * Repository cho AdminTag
 */
@Injectable()
export class AdminTagRepository {
  constructor(
    @InjectRepository(AdminTag)
    private readonly repository: Repository<AdminTag>,
  ) {}

  /**
   * Tìm kiếm nhiều tag
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách tag
   */
  async find(options?: FindManyOptions<AdminTag>): Promise<AdminTag[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một tag
   * @param options Tùy chọn tìm kiếm
   * @returns Tag hoặc null
   */
  async findOne(options?: FindOneOptions<AdminTag>): Promise<AdminTag | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Lưu tag
   * @param tag Tag cần lưu
   * @returns Tag đã lưu
   */
  async save(tag: AdminTag): Promise<AdminTag>;
  async save(tag: AdminTag[]): Promise<AdminTag[]>;
  async save(tag: AdminTag | AdminTag[]): Promise<AdminTag | AdminTag[]> {
    return this.repository.save(tag as any);
  }

  /**
   * Xóa tag
   * @param tag Tag cần xóa
   * @returns Tag đã xóa
   */
  async remove(tag: AdminTag): Promise<AdminTag>;
  async remove(tag: AdminTag[]): Promise<AdminTag[]>;
  async remove(tag: AdminTag | AdminTag[]): Promise<AdminTag | AdminTag[]> {
    return this.repository.remove(tag as any);
  }

  /**
   * Đếm số lượng tag
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng tag
   */
  async count(options?: FindManyOptions<AdminTag>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }
}
