import { ValidationRule, ValidationResult, ValidationContext } from './types';
import { VALIDATION_PATTERNS, VIETNAMESE_PATTERNS, BUSINESS_PATTERNS } from './patterns';

/**
 * Create a validation result
 */
const createResult = (
  isValid: boolean,
  field: string,
  message?: string,
  code?: string,
  severity: 'error' | 'warning' | 'info' = 'error'
): ValidationResult => ({
  isValid,
  errors: !isValid && severity === 'error' ? [{ field, message: message || '', code, severity }] : [],
  warnings: !isValid && severity === 'warning' ? [{ field, message: message || '', code, severity }] : [],
  infos: !isValid && severity === 'info' ? [{ field, message: message || '', code, severity }] : [],
});

/**
 * Basic validation rules
 */
export const ValidationRules = {
  /**
   * Required field validation
   */
  required: (message = 'This field is required'): ValidationRule => ({
    name: 'required',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      const isEmpty = value === null || value === undefined ||
        (typeof value === 'string' && value.trim() === '') ||
        (Array.isArray(value) && value.length === 0);

      return createResult(!isEmpty, context?.field || '', message, 'REQUIRED');
    },
    message,
    severity: 'error',
  }),

  /**
   * Minimum length validation
   */
  minLength: (min: number, message?: string): ValidationRule => ({
    name: 'minLength',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined) return createResult(true, context?.field || '');

      const length = typeof value === 'string' ? value.length :
        Array.isArray(value) ? value.length : 0;

      const isValid = length >= min;
      const defaultMessage = `Must be at least ${min} characters long`;

      return createResult(isValid, context?.field || '', message || defaultMessage, 'MIN_LENGTH');
    },
    message,
    severity: 'error',
  }),

  /**
   * Maximum length validation
   */
  maxLength: (max: number, message?: string): ValidationRule => ({
    name: 'maxLength',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined) return createResult(true, context?.field || '');

      const length = typeof value === 'string' ? value.length :
        Array.isArray(value) ? value.length : 0;

      const isValid = length <= max;
      const defaultMessage = `Must be at most ${max} characters long`;

      return createResult(isValid, context?.field || '', message || defaultMessage, 'MAX_LENGTH');
    },
    message,
    severity: 'error',
  }),

  /**
   * Minimum value validation
   */
  min: (min: number, message?: string): ValidationRule => ({
    name: 'min',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined) return createResult(true, context?.field || '');

      const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
      if (isNaN(numValue)) return createResult(false, context?.field || '', 'Must be a valid number', 'INVALID_NUMBER');

      const isValid = numValue >= min;
      const defaultMessage = `Must be at least ${min}`;

      return createResult(isValid, context?.field || '', message || defaultMessage, 'MIN_VALUE');
    },
    message,
    severity: 'error',
  }),

  /**
   * Maximum value validation
   */
  max: (max: number, message?: string): ValidationRule => ({
    name: 'max',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined) return createResult(true, context?.field || '');

      const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
      if (isNaN(numValue)) return createResult(false, context?.field || '', 'Must be a valid number', 'INVALID_NUMBER');

      const isValid = numValue <= max;
      const defaultMessage = `Must be at most ${max}`;

      return createResult(isValid, context?.field || '', message || defaultMessage, 'MAX_VALUE');
    },
    message,
    severity: 'error',
  }),

  /**
   * Email validation
   */
  email: (message = 'Must be a valid email address'): ValidationRule => ({
    name: 'email',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const isValid = typeof value === 'string' && VALIDATION_PATTERNS.email.test(value);

      return createResult(isValid, context?.field || '', message, 'INVALID_EMAIL');
    },
    message,
    severity: 'error',
  }),

  /**
   * URL validation
   */
  url: (message = 'Must be a valid URL'): ValidationRule => ({
    name: 'url',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const isValid = typeof value === 'string' && VALIDATION_PATTERNS.url.test(value);

      return createResult(isValid, context?.field || '', message, 'INVALID_URL');
    },
    message,
    severity: 'error',
  }),

  /**
   * Phone number validation
   */
  phone: (message = 'Must be a valid phone number'): ValidationRule => ({
    name: 'phone',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const isValid = typeof value === 'string' && VALIDATION_PATTERNS.phone.test(value);

      return createResult(isValid, context?.field || '', message, 'INVALID_PHONE');
    },
    message,
    severity: 'error',
  }),

  /**
   * Vietnamese phone number validation
   */
  phoneVN: (message = 'Must be a valid Vietnamese phone number'): ValidationRule => ({
    name: 'phoneVN',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const isValid = typeof value === 'string' && VIETNAMESE_PATTERNS.phoneVN.test(value);

      return createResult(isValid, context?.field || '', message, 'INVALID_PHONE_VN');
    },
    message,
    severity: 'error',
  }),

  /**
   * Pattern validation
   */
  pattern: (regex: RegExp, message = 'Invalid format'): ValidationRule => ({
    name: 'pattern',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const isValid = typeof value === 'string' && regex.test(value);

      return createResult(isValid, context?.field || '', message, 'INVALID_PATTERN');
    },
    message,
    severity: 'error',
  }),

  /**
   * Numeric validation
   */
  numeric: (message = 'Must be a valid number'): ValidationRule => ({
    name: 'numeric',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
      const isValid = !isNaN(numValue) && isFinite(numValue);

      return createResult(isValid, context?.field || '', message, 'INVALID_NUMBER');
    },
    message,
    severity: 'error',
  }),

  /**
   * Integer validation
   */
  integer: (message = 'Must be a valid integer'): ValidationRule => ({
    name: 'integer',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
      const isValid = !isNaN(numValue) && Number.isInteger(numValue);

      return createResult(isValid, context?.field || '', message, 'INVALID_INTEGER');
    },
    message,
    severity: 'error',
  }),

  /**
   * Positive number validation
   */
  positive: (message = 'Must be a positive number'): ValidationRule => ({
    name: 'positive',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
      if (isNaN(numValue)) return createResult(false, context?.field || '', 'Must be a valid number', 'INVALID_NUMBER');

      const isValid = numValue > 0;

      return createResult(isValid, context?.field || '', message, 'NOT_POSITIVE');
    },
    message,
    severity: 'error',
  }),

  /**
   * Date validation
   */
  date: (message = 'Must be a valid date'): ValidationRule => ({
    name: 'date',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const date = new Date(value as string);
      const isValid = !isNaN(date.getTime());

      return createResult(isValid, context?.field || '', message, 'INVALID_DATE');
    },
    message,
    severity: 'error',
  }),

  /**
   * Future date validation
   */
  futureDate: (message = 'Must be a future date'): ValidationRule => ({
    name: 'futureDate',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const date = new Date(value as string);
      if (isNaN(date.getTime())) return createResult(false, context?.field || '', 'Must be a valid date', 'INVALID_DATE');

      const isValid = date > new Date();

      return createResult(isValid, context?.field || '', message, 'NOT_FUTURE_DATE');
    },
    message,
    severity: 'error',
  }),

  /**
   * Past date validation
   */
  pastDate: (message = 'Must be a past date'): ValidationRule => ({
    name: 'pastDate',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const date = new Date(value as string);
      if (isNaN(date.getTime())) return createResult(false, context?.field || '', 'Must be a valid date', 'INVALID_DATE');

      const isValid = date < new Date();

      return createResult(isValid, context?.field || '', message, 'NOT_PAST_DATE');
    },
    message,
    severity: 'error',
  }),

  /**
   * Age validation
   */
  minAge: (minAge: number, message?: string): ValidationRule => ({
    name: 'minAge',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const birthDate = new Date(value as string);
      if (isNaN(birthDate.getTime())) return createResult(false, context?.field || '', 'Must be a valid date', 'INVALID_DATE');

      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      const actualAge = monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())
        ? age - 1
        : age;

      const isValid = actualAge >= minAge;
      const defaultMessage = `Must be at least ${minAge} years old`;

      return createResult(isValid, context?.field || '', message || defaultMessage, 'MIN_AGE_NOT_MET');
    },
    message,
    severity: 'error',
  }),

  /**
   * Password strength validation
   */
  strongPassword: (message = 'Password must contain at least 8 characters, including uppercase, lowercase, number and special character'): ValidationRule => ({
    name: 'strongPassword',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const isValid = typeof value === 'string' && VALIDATION_PATTERNS.strongPassword.test(value);

      return createResult(isValid, context?.field || '', message, 'WEAK_PASSWORD');
    },
    message,
    severity: 'error',
  }),

  /**
   * Confirm password validation
   */
  confirmPassword: (passwordField: string, message = 'Passwords do not match'): ValidationRule => ({
    name: 'confirmPassword',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const password = context?.formValues[passwordField];
      const isValid = value === password;

      return createResult(isValid, context?.field || '', message, 'PASSWORD_MISMATCH');
    },
    message,
    severity: 'error',
    dependencies: [passwordField],
  }),

  /**
   * File size validation
   */
  fileSize: (maxSizeInBytes: number, message?: string): ValidationRule => ({
    name: 'fileSize',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined) return createResult(true, context?.field || '');

      if (!(value instanceof File)) return createResult(false, context?.field || '', 'Must be a file', 'INVALID_FILE');

      const isValid = value.size <= maxSizeInBytes;
      const defaultMessage = `File size must be less than ${Math.round(maxSizeInBytes / 1024 / 1024 * 100) / 100}MB`;

      return createResult(isValid, context?.field || '', message || defaultMessage, 'FILE_TOO_LARGE');
    },
    message,
    severity: 'error',
  }),

  /**
   * File type validation
   */
  fileType: (allowedTypes: string[], message?: string): ValidationRule => ({
    name: 'fileType',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined) return createResult(true, context?.field || '');

      if (!(value instanceof File)) return createResult(false, context?.field || '', 'Must be a file', 'INVALID_FILE');

      const isValid = allowedTypes.includes(value.type);
      const defaultMessage = `File type must be one of: ${allowedTypes.join(', ')}`;

      return createResult(isValid, context?.field || '', message || defaultMessage, 'INVALID_FILE_TYPE');
    },
    message,
    severity: 'error',
  }),
};

/**
 * Composite validation rules
 */
export const CompositeRules = {
  /**
   * Vietnamese citizen ID validation
   */
  citizenIdVN: (): ValidationRule => ({
    name: 'citizenIdVN',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const isValid = typeof value === 'string' && VIETNAMESE_PATTERNS.citizenIdVN.test(value);

      return createResult(isValid, context?.field || '', 'Must be a valid Vietnamese citizen ID', 'INVALID_CITIZEN_ID_VN');
    },
    severity: 'error',
  }),

  /**
   * Vietnamese name validation
   */
  nameVN: (): ValidationRule => ({
    name: 'nameVN',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const isValid = typeof value === 'string' && VIETNAMESE_PATTERNS.nameVN.test(value);

      return createResult(isValid, context?.field || '', 'Must be a valid Vietnamese name', 'INVALID_NAME_VN');
    },
    severity: 'error',
  }),

  /**
   * Currency validation
   */
  currency: (): ValidationRule => ({
    name: 'currency',
    validator: (value: unknown, context?: ValidationContext): ValidationResult => {
      if (value === null || value === undefined || value === '') return createResult(true, context?.field || '');

      const isValid = typeof value === 'string' && BUSINESS_PATTERNS.currency.test(value);

      return createResult(isValid, context?.field || '', 'Must be a valid currency amount', 'INVALID_CURRENCY');
    },
    severity: 'error',
  }),
};
