import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  Button,
  Card,
  EmptyState,
  FormItem,
  Icon,
  Input,
  Pagination,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Interface cho item URL
 */
interface Url {
  id: string;
  title: string;
  url: string;
  description?: string;
  category?: string;
  createdAt: string;
  lastChecked?: string;
  status?: 'active' | 'broken' | 'pending';
}

/**
 * Props cho component UrlSlideInForm
 */
interface UrlSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi chọn các URL
   */
  onSelect: (selectedUrls: Url[]) => void;

  /**
   * <PERSON>h sách ID của các URL đã chọn
   */
  selectedUrlIds?: string[];
}

/**
 * Component form trượt để chọn các URL
 */
const UrlSlideInForm: React.FC<UrlSlideInFormProps> = ({
  isVisible,
  onClose,
  onSelect,
  selectedUrlIds = [],
}) => {
  // State cho dữ liệu và UI
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');
  const [urls, setUrls] = useState<Url[]>([]);
  const [selectedIds, setSelectedIds] = useState<string[]>(selectedUrlIds);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // State cho phân trang
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);

  // State cho sắp xếp và lọc
  const [sortBy, setSortBy] = useState<string>('title');
  const [sortDirection, setSortDirection] = useState<'ASC' | 'DESC'>('ASC');
  const [filterStatus, setFilterStatus] = useState<string>('');

  // State cho thêm URL mới
  const [showAddForm, setShowAddForm] = useState<boolean>(false);
  const [newUrlTitle, setNewUrlTitle] = useState<string>('');
  const [newUrlAddress, setNewUrlAddress] = useState<string>('');
  const [newUrlDescription, setNewUrlDescription] = useState<string>('');

  // Cấu hình cột cho bảng
  const columns: TableColumn<Url>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'url',
      title: 'URL',
      dataIndex: 'title',
      width: '40%',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-md bg-blue-100 flex items-center justify-center mr-3">
            <Icon name="link" size="md" className="text-blue-600" />
          </div>
          <div>
            <Typography variant="subtitle1">{record.title}</Typography>
            <Typography variant="caption" className="text-gray-500">
              {record.url}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'category',
      title: 'Danh mục',
      dataIndex: 'category',
      width: '20%',
    },
    {
      key: 'status',
      title: 'Trạng thái',
      dataIndex: 'status',
      width: '20%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.status === 'active' ? (
            <span className="text-green-500 text-sm flex items-center">
              <Icon name="check-circle" size="sm" className="mr-1" />
              Hoạt động
            </span>
          ) : record.status === 'pending' ? (
            <span className="text-yellow-500 text-sm flex items-center">
              <Icon name="clock" size="sm" className="mr-1" />
              Đang kiểm tra
            </span>
          ) : record.status === 'broken' ? (
            <span className="text-red-500 text-sm flex items-center">
              <Icon name="alert-circle" size="sm" className="mr-1" />
              Lỗi liên kết
            </span>
          ) : (
            <span className="text-gray-500 text-sm flex items-center">
              <Icon name="circle" size="sm" className="mr-1" />
              Chưa kiểm tra
            </span>
          )}
        </div>
      ),
    },
    {
      key: 'visit',
      title: 'Truy cập',
      width: '20%',
      render: (_, record) => (
        <Button
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            window.open(record.url, '_blank');
          }}
        >
          <Icon name="external-link" size="sm" className="mr-1" />
          Mở
        </Button>
      ),
    },
  ];

  // Giả lập dữ liệu URL - sử dụng useMemo để tránh re-render không cần thiết
  const mockUrls = useMemo<Url[]>(() => [
    { id: 'url-1', title: 'Trang chủ công ty', url: 'https://example.com', description: 'Trang web chính thức của công ty', category: 'Công ty', createdAt: '2023-05-15', lastChecked: '2023-10-01', status: 'active' },
    { id: 'url-2', title: 'Blog công nghệ', url: 'https://blog.example.com', description: 'Blog chia sẻ kiến thức công nghệ', category: 'Blog', createdAt: '2023-06-20', lastChecked: '2023-10-01', status: 'active' },
    { id: 'url-3', title: 'Trang sản phẩm', url: 'https://example.com/products', description: 'Danh sách sản phẩm của công ty', category: 'Sản phẩm', createdAt: '2023-07-05', lastChecked: '2023-10-01', status: 'active' },
    { id: 'url-4', title: 'Trang hỗ trợ', url: 'https://support.example.com', description: 'Trung tâm hỗ trợ khách hàng', category: 'Hỗ trợ', createdAt: '2023-08-10', lastChecked: '2023-10-01', status: 'broken' },
    { id: 'url-5', title: 'Trang tài liệu API', url: 'https://api.example.com/docs', description: 'Tài liệu API cho nhà phát triển', category: 'Tài liệu', createdAt: '2023-04-25', lastChecked: '2023-10-01', status: 'active' },
    { id: 'url-6', title: 'Trang đăng ký', url: 'https://example.com/register', description: 'Đăng ký tài khoản mới', category: 'Tài khoản', createdAt: '2023-01-15', lastChecked: '2023-10-01', status: 'active' },
    { id: 'url-7', title: 'Trang đăng nhập', url: 'https://example.com/login', description: 'Đăng nhập vào tài khoản', category: 'Tài khoản', createdAt: '2023-09-05', lastChecked: '2023-10-01', status: 'active' },
    { id: 'url-8', title: 'Trang liên hệ', url: 'https://example.com/contact', description: 'Thông tin liên hệ công ty', category: 'Liên hệ', createdAt: '2023-09-06', lastChecked: '2023-10-01', status: 'active' },
    { id: 'url-9', title: 'Trang giới thiệu', url: 'https://example.com/about', description: 'Giới thiệu về công ty', category: 'Công ty', createdAt: '2023-09-07', lastChecked: '2023-10-01', status: 'pending' },
    { id: 'url-10', title: 'Trang tuyển dụng', url: 'https://example.com/careers', description: 'Cơ hội nghề nghiệp tại công ty', category: 'Tuyển dụng', createdAt: '2023-10-01', lastChecked: '2023-10-01', status: 'active' },
  ], []);

  // Giả lập việc tải dữ liệu
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Giả lập API call
        await new Promise(resolve => setTimeout(resolve, 500));

        // Lọc dữ liệu theo tìm kiếm và trạng thái
        let filteredData = [...mockUrls];

        if (search) {
          filteredData = filteredData.filter(url =>
            url.title.toLowerCase().includes(search.toLowerCase()) ||
            url.url.toLowerCase().includes(search.toLowerCase()) ||
            (url.description && url.description.toLowerCase().includes(search.toLowerCase()))
          );
        }

        if (filterStatus) {
          filteredData = filteredData.filter(url => url.status === filterStatus);
        }

        // Sắp xếp dữ liệu
        filteredData.sort((a, b) => {
          if (sortBy === 'title') {
            return sortDirection === 'ASC'
              ? a.title.localeCompare(b.title)
              : b.title.localeCompare(a.title);
          } else if (sortBy === 'createdAt') {
            return sortDirection === 'ASC'
              ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
              : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
          }
          return 0;
        });

        // Phân trang
        const startIndex = (currentPage - 1) * itemsPerPage;
        const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage);

        setUrls(paginatedData);
        setTotalItems(filteredData.length);
      } catch (error) {
        console.error('Error fetching URLs:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, currentPage, itemsPerPage, sortBy, sortDirection, filterStatus]);

  // Kiểm tra có thay đổi chưa lưu không
  useEffect(() => {
    const hasUnsavedChanges =
      selectedIds.length !== selectedUrlIds.length ||
      selectedIds.some(id => !selectedUrlIds.includes(id)) ||
      selectedUrlIds.some(id => !selectedIds.includes(id));

    setHasChanges(hasUnsavedChanges);
  }, [selectedIds, selectedUrlIds]);

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setSearch(term);
    setCurrentPage(1);
  };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = (column: string, direction: 'ASC' | 'DESC') => {
    setSortBy(column);
    setSortDirection(direction);
  };

  // Xử lý thêm URL mới
  const handleAddUrl = () => {
    if (!newUrlTitle || !newUrlAddress) {
      alert('Vui lòng nhập đầy đủ thông tin URL');
      return;
    }

    // Kiểm tra URL hợp lệ
    try {
      new URL(newUrlAddress);
    } catch {
      alert('URL không hợp lệ. Vui lòng nhập URL đúng định dạng (ví dụ: https://example.com)');
      return;
    }

    // Tạo URL mới
    const newUrl: Url = {
      id: `url-${Date.now()}`,
      title: newUrlTitle,
      url: newUrlAddress,
      description: newUrlDescription,
      category: 'Khác',
      createdAt: new Date().toISOString().split('T')[0],
      status: 'pending',
    };

    // Thêm vào danh sách đã chọn
    setSelectedIds(prev => [...prev, newUrl.id]);

    // Reset form
    setNewUrlTitle('');
    setNewUrlAddress('');
    setNewUrlDescription('');
    setShowAddForm(false);

    // Reload dữ liệu
    setCurrentPage(1);
  };

  // Xử lý lưu
  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      // Giả lập API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Lấy thông tin đầy đủ của các URL đã chọn
      const selectedUrls = mockUrls.filter(url =>
        selectedIds.includes(url.id)
      );

      onSelect(selectedUrls);
      onClose();
    } catch (error) {
      console.error('Error saving selected URLs:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    if (hasChanges) {
      const confirmed = window.confirm(
        'Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn đóng form?'
      );
      if (!confirmed) return;
    }

    setSearch('');
    setShowAddForm(false);
    onClose();
  }, [hasChanges, onClose]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'sort',
      label: 'Sắp xếp theo',
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-title',
      label: 'Tiêu đề',
      onClick: () => handleSortChange('title', sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-date',
      label: 'Ngày tạo',
      onClick: () => handleSortChange('createdAt', sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'divider',
      divider: true,
    },
    {
      id: 'filter',
      label: 'Lọc theo',
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'filter-all',
      label: 'Tất cả',
      onClick: () => setFilterStatus(''),
    },
    {
      id: 'filter-active',
      label: 'Hoạt động',
      onClick: () => setFilterStatus('active'),
    },
    {
      id: 'filter-pending',
      label: 'Đang kiểm tra',
      onClick: () => setFilterStatus('pending'),
    },
    {
      id: 'filter-broken',
      label: 'Lỗi liên kết',
      onClick: () => setFilterStatus('broken'),
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-6xl">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">Chọn URL</Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            Đóng
          </Button>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => setShowAddForm(!showAddForm)}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Form thêm URL mới */}
        {showAddForm && (
          <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <Typography variant="subtitle1" className="mb-3">Thêm URL mới</Typography>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
              <div>
                <FormItem label="Tiêu đề">
                  <Input
                    value={newUrlTitle}
                    onChange={(e) => setNewUrlTitle(e.target.value)}
                    placeholder="Nhập tiêu đề URL"
                    fullWidth
                  />
                </FormItem>
              </div>
              <div>
                <FormItem label="Địa chỉ URL">
                  <Input
                    value={newUrlAddress}
                    onChange={(e) => setNewUrlAddress(e.target.value)}
                    placeholder="https://example.com"
                    fullWidth
                  />
                </FormItem>
              </div>
            </div>
            <div className="mb-3">
              <FormItem label="Mô tả">
                <Input
                  value={newUrlDescription}
                  onChange={(e) => setNewUrlDescription(e.target.value)}
                  placeholder="Nhập mô tả (tùy chọn)"
                  fullWidth
                />
              </FormItem>
            </div>
            <div className="flex justify-end">
              <Button
                variant="outline"
                onClick={() => setShowAddForm(false)}
                className="mr-2"
              >
                Hủy
              </Button>
              <Button
                variant="primary"
                onClick={handleAddUrl}
                disabled={!newUrlTitle || !newUrlAddress}
              >
                Thêm
              </Button>
            </div>
          </div>
        )}

        {/* Bảng dữ liệu */}
        <div className="mb-4 w-full overflow-x-auto">
          <Table
            data={urls}
            columns={columns}
            loading={isLoading}
            rowKey="id"
            size="lg"
            hoverable
            bordered={false}
            selectable={true}
            className="w-full table-fixed"
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
          />

          {/* Hiển thị khi không có dữ liệu */}
          {!isLoading && urls.length === 0 && (
            <EmptyState
              icon="search"
              title="Không có kết quả"
              description="Không tìm thấy URL nào phù hợp với tìm kiếm của bạn."
              className="py-8"
            />
          )}
        </div>

        {/* Phân trang */}
        <div className="mb-4">
          <Pagination
            currentPage={currentPage}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPageOptions={[5, 10, 20, 50]}
            showItemsPerPageSelector={true}
            showPageInfo={true}
          />
        </div>

        {/* Nút lưu */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            className="mr-2"
            disabled={isSubmitting}
          >
            Hủy
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            isLoading={isSubmitting}
            disabled={isLoading || !hasChanges}
          >
            Lưu
          </Button>
        </div>
      </Card>
    </SlideInForm>
  );
};

export default UrlSlideInForm;
