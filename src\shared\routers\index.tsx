import { createBrowser<PERSON>outer, RouterProvider } from 'react-router-dom';

// Import module routes
import formRoutes from './modules/formRoutes';
import componentRoutes from './modules/componentRoutes';
import aiRoutes from './modules/aiRoutes';
import commonRoutes from './modules/commonRoutes';
import demoRoutes from './modules/demoRoutes';
import blogRoutes from '@/modules/blog/routers/blogRoutes';
import { marketplaceRoutes } from '@/modules/marketplace';
import { businessRoutes } from '@/modules/business';
import dataRoutes from '@/modules/data/routers/dataRoutes';
import adminDataRoutes from '@/modules/admin/data/routers/adminDataRoutes';
import { authRoutes } from '@/modules/auth';
import { profileRoutes } from '@/modules/profile';
import { marketingRoutes } from '@/modules/marketing';
import { rpointRoutes } from '@/modules/rpoint';
import {
  rpointAdminRoutes as newRPointAdminRoutes,
  rpointAdminRoutes,
} from '@/modules/admin/r-point/routers/rpointAdminRoutes';
import { subscriptionRoutes } from '@/modules/subscription';
import { integrationRoutes } from '@/modules/integration';
import { employeeRoutes } from '@/modules/admin/employee/routers/employeeRoutes';
import authAdminRouters from '@/modules/admin/auth/routers/authAdminRouters';
import { genericPageRoutes } from '@/modules/generic-page';
import { userRoutes } from '@/modules/admin/user/routers';
import { affiliateRoutes } from '@/modules/admin/affiliate';
import { blogAdminRoutes } from '@/modules/admin/blog/routers';
import { adminToolRoutes } from '@/modules/admin/tool/routers';
import { toolRoutes } from '@/modules/tools/routers';
import dashboardAdminRoutes from '@/modules/admin/dashboard/routers/dashboardRouters';
import { marketingAdminRoutes } from '@/modules/admin/marketing/routers/marketingRoutes';
import { modelTrainingRoutes } from '@/modules/model-training';
import { businessAdminRoutes } from '@/modules/admin/business';
import { settingsRoutes } from '@/modules/settings';
import agentRoutes from '@/modules/ai-agents/routers/agentRouter';

/**
 * Create router with all routes from modules
 */
const router = createBrowserRouter([
  ...authRoutes,
  ...blogRoutes,
  ...genericPageRoutes,
  ...profileRoutes,
  ...marketplaceRoutes,
  ...businessRoutes,
  ...dataRoutes,
  ...integrationRoutes,
  ...rpointRoutes,
  ...subscriptionRoutes,
  ...marketingRoutes,
  ...modelTrainingRoutes,
  ...authAdminRouters,
  ...newRPointAdminRoutes,
  ...userRoutes,
  ...affiliateRoutes,
  ...employeeRoutes,
  ...blogAdminRoutes,
  ...marketingAdminRoutes,
  ...adminDataRoutes,
  ...dashboardAdminRoutes,
  ...componentRoutes,
  ...formRoutes,
  ...aiRoutes,
  ...demoRoutes,
  ...rpointAdminRoutes,
  ...adminToolRoutes,
  ...toolRoutes,
  ...businessAdminRoutes,
  ...settingsRoutes,
  ...agentRoutes,

  ...commonRoutes,
]);

const AppRouter = () => {
  return <RouterProvider router={router} />;
};

export default AppRouter;
