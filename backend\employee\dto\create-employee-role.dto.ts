import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho việc tạo role mới
 */
export class CreateEmployeeRoleDto {
  /**
   * ID của role
   */
  @ApiProperty({
    description: 'ID của role',
    example: 1,
  })
  @IsNotEmpty({ message: 'ID không được để trống' })
  @IsNumber({}, { message: 'ID phải là số' })
  id: number;

  /**
   * Tên role
   */
  @ApiProperty({
    description: 'Tên role',
    example: 'Admin',
  })
  @IsNotEmpty({ message: 'Tên role không được để trống' })
  @IsString({ message: 'Tên role phải là chuỗi' })
  name: string;

  /**
   * <PERSON>ô tả role
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả role',
    example: 'Quản trị viê<PERSON> hệ thống',
  })
  @IsNotEmpty({ message: '<PERSON><PERSON> tả không được để trống' })
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description: string;

  /**
   * Danh sách ID của các permission
   */
  @ApiProperty({
    description: 'Danh sách ID của các permission',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsOptional()
  @IsArray({ message: 'Danh sách permission phải là mảng' })
  permissionIds?: number[];
}
