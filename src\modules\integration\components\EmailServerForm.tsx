import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, Typography, Button, Input, Textarea, FormItem, Form } from '@/shared/components/common';
import Toggle from '@/shared/components/common/Toggle';
import {
  emailServerConfigurationSchema,
  EmailServerConfigurationFormData,
} from '../email/schemas';
import { EmailServerConfiguration, TestEmailServerDto } from '../email/types';
import { useTestEmailServer } from '../email/hooks';

interface EmailServerFormProps {
  onSubmit: (values: EmailServerConfigurationFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
  readOnly?: boolean;
  initialValues?: EmailServerConfiguration;
}

/**
 * Form tạo/chỉnh sửa Email Server Configuration
 */
const EmailServerForm: React.FC<EmailServerFormProps> = ({
  onSubmit,
  onCancel,
  isLoading = false,
  readOnly = false,
  initialValues,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [showTestForm, setShowTestForm] = useState(false);
  const [testData, setTestData] = useState<TestEmailServerDto>({
    recipientEmail: '',
    subject: 'Test Email Connection',
  });

  const testEmailMutation = useTestEmailServer();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
  } = useForm({
    resolver: zodResolver(emailServerConfigurationSchema),
    defaultValues: {
      serverName: '',
      host: '',
      port: 587,
      username: '',
      password: '',
      useSsl: false,
      useStartTls: true,
      additionalSettings: '',
      isActive: true,
    },
  });

  // Reset form khi có initialValues
  useEffect(() => {
    if (initialValues) {
      reset({
        serverName: initialValues.serverName,
        host: initialValues.host,
        port: initialValues.port,
        username: initialValues.username,
        password: '', // Không hiển thị password cũ
        useSsl: initialValues.useSsl,
        useStartTls: initialValues.useStartTls,
        additionalSettings: initialValues.additionalSettings
          ? JSON.stringify(initialValues.additionalSettings, null, 2)
          : '',
        isActive: initialValues.isActive,
      });
    }
  }, [initialValues, reset]);

  const handleFormSubmit = (data: EmailServerConfigurationFormData) => {
    const processedData = {
      ...data,
      additionalSettings: data.additionalSettings
        ? JSON.parse(data.additionalSettings)
        : undefined,
    };
    onSubmit(processedData);
  };

  const handleTestConnection = async () => {
    if (!initialValues?.id) {
      return;
    }

    await testEmailMutation.mutateAsync({
      id: initialValues.id,
      data: testData,
    });
  };

  const isEditMode = !!initialValues;

  return (
    <div className="p-6 bg-white dark:bg-gray-800">
      <Form onSubmit={handleFormSubmit} schema={emailServerConfigurationSchema} className="space-y-6">
        <Typography variant="h5" className="mb-4 text-primary-600 dark:text-primary-400">
          {readOnly
            ? t('admin:integration.email.form.view', 'Xem Email Server')
            : isEditMode
            ? t('admin:integration.email.form.edit', 'Chỉnh sửa Email Server')
            : t('admin:integration.email.form.create', 'Tạo Email Server')}
        </Typography>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Server Name */}
          <FormItem
            name="serverName"
            label={t('admin:integration.email.form.fields.serverName')}
            required
          >
            <Input
              placeholder={t('admin:integration.email.form.placeholders.serverName')}
              {...register('serverName')}
              disabled={readOnly || isLoading}
            />
          </FormItem>

          {/* Host */}
          <FormItem
            name="host"
            label={t('admin:integration.email.form.fields.host')}
            required
          >
            <Input
              placeholder={t('admin:integration.email.form.placeholders.host')}
              {...register('host')}
              disabled={readOnly || isLoading}
            />
          </FormItem>

          {/* Port */}
          <FormItem
            name="port"
            label={t('admin:integration.email.form.fields.port')}
            required
          >
            <Input
              type="number"
              placeholder={t('admin:integration.email.form.placeholders.port')}
              {...register('port', { valueAsNumber: true })}
              disabled={readOnly || isLoading}
            />
          </FormItem>

          {/* Username */}
          <FormItem
            name="username"
            label={t('admin:integration.email.form.fields.username')}
            required
          >
            <Input
              type="email"
              placeholder={t('admin:integration.email.form.placeholders.username')}
              {...register('username')}
              disabled={readOnly || isLoading}
            />
          </FormItem>

          {/* Password */}
          <FormItem
            name="password"
            label={t('admin:integration.email.form.fields.password')}
            required={!isEditMode}
          >
            <Input
              type="password"
              placeholder={t('admin:integration.email.form.placeholders.password')}
              {...register('password')}
              disabled={readOnly || isLoading}
            />
          </FormItem>

          {/* SSL/TLS Settings */}
          <div className="space-y-4">
            <FormItem name="useSsl">
              <Toggle
                checked={watch('useSsl')}
                onChange={(checked) => setValue('useSsl', checked)}
                disabled={readOnly || isLoading}
                label={t('admin:integration.email.form.fields.useSsl')}
              />
            </FormItem>

            <FormItem name="useStartTls">
              <Toggle
                checked={watch('useStartTls')}
                onChange={(checked) => setValue('useStartTls', checked)}
                disabled={readOnly || isLoading}
                label={t('admin:integration.email.form.fields.useStartTls')}
              />
            </FormItem>

            <FormItem name="isActive">
              <Toggle
                checked={watch('isActive')}
                onChange={(checked) => setValue('isActive', checked)}
                disabled={readOnly || isLoading}
                label={t('admin:integration.email.form.fields.isActive')}
              />
            </FormItem>
          </div>
        </div>

        {/* Additional Settings */}
        <FormItem
          name="additionalSettings"
          label={t('admin:integration.email.form.fields.additionalSettings')}
        >
          <Textarea
            placeholder={t('admin:integration.email.form.placeholders.additionalSettings')}
            {...register('additionalSettings')}
            disabled={readOnly || isLoading}
            rows={4}
            fullWidth
          />
        </FormItem>

        {/* Test Connection Section */}
        {isEditMode && !readOnly && (
          <Card className="p-4 bg-gray-50 dark:bg-gray-700">
            <div className="flex items-center justify-between mb-4">
              <Typography variant="h6">
                {t('admin:integration.email.form.test', 'Kiểm tra kết nối')}
              </Typography>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowTestForm(!showTestForm)}
              >
                {showTestForm ? t('common:hide') : t('admin:integration.email.actions.test')}
              </Button>
            </div>

            {showTestForm && (
              <div className="space-y-4">
                <FormItem
                  name="recipientEmail"
                  label={t('admin:integration.email.form.fields.recipientEmail')}
                  required
                >
                  <Input
                    type="email"
                    placeholder={t('admin:integration.email.form.placeholders.recipientEmail')}
                    value={testData.recipientEmail}
                    onChange={(e) => setTestData({ ...testData, recipientEmail: e.target.value })}
                    disabled={testEmailMutation.isPending}
                  />
                </FormItem>

                <FormItem
                  name="subject"
                  label={t('admin:integration.email.form.fields.subject')}
                  required
                >
                  <Input
                    placeholder={t('admin:integration.email.form.placeholders.subject')}
                    value={testData.subject}
                    onChange={(e) => setTestData({ ...testData, subject: e.target.value })}
                    disabled={testEmailMutation.isPending}
                  />
                </FormItem>

                <div className="flex justify-end">
                  <Button
                    type="button"
                    variant="primary"
                    onClick={handleTestConnection}
                    disabled={testEmailMutation.isPending}
                  >
                    {testEmailMutation.isPending
                      ? t('common:processing')
                      : t('admin:integration.email.actions.test')}
                  </Button>
                </div>
              </div>
            )}
          </Card>
        )}

        {/* Form Actions */}
        {!readOnly && (
          <div className="flex justify-end space-x-3">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              {t('common:cancel')}
            </Button>
            <Button type="submit" variant="primary" disabled={isLoading}>
              {isLoading ? t('common:processing') : t('common:save')}
            </Button>
          </div>
        )}
      </form>
    </div>
  );
};

export default EmailServerForm;
