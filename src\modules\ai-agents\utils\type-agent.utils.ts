/**
 * Utility functions cho Type Agent
 */

import { CustomAgentFormData } from '../components/agent-add/CustomAgentForm';
import { CreateTypeAgentDto, TypeAgentConfig } from '../types/dto';

/**
 * Interface cho config field item
 */
export interface ConfigFieldItem {
  key: keyof TypeAgentConfig;
  label: string;
  description?: string;
}

/**
 * Mapping các field của TypeAgentConfig với label tiếng Việt
 */
export const TYPE_AGENT_CONFIG_FIELDS: ConfigFieldItem[] = [
  {
    key: 'hasProfile',
    label: 'Hỗ trợ Profile',
    description: 'Cho phép cấu hình thông tin cá nhân của agent'
  },
  {
    key: 'hasOutput',
    label: 'Hỗ trợ Output',
    description: 'Cho phép cấu hình định dạng đầu ra'
  },
  {
    key: 'hasConversion',
    label: 'Hỗ trợ Conversion',
    description: 'Cho phép cấu hình chuyển đổi dữ liệu'
  },
  {
    key: 'hasResources',
    label: 'Hỗ trợ Resources',
    description: 'Cho phép quản lý tài nguyên và file'
  },
  {
    key: 'hasStrategy',
    label: 'Hỗ trợ Strategy',
    description: 'Cho phép cấu hình chiến lược xử lý'
  },
  {
    key: 'hasMultiAgent',
    label: 'Hỗ trợ Multi Agent',
    description: 'Cho phép cấu hình nhiều agent cùng làm việc'
  }
];

/**
 * Lấy danh sách config fields cho FullTypeAgentConfig (tất cả fields)
 */
export const FULL_TYPE_AGENT_CONFIG_FIELDS: ConfigFieldItem[] = TYPE_AGENT_CONFIG_FIELDS;

/**
 * Chuyển đổi CustomAgentFormData sang CreateTypeAgentDto
 * @param formData Dữ liệu từ form
 * @returns DTO để gửi API
 */
export const convertCustomAgentFormToDto = (
  formData: CustomAgentFormData
): CreateTypeAgentDto => {
  // Chuyển đổi config từ CustomAgentFormData.config sang TypeAgentConfig
  const config: TypeAgentConfig = {
    hasProfile: formData.config.hasProfile,
    hasOutput: formData.config.hasOutput,
    hasConversion: formData.config.hasConversion,
    hasResources: formData.config.hasResources,
    // Mặc định cho các field không có trong CustomAgentFormData.config hoặc có thể undefined
    hasStrategy: formData.config.hasStrategy ?? false,
    hasMultiAgent: formData.config.hasMultiAgent ?? false,
  };

  return {
    name: formData.name,
    description: formData.description,
    config,
    groupToolIds: formData.groupToolIds,
  };
};

/**
 * Chuyển đổi CreateTypeAgentDto sang CustomAgentFormData
 * @param dto DTO từ API
 * @returns Dữ liệu cho form
 */
export const convertDtoToCustomAgentForm = (
  dto: CreateTypeAgentDto
): CustomAgentFormData => {
  return {
    name: dto.name,
    description: dto.description || '',
    config: {
      hasProfile: dto.config.hasProfile || false,
      hasOutput: dto.config.hasOutput || false,
      hasConversion: dto.config.hasConversion || false,
      hasResources: dto.config.hasResources || false,
      hasStrategy: dto.config.hasStrategy || false,
      hasMultiAgent: dto.config.hasMultiAgent || false,
    },
    groupToolIds: dto.groupToolIds,
  };
};

/**
 * Validate dữ liệu form trước khi submit
 * @param formData Dữ liệu form
 * @returns Object chứa isValid và errors
 */
export const validateCustomAgentForm = (formData: CustomAgentFormData) => {
  const errors: Record<string, string> = {};

  // Validate tên
  if (!formData.name.trim()) {
    errors.name = 'Tên agent không được để trống';
  } else if (formData.name.trim().length < 2) {
    errors.name = 'Tên agent phải có ít nhất 2 ký tự';
  } else if (formData.name.trim().length > 100) {
    errors.name = 'Tên agent không được vượt quá 100 ký tự';
  }

  // Validate mô tả
  if (!formData.description.trim()) {
    errors.description = 'Mô tả không được để trống';
  } else if (formData.description.trim().length < 10) {
    errors.description = 'Mô tả phải có ít nhất 10 ký tự';
  } else if (formData.description.trim().length > 500) {
    errors.description = 'Mô tả không được vượt quá 500 ký tự';
  }

  // Validate group tools - không bắt buộc
  // Bỏ validation bắt buộc cho groupToolIds

  // Validate config - ít nhất một tính năng phải được bật
  const hasAnyFeature = Object.values(formData.config).some(value => value === true);
  if (!hasAnyFeature) {
    errors.config = 'Phải bật ít nhất một tính năng cho agent';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};
