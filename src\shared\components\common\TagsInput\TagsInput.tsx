import React, { useState, useEffect } from 'react';
import { FieldValues } from 'react-hook-form';
import { Input, Chip } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';

interface TagsInputProps<T extends FieldValues = Record<string, unknown>> {
  /**
   * Tên trường trong form
   */
  fieldName: string;

  /**
   * Placeholder cho input
   */
  placeholder?: string;

  /**
   * Form ref để truy cập form methods
   */
  formRef: React.RefObject<FormRef<T>>;

  /**
   * Giá trị ban đầu (chuỗi các tag phân cách bởi dấu phẩy hoặc mảng các tag)
   */
  initialValue?: string | string[];

  /**
   * Callback khi danh sách tags thay đổi
   */
  onChange?: (tags: string[]) => void;

  /**
   * Có cho phép chỉnh sửa không
   */
  readOnly?: boolean;
}

/**
 * Component nhập tags với hỗ trợ thêm/xóa tag
 */
const TagsInput = <T extends FieldValues = Record<string, unknown>>({
  fieldName,
  placeholder = 'Nhập tag và nhấn Enter',
  formRef,
  initialValue = '',
  onChange,
  readOnly = false,
}: TagsInputProps<T>) => {
  // State lưu trữ danh sách tags
  const [tags, setTags] = useState<string[]>([]);

  // Khởi tạo tags từ giá trị ban đầu
  useEffect(() => {
    if (initialValue) {
      // Xử lý cả trường hợp initialValue là string hoặc array
      let initialTags: string[] = [];

      if (typeof initialValue === 'string') {
        initialTags = initialValue
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0);
      } else if (Array.isArray(initialValue)) {
        initialTags = initialValue
          .map(tag => String(tag).trim())
          .filter(tag => tag.length > 0);
      }

      setTags(initialTags);
    }
  }, [initialValue]);

  // Cập nhật giá trị form khi tags thay đổi
  // Sử dụng useRef để theo dõi giá trị trước đó và tránh vòng lặp vô hạn
  const prevTagsRef = React.useRef<string[]>([]);

  useEffect(() => {
    // So sánh tags hiện tại với tags trước đó
    const prevTags = prevTagsRef.current;
    const tagsChanged =
      prevTags.length !== tags.length ||
      tags.some((tag, i) => prevTags[i] !== tag);

    // Chỉ cập nhật khi tags thực sự thay đổi
    if (tagsChanged && formRef.current) {
      const tagsString = tags.join(', ');
      formRef.current.getFormMethods().setValue(fieldName as never, tagsString as never, {
        shouldValidate: true,
        shouldDirty: true,
      });

      if (onChange) {
        onChange(tags);
      }

      // Cập nhật ref để lưu giá trị hiện tại cho lần so sánh tiếp theo
      prevTagsRef.current = [...tags];
    }
  }, [tags, fieldName, formRef, onChange]);

  // Xử lý thêm tag mới
  const handleAddTag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && e.currentTarget.value.trim()) {
      e.preventDefault();

      const newTag = e.currentTarget.value.trim();

      // Thêm tag mới nếu chưa tồn tại
      if (!tags.includes(newTag)) {
        setTags(prevTags => [...prevTags, newTag]);
      }

      // Xóa giá trị input
      e.currentTarget.value = '';
    }
  };

  // Xử lý xóa tag
  const handleRemoveTag = (tagToRemove: string) => {
    setTags(prevTags => prevTags.filter(tag => tag !== tagToRemove));
  };

  return (
    <div className="space-y-2">
      {!readOnly && (
        <Input
          fullWidth
          placeholder={placeholder}
          onKeyDown={handleAddTag}
          disabled={readOnly}
        />
      )}
      <div className="flex flex-wrap gap-1 mt-2">
        {tags.map((tag, index) => (
          <Chip
            key={`tag-${index}-${tag}`}
            size="sm"
            closable={!readOnly}
            onClose={() => handleRemoveTag(tag)}
          >
            {tag}
          </Chip>
        ))}
      </div>
    </div>
  );
};

export default TagsInput;
