import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, getSchemaPath } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ZaloService } from '../services/zalo.service';
import { ZaloCampaign, ZaloCampaignLog } from '../entities';
import {
  CreateZaloCampaignDto,
  UpdateZaloCampaignDto,
  ZaloCampaignLogQueryDto,
  ZaloCampaignLogResponseDto,
  ZaloCampaignQueryDto,
  ZaloCampaignResponseDto,
} from '../dto/zalo';

/**
 * Controller xử lý API liên quan đến chiến dịch Zalo
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_CAMPAIGN)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/:oaId/campaigns')
export class ZaloCampaignController {
  constructor(private readonly zaloService: ZaloService) {}

  /**
   * Lấy danh sách chiến dịch Zalo
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách chiến dịch Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách chiến dịch Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZaloCampaignResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getCampaigns(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query() queryDto: ZaloCampaignQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloCampaign>>> {
    const result = await this.zaloService.getZaloCampaigns(user.id, oaId, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách chiến dịch Zalo thành công');
  }

  /**
   * Lấy thông tin chi tiết chiến dịch Zalo
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết chiến dịch Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết chiến dịch Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloCampaignResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch Zalo' })
  async getCampaignDetail(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ZaloCampaign>> {
    const result = await this.zaloService.getZaloCampaignDetail(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết chiến dịch Zalo thành công');
  }

  /**
   * Tạo chiến dịch Zalo mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo chiến dịch Zalo mới' })
  @ApiResponse({
    status: 201,
    description: 'Tạo chiến dịch Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloCampaignResponseDto) }
          }
        }
      ]
    }
  })
  async createCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() createDto: CreateZaloCampaignDto,
  ): Promise<ApiResponseDto<ZaloCampaign>> {
    const result = await this.zaloService.createZaloCampaign(user.id, oaId, createDto);
    return ApiResponseDto.success(result, 'Tạo chiến dịch Zalo thành công');
  }

  /**
   * Cập nhật chiến dịch Zalo
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật chiến dịch Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật chiến dịch Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloCampaignResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch Zalo' })
  async updateCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateZaloCampaignDto,
  ): Promise<ApiResponseDto<ZaloCampaign>> {
    const result = await this.zaloService.updateZaloCampaign(user.id, oaId, id, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật chiến dịch Zalo thành công');
  }

  /**
   * Xóa chiến dịch Zalo
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa chiến dịch Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Xóa chiến dịch Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { type: 'boolean' }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch Zalo' })
  async deleteCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.zaloService.deleteZaloCampaign(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Xóa chiến dịch Zalo thành công');
  }

  /**
   * Thực thi chiến dịch Zalo
   */
  @Post(':id/execute')
  @ApiOperation({ summary: 'Thực thi chiến dịch Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Thực thi chiến dịch Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloCampaignResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch Zalo' })
  async executeCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ZaloCampaign>> {
    const result = await this.zaloService.executeZaloCampaign(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Thực thi chiến dịch Zalo thành công');
  }

  /**
   * Dừng chiến dịch Zalo
   */
  @Post(':id/stop')
  @ApiOperation({ summary: 'Dừng chiến dịch Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Dừng chiến dịch Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloCampaignResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch Zalo' })
  async stopCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ZaloCampaign>> {
    const result = await this.zaloService.stopZaloCampaign(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Dừng chiến dịch Zalo thành công');
  }

  /**
   * Lấy lịch sử thực thi chiến dịch Zalo
   */
  @Get(':id/logs')
  @ApiOperation({ summary: 'Lấy lịch sử thực thi chiến dịch Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Lấy lịch sử thực thi chiến dịch Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZaloCampaignLogResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch Zalo' })
  async getCampaignLogs(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
    @Query() queryDto: ZaloCampaignLogQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloCampaignLog>>> {
    const result = await this.zaloService.getZaloCampaignLogs(user.id, oaId, id, queryDto);
    return ApiResponseDto.success(result, 'Lấy lịch sử thực thi chiến dịch Zalo thành công');
  }
}
