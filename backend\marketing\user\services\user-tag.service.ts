import { Injectable, NotFoundException } from '@nestjs/common';
import { UserTagRepository } from '../repositories/user-tag.repository';
import { CreateTagDto, TagResponseDto, UpdateTagDto } from '../dto/tag';
import { UserTag } from '../entities/user-tag.entity';

/**
 * Service xử lý logic liên quan đến tag
 */
@Injectable()
export class UserTagService {
  constructor(private readonly userTagRepository: UserTagRepository) {}

  /**
   * Tạo tag mới
   * @param userId ID của người dùng
   * @param createTagDto Dữ liệu tạo tag
   * @returns Thông tin tag đã tạo
   */
  async create(userId: number, createTagDto: CreateTagDto): Promise<TagResponseDto> {
    const now = Math.floor(Date.now() / 1000);

    const tag = new UserTag();
    tag.userId = userId;
    tag.name = createTagDto.name;
    tag.color = createTagDto.color || '#3498db'; // Màu mặc định nếu không được cung cấp
    tag.createdAt = now;
    tag.updatedAt = now;

    const savedTag = await this.userTagRepository.save(tag);
    return this.mapToDto(savedTag as UserTag);
  }

  /**
   * Cập nhật tag
   * @param userId ID của người dùng
   * @param id ID của tag
   * @param updateTagDto Dữ liệu cập nhật tag
   * @returns Thông tin tag đã cập nhật
   */
  async update(userId: number, id: number, updateTagDto: UpdateTagDto): Promise<TagResponseDto> {
    const tag = await this.userTagRepository.findOne({ where: { id, userId } });
    if (!tag) {
      throw new NotFoundException(`Tag với ID ${id} không tồn tại`);
    }

    if (updateTagDto.name !== undefined) {
      tag.name = updateTagDto.name;
    }

    if (updateTagDto.color !== undefined) {
      tag.color = updateTagDto.color;
    }

    tag.updatedAt = Math.floor(Date.now() / 1000);

    const updatedTag = await this.userTagRepository.save(tag);
    return this.mapToDto(updatedTag as UserTag);
  }

  /**
   * Xóa tag
   * @param userId ID của người dùng
   * @param id ID của tag
   * @returns true nếu xóa thành công
   */
  async remove(userId: number, id: number): Promise<boolean> {
    const tag = await this.userTagRepository.findOne({ where: { id, userId } });
    if (!tag) {
      throw new NotFoundException(`Tag với ID ${id} không tồn tại`);
    }

    await this.userTagRepository.remove(tag);
    return true;
  }

  /**
   * Lấy danh sách tag của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách tag
   */
  async findAll(userId: number): Promise<TagResponseDto[]> {
    const tags = await this.userTagRepository.find({ where: { userId } });
    return tags.map(tag => this.mapToDto(tag));
  }

  /**
   * Lấy thông tin tag theo ID
   * @param userId ID của người dùng
   * @param id ID của tag
   * @returns Thông tin tag
   */
  async findOne(userId: number, id: number): Promise<TagResponseDto> {
    const tag = await this.userTagRepository.findOne({ where: { id, userId } });
    if (!tag) {
      throw new NotFoundException(`Tag với ID ${id} không tồn tại`);
    }

    return this.mapToDto(tag);
  }

  /**
   * Chuyển đổi entity thành DTO
   * @param tag Entity tag
   * @returns DTO tag
   */
  private mapToDto(tag: UserTag): TagResponseDto {
    const dto = new TagResponseDto();
    dto.id = tag.id;
    dto.name = tag.name;
    dto.color = tag.color;
    dto.createdAt = tag.createdAt;
    dto.updatedAt = tag.updatedAt;
    return dto;
  }
}
