import { Test, TestingModule } from '@nestjs/testing';
import { UserSegmentService } from '../services/user-segment.service';
import { UserSegmentRepository } from '../repositories/user-segment.repository';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import { NotFoundException } from '@nestjs/common';
import { CreateSegmentDto, UpdateSegmentDto, ConditionType, OperatorType } from '../dto/segment';
// Các entity được sử dụng trong các mock

describe('UserSegmentService', () => {
  let service: UserSegmentService;
  let segmentRepository: UserSegmentRepository;
  let audienceRepository: UserAudienceRepository;

  const mockUserSegmentRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
  };

  const mockUserAudienceRepository = {
    find: jest.fn(),
    count: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserSegmentService,
        {
          provide: UserSegmentRepository,
          useValue: mockUserSegmentRepository,
        },
        {
          provide: UserAudienceRepository,
          useValue: mockUserAudienceRepository,
        },
      ],
    }).compile();

    service = module.get<UserSegmentService>(UserSegmentService);
    segmentRepository = module.get<UserSegmentRepository>(UserSegmentRepository);
    audienceRepository = module.get<UserAudienceRepository>(UserAudienceRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new segment', async () => {
      // Arrange
      const userId = 1;
      const createSegmentDto: CreateSegmentDto = {
        name: 'Test Segment',
        description: 'Test Description',
        criteria: {
          conditionType: ConditionType.AND,
          conditions: [
            {
              field: 'email',
              operator: OperatorType.CONTAINS,
              value: 'example.com',
            },
          ],
        },
      };

      const savedSegment = {
        id: 1,
        userId,
        name: createSegmentDto.name,
        description: createSegmentDto.description,
        criteria: createSegmentDto.criteria,
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      mockUserSegmentRepository.save.mockResolvedValue(savedSegment);

      // Act
      const result = await service.create(userId, createSegmentDto);

      // Assert
      expect(mockUserSegmentRepository.save).toHaveBeenCalled();
      expect(result).toEqual({
        id: savedSegment.id,
        name: savedSegment.name,
        description: savedSegment.description,
        criteria: savedSegment.criteria,
        createdAt: savedSegment.createdAt,
        updatedAt: savedSegment.updatedAt,
      });
    });
  });

  describe('findAll', () => {
    it('should return an array of segments', async () => {
      // Arrange
      const userId = 1;
      const segments = [
        {
          id: 1,
          userId,
          name: 'Segment 1',
          description: 'Description 1',
          criteria: {
            conditionType: ConditionType.AND,
            conditions: [
              {
                field: 'email',
                operator: OperatorType.CONTAINS,
                value: 'example.com',
              },
            ],
          },
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
        {
          id: 2,
          userId,
          name: 'Segment 2',
          description: 'Description 2',
          criteria: {
            conditionType: ConditionType.OR,
            conditions: [
              {
                field: 'phone',
                operator: OperatorType.CONTAINS,
                value: '+84',
              },
            ],
          },
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
      ];

      mockUserSegmentRepository.find.mockResolvedValue(segments);

      // Act
      const result = await service.findAll(userId);

      // Assert
      expect(mockUserSegmentRepository.find).toHaveBeenCalledWith({ where: { userId } });
      expect(result).toHaveLength(2);
      expect(result[0].id).toEqual(segments[0].id);
      expect(result[1].id).toEqual(segments[1].id);
    });
  });

  describe('findOne', () => {
    it('should return a segment by id', async () => {
      // Arrange
      const userId = 1;
      const segmentId = 1;
      const segment = {
        id: segmentId,
        userId,
        name: 'Test Segment',
        description: 'Test Description',
        criteria: {
          conditionType: ConditionType.AND,
          conditions: [
            {
              field: 'email',
              operator: OperatorType.CONTAINS,
              value: 'example.com',
            },
          ],
        },
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      mockUserSegmentRepository.findOne.mockResolvedValue(segment);

      // Act
      const result = await service.findOne(userId, segmentId);

      // Assert
      expect(mockUserSegmentRepository.findOne).toHaveBeenCalledWith({ where: { id: segmentId, userId } });
      expect(result).toEqual({
        id: segment.id,
        name: segment.name,
        description: segment.description,
        criteria: segment.criteria,
        createdAt: segment.createdAt,
        updatedAt: segment.updatedAt,
      });
    });

    it('should throw NotFoundException if segment not found', async () => {
      // Arrange
      const userId = 1;
      const segmentId = 999;

      mockUserSegmentRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findOne(userId, segmentId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a segment', async () => {
      // Arrange
      const userId = 1;
      const segmentId = 1;
      const updateSegmentDto: UpdateSegmentDto = {
        name: 'Updated Segment',
      };

      const existingSegment = {
        id: segmentId,
        userId,
        name: 'Test Segment',
        description: 'Test Description',
        criteria: {
          conditionType: ConditionType.AND,
          conditions: [
            {
              field: 'email',
              operator: OperatorType.CONTAINS,
              value: 'example.com',
            },
          ],
        },
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      const updatedSegment = {
        ...existingSegment,
        name: updateSegmentDto.name,
        updatedAt: 1619171300,
      };

      mockUserSegmentRepository.findOne.mockResolvedValue(existingSegment);
      mockUserSegmentRepository.save.mockResolvedValue(updatedSegment);

      // Act
      const result = await service.update(userId, segmentId, updateSegmentDto);

      // Assert
      expect(mockUserSegmentRepository.findOne).toHaveBeenCalledWith({ where: { id: segmentId, userId } });
      expect(mockUserSegmentRepository.save).toHaveBeenCalled();
      expect(result.name).toEqual(updateSegmentDto.name);
    });

    it('should throw NotFoundException if segment not found', async () => {
      // Arrange
      const userId = 1;
      const segmentId = 999;
      const updateSegmentDto: UpdateSegmentDto = {
        name: 'Updated Segment',
      };

      mockUserSegmentRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.update(userId, segmentId, updateSegmentDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should remove a segment', async () => {
      // Arrange
      const userId = 1;
      const segmentId = 1;
      const segment = {
        id: segmentId,
        userId,
        name: 'Test Segment',
        description: 'Test Description',
        criteria: {
          conditionType: ConditionType.AND,
          conditions: [
            {
              field: 'email',
              operator: OperatorType.CONTAINS,
              value: 'example.com',
            },
          ],
        },
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      mockUserSegmentRepository.findOne.mockResolvedValue(segment);
      mockUserSegmentRepository.remove.mockResolvedValue(segment);

      // Act
      const result = await service.remove(userId, segmentId);

      // Assert
      expect(mockUserSegmentRepository.findOne).toHaveBeenCalledWith({ where: { id: segmentId, userId } });
      expect(mockUserSegmentRepository.remove).toHaveBeenCalledWith(segment);
      expect(result).toBe(true);
    });

    it('should throw NotFoundException if segment not found', async () => {
      // Arrange
      const userId = 1;
      const segmentId = 999;

      mockUserSegmentRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.remove(userId, segmentId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getStats', () => {
    it('should return segment statistics', async () => {
      // Arrange
      const userId = 1;
      const segmentId = 1;
      const segment = {
        id: segmentId,
        userId,
        name: 'Test Segment',
        description: 'Test Description',
        criteria: {
          conditionType: ConditionType.AND,
          conditions: [
            {
              field: 'email',
              operator: OperatorType.CONTAINS,
              value: 'example.com',
            },
          ],
        },
        createdAt: 1619171200,
        updatedAt: 1619171200,
      };

      const audiences = [
        {
          id: 1,
          userId,
          email: '<EMAIL>',
          phone: '+84912345678',
          createdAt: 1619171200,
          updatedAt: 1619171200,
        },
      ];

      mockUserSegmentRepository.findOne.mockResolvedValue(segment);
      mockUserAudienceRepository.count.mockResolvedValue(10);
      mockUserAudienceRepository.find.mockResolvedValue(audiences);

      // Mock the getAudiencesInSegment method
      jest.spyOn(service, 'getAudiencesInSegment').mockResolvedValue(audiences);

      // Act
      const result = await service.getStats(userId, segmentId);

      // Assert
      expect(mockUserSegmentRepository.findOne).toHaveBeenCalledWith({ where: { id: segmentId, userId } });
      expect(mockUserAudienceRepository.count).toHaveBeenCalledWith({ where: { userId } });
      expect(service.getAudiencesInSegment).toHaveBeenCalledWith(userId, segment);
      expect(result.segmentId).toEqual(segmentId);
      expect(result.segmentName).toEqual(segment.name);
      expect(result.totalAudiences).toEqual(audiences.length);
      expect(result.percentageOfTotal).toEqual(audiences.length / 10);
    });

    it('should throw NotFoundException if segment not found', async () => {
      // Arrange
      const userId = 1;
      const segmentId = 999;

      mockUserSegmentRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getStats(userId, segmentId)).rejects.toThrow(NotFoundException);
    });
  });
});
