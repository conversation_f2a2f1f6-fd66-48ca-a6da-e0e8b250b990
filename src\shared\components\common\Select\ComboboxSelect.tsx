import { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/contexts/theme';
import SelectOption from './SelectOption';
import { SelectOption as SelectOptionType } from './Select';

export interface ComboboxSelectProps {
  /**
   * Giá trị đã chọn
   */
  value?: string;

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: string) => void;

  /**
   * Options
   */
  options: SelectOptionType[];

  /**
   * Cho phép nhập giá trị tùy ý
   */
  allowCustomValue?: boolean;

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Các props khác
   */
  name?: string;
  id?: string;
  className?: string;
  error?: string;
  helperText?: string;

  /**
   * <PERSON><PERSON><PERSON> thước
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Chiều rộng
   */
  fullWidth?: boolean;

  /**
   * Callback khi blur
   */
  onBlur?: () => void;
}

/**
 * Component ComboboxSelect - Select kết hợp giữa dropdown và input, cho phép nhập tự do
 */
const ComboboxSelect = forwardRef<HTMLInputElement, ComboboxSelectProps>(
  (
    {
      value = '',
      onChange,
      options = [],
      allowCustomValue = true,
      placeholder = '',
      label,
      disabled = false,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      onBlur,
    },
    ref
  ) => {
    const { t } = useTranslation();
    useTheme(); // Keep the hook call to avoid React hooks rules violation
    const [isOpen, setIsOpen] = useState(false);
    const [inputValue, setInputValue] = useState(value);
    const [filteredOptions, setFilteredOptions] = useState<SelectOptionType[]>(options);

    const comboboxRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    // Forward ref to input element
    useImperativeHandle(ref, () => inputRef.current as HTMLInputElement);

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10',
      lg: 'h-12 text-lg',
    }[size];

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Update input value when value prop changes
    useEffect(() => {
      setInputValue(value);
    }, [value]);

    // Close dropdown when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (comboboxRef.current && !comboboxRef.current.contains(event.target as Node)) {
          setIsOpen(false);

          // Call onBlur if provided
          if (onBlur) {
            onBlur();
          }
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [onBlur]);

    // Filter options based on input value
    useEffect(() => {
      if (!inputValue) {
        setFilteredOptions(options);
        return;
      }

      const filtered = options.filter(option =>
        option.label.toLowerCase().includes(inputValue.toLowerCase())
      );

      setFilteredOptions(filtered);
    }, [inputValue, options]);

    // Handle input change
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setInputValue(newValue);

      // Open dropdown when typing
      if (!isOpen && newValue) {
        setIsOpen(true);
      }

      // Call onChange if allowCustomValue is true
      if (allowCustomValue && onChange) {
        onChange(newValue);
      }
    };

    // Handle option click
    const handleOptionClick = (option: SelectOptionType) => {
      setInputValue(option.label);
      setIsOpen(false);

      if (onChange) {
        onChange(option.value.toString());
      }

      // Focus input after selection
      if (inputRef.current) {
        inputRef.current.focus();
      }
    };

    // Handle key down
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Close dropdown on Escape
      if (e.key === 'Escape') {
        setIsOpen(false);
      }

      // Select first option on Enter if dropdown is open
      if (e.key === 'Enter' && isOpen && filteredOptions.length > 0) {
        e.preventDefault();
        handleOptionClick(filteredOptions[0]);
      }

      // Open dropdown on ArrowDown
      if (e.key === 'ArrowDown' && !isOpen) {
        setIsOpen(true);
      }
    };

    return (
      <div className={`relative ${widthClass} ${className}`} ref={comboboxRef}>
        {/* Label */}
        {label && (
          <label className="block text-sm font-medium mb-1" htmlFor={id}>
            {label}
          </label>
        )}

        {/* Input container */}
        <div
          className={`
          flex items-center
          border-0 dark:border rounded-md bg-card-muted
          ${sizeClasses}
          ${disabled ? 'opacity-60 cursor-not-allowed' : ''}
          ${error ? 'dark:border-red-500' : 'dark:border-border'}
          ${isOpen ? 'ring-2 ring-primary/30' : ''}
          ${fullWidth ? 'w-full' : ''}
        `}
        >
          <input
            ref={inputRef}
            type="text"
            id={id}
            name={name}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onClick={() => !disabled && setIsOpen(true)}
            placeholder={placeholder}
            disabled={disabled}
            className="w-full h-full px-3 bg-transparent border-none focus:outline-none text-foreground"
          />

          <div className="px-2 cursor-pointer" onClick={() => !disabled && setIsOpen(!isOpen)}>
            <svg
              className={`w-4 h-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>

        {/* Error message */}
        {error && <p className="mt-1 text-sm text-red-500">{error}</p>}

        {/* Helper text */}
        {helperText && !error && (
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{helperText}</p>
        )}

        {/* Dropdown */}
        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white dark:bg-dark-light rounded-md shadow-lg max-h-60 overflow-auto animate-fade-in">
            {/* Options */}
            <div role="listbox">
              {filteredOptions.length > 0 ? (
                filteredOptions.map(option => {
                  const isSelected = inputValue === option.label;

                  return (
                    <SelectOption
                      key={`option-${option.value}`}
                      value={option.value}
                      label={option.label}
                      icon={option.icon}
                      disabled={option.disabled}
                      selected={isSelected}
                      onClick={() => handleOptionClick(option)}
                      data={option.data}
                    />
                  );
                })
              ) : (
                <div className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                  {t('common.noResults', 'No results found')}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }
);

ComboboxSelect.displayName = 'ComboboxSelect';

export default ComboboxSelect;
