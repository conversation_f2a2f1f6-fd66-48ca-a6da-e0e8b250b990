/**
 * Query keys cho React Query trong marketplace module
 */

export const PRODUCT_QUERY_KEYS = {
  // Base key
  ALL: ['marketplace', 'products'] as const,

  // Product list keys
  LIST: ['marketplace', 'products', 'list'] as const,
  APPROVED_LIST: ['marketplace', 'products', 'approved'] as const,
  USER_PRODUCTS: ['marketplace', 'products', 'user'] as const,

  // Product detail keys
  DETAIL: (id: number) => ['marketplace', 'products', 'detail', id] as const,
  USER_PRODUCT_DETAIL: (id: number) => ['marketplace', 'products', 'user', id] as const,

  // Cart keys
  CART: ['marketplace', 'cart'] as const,

  // Order keys
  ORDERS: ['marketplace', 'orders'] as const,
  PURCHASE_HISTORY: ['marketplace', 'orders', 'purchase-history'] as const,

  // User balance
  USER_BALANCE: ['marketplace', 'user', 'balance'] as const,

  // Legacy keys for backward compatibility
  CATEGORIES: 'product-categories',
  BRANDS: 'product-brands',
} as const;

/**
 * Utility functions để tạo query keys
 */
export const createProductQueryKey = {
  list: (params?: Record<string, unknown>) => [...PRODUCT_QUERY_KEYS.LIST, params] as const,
  approvedList: (params?: Record<string, unknown>) => [...PRODUCT_QUERY_KEYS.APPROVED_LIST, params] as const,
  userProducts: (params?: Record<string, unknown>) => [...PRODUCT_QUERY_KEYS.USER_PRODUCTS, params] as const,
  detail: (id: number) => PRODUCT_QUERY_KEYS.DETAIL(id),
  userProductDetail: (id: number) => PRODUCT_QUERY_KEYS.USER_PRODUCT_DETAIL(id),
  cart: () => PRODUCT_QUERY_KEYS.CART,
  orders: (params?: Record<string, unknown>) => [...PRODUCT_QUERY_KEYS.ORDERS, params] as const,
  purchaseHistory: (params?: Record<string, unknown>) => [...PRODUCT_QUERY_KEYS.PURCHASE_HISTORY, params] as const,
};
