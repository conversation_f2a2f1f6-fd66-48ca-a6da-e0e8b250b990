import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, getSchemaPath } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ZaloService } from '../services/zalo.service';
import { ZaloAutomation, ZaloAutomationLog } from '../entities';
import {
  CreateZaloAutomationDto,
  UpdateZaloAutomationDto,
  ZaloAutomationLogQueryDto,
  ZaloAutomationLogResponseDto,
  ZaloAutomationQueryDto,
  ZaloAutomationResponseDto,
} from '../dto/zalo';

/**
 * Controller xử lý API liên quan đến tự động hóa Zalo
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_AUTOMATION)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/:oaId/automations')
export class ZaloAutomationController {
  constructor(private readonly zaloService: ZaloService) {}

  /**
   * Lấy danh sách tự động hóa Zalo
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tự động hóa Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách tự động hóa Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZaloAutomationResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getAutomations(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query() queryDto: ZaloAutomationQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloAutomation>>> {
    const result = await this.zaloService.getZaloAutomations(user.id, oaId, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách tự động hóa Zalo thành công');
  }

  /**
   * Lấy thông tin chi tiết tự động hóa Zalo
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết tự động hóa Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết tự động hóa Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloAutomationResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy tự động hóa Zalo' })
  async getAutomationDetail(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ZaloAutomation>> {
    const result = await this.zaloService.getZaloAutomationDetail(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết tự động hóa Zalo thành công');
  }

  /**
   * Tạo tự động hóa Zalo mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo tự động hóa Zalo mới' })
  @ApiResponse({
    status: 201,
    description: 'Tạo tự động hóa Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloAutomationResponseDto) }
          }
        }
      ]
    }
  })
  async createAutomation(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() createDto: CreateZaloAutomationDto,
  ): Promise<ApiResponseDto<ZaloAutomation>> {
    const result = await this.zaloService.createZaloAutomation(user.id, oaId, createDto);
    return ApiResponseDto.success(result, 'Tạo tự động hóa Zalo thành công');
  }

  /**
   * Cập nhật tự động hóa Zalo
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật tự động hóa Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật tự động hóa Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloAutomationResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy tự động hóa Zalo' })
  async updateAutomation(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateZaloAutomationDto,
  ): Promise<ApiResponseDto<ZaloAutomation>> {
    const result = await this.zaloService.updateZaloAutomation(user.id, oaId, id, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật tự động hóa Zalo thành công');
  }

  /**
   * Xóa tự động hóa Zalo
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa tự động hóa Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Xóa tự động hóa Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { type: 'boolean' }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy tự động hóa Zalo' })
  async deleteAutomation(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.zaloService.deleteZaloAutomation(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Xóa tự động hóa Zalo thành công');
  }

  /**
   * Kích hoạt tự động hóa Zalo
   */
  @Post(':id/activate')
  @ApiOperation({ summary: 'Kích hoạt tự động hóa Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Kích hoạt tự động hóa Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloAutomationResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy tự động hóa Zalo' })
  async activateAutomation(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ZaloAutomation>> {
    const result = await this.zaloService.activateZaloAutomation(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Kích hoạt tự động hóa Zalo thành công');
  }

  /**
   * Vô hiệu hóa tự động hóa Zalo
   */
  @Post(':id/deactivate')
  @ApiOperation({ summary: 'Vô hiệu hóa tự động hóa Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Vô hiệu hóa tự động hóa Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloAutomationResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy tự động hóa Zalo' })
  async deactivateAutomation(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ZaloAutomation>> {
    const result = await this.zaloService.deactivateZaloAutomation(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Vô hiệu hóa tự động hóa Zalo thành công');
  }

  /**
   * Lấy lịch sử thực thi tự động hóa Zalo
   */
  @Get(':id/logs')
  @ApiOperation({ summary: 'Lấy lịch sử thực thi tự động hóa Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Lấy lịch sử thực thi tự động hóa Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZaloAutomationLogResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy tự động hóa Zalo' })
  async getAutomationLogs(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
    @Query() queryDto: ZaloAutomationLogQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloAutomationLog>>> {
    const result = await this.zaloService.getZaloAutomationLogs(user.id, oaId, id, queryDto);
    return ApiResponseDto.success(result, 'Lấy lịch sử thực thi tự động hóa Zalo thành công');
  }
}
