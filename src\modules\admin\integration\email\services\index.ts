import { apiClient } from '@/shared/api/axios';
import { ApiResponse, PaginatedResult } from '@/shared/types/api';
import {
  EmailServerConfiguration,
  CreateEmailServerDto,
  UpdateEmailServerDto,
  TestEmailServerDto,
  EmailServerQueryParams,
  EmailServerTestResult,
} from '../types';

/**
 * Email Server Configuration Service
 */
export class EmailServerService {
  private static readonly BASE_URL = '/admin/integration/email-server';

  /**
   * L<PERSON>y danh sách cấu hình máy chủ email
   */
  static async getEmailServers(
    params?: EmailServerQueryParams
  ): Promise<ApiResponse<PaginatedResult<EmailServerConfiguration>>> {
    const response = await apiClient.get(this.BASE_URL, { params });
    return response.data;
  }

  /**
   * L<PERSON>y thông tin chi tiết cấu hình máy chủ email
   */
  static async getEmailServer(id: number): Promise<ApiResponse<EmailServerConfiguration>> {
    const response = await apiClient.get(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  /**
   * Tạo mới cấu hình máy chủ email
   */
  static async createEmailServer(
    data: CreateEmailServerDto
  ): Promise<ApiResponse<EmailServerConfiguration>> {
    const response = await apiClient.post(this.BASE_URL, data);
    return response.data;
  }

  /**
   * Cập nhật cấu hình máy chủ email
   */
  static async updateEmailServer(
    id: number,
    data: UpdateEmailServerDto
  ): Promise<ApiResponse<EmailServerConfiguration>> {
    const response = await apiClient.put(`${this.BASE_URL}/${id}`, data);
    return response.data;
  }

  /**
   * Xóa cấu hình máy chủ email
   */
  static async deleteEmailServer(id: number): Promise<ApiResponse<{ message: string }>> {
    const response = await apiClient.delete(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  /**
   * Kiểm tra kết nối máy chủ email
   */
  static async testEmailServerConnection(
    id: number,
    data: TestEmailServerDto
  ): Promise<ApiResponse<EmailServerTestResult>> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/test`, data);
    return response.data;
  }
}
