import React from 'react';
import { ResponsiveGrid } from '@/shared/components/common';
import TypeAgentCard, { TypeAgent } from './TypeAgentCard';
import CustomTypeAgentCard from './CustomTypeAgentCard';

export interface TypeAgentGridProps {
  /**
   * Mảng các type agent để hiển thị
   */
  agents?: TypeAgent[];

  /**
   * ID của agent đ<PERSON><PERSON><PERSON> chọ<PERSON>
   */
  selectedAgentId?: number | null;

  /**
   * Có đang chọn custom agent không
   */
  isCustomAgentSelected?: boolean;

  /**
   * Hàm xử lý khi chọn agent
   */
  onSelectAgent?: (id: number) => void;

  /**
   * Hàm xử lý khi chọn custom agent
   */
  onCustomAgentClick?: () => void;

  /**
   * Hàm xử lý khi xem chi tiết agent
   */
  onViewAgent?: (id: number) => void;

  /**
   * Hàm xử lý khi xóa agent
   */
  onDeleteAgent?: (id: number) => void;

  /**
   * <PERSON><PERSON> hiển thị action buttons không
   */
  showActions?: boolean;
}

/**
 * Component hiển thị grid các Type Agent
 */
const TypeAgentGrid: React.FC<TypeAgentGridProps> = ({
  agents,
  selectedAgentId = null,
  isCustomAgentSelected = false,
  onSelectAgent,
  onCustomAgentClick,
  onViewAgent,
  onDeleteAgent,
  showActions = false
}) => {

  // Sử dụng dữ liệu được truyền vào hoặc dữ liệu mẫu
  const displayAgents = agents && agents.length > 0 ? agents : [];

  // Xử lý khi chọn agent
  const handleSelectAgent = (id: number) => {
    if (onSelectAgent) {
      onSelectAgent(id);
    }
  };

  // Xử lý khi chọn custom agent
  const handleCustomAgentClick = () => {
    if (onCustomAgentClick) {
      onCustomAgentClick();
    }
  };

  return (
    <ResponsiveGrid
      maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4, xl: 5 }}
      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 4 }}
      gap={6}
      className="w-full"
    >
      {/* Custom Agent Card luôn hiển thị đầu tiên */}
      <CustomTypeAgentCard
        isSelected={isCustomAgentSelected}
        onClick={handleCustomAgentClick}
      />

      {/* Các Type Agent Cards */}
      {displayAgents.map((agent) => (
        <TypeAgentCard
          key={agent.id}
          agent={agent}
          isSelected={selectedAgentId === agent.id}
          onClick={handleSelectAgent}
          onView={onViewAgent}
          onDelete={onDeleteAgent}
          showActions={showActions}
        />
      ))}
    </ResponsiveGrid>
  );
};

export default TypeAgentGrid;
