import { Injectable, Logger } from '@nestjs/common';
import { AdminEmailServerConfigurationRepository } from '../../repositories';
import { CreateEmailServerDto, TestEmailServerDto, UpdateEmailServerDto } from '../../user/dto';
import { AdminEmailServerConfigurationEntity } from '@modules/integration/entities/admin_email_server_configurations.entity';
import { EmailServerQueryDto } from '../dto';
import { PaginatedResult } from '@/common/response';
import * as nodemailer from 'nodemailer';
import { EncryptionService } from '@shared/services/encryption.service';
import { AppException } from '@common/exceptions';
import { INTEGRATION_ERROR_CODES } from '../../exceptions/integration-error.code';

@Injectable()
export class AdminEmailServerConfigurationService {
  private readonly logger = new Logger(AdminEmailServerConfigurationService.name);

  constructor(
    private readonly emailServerConfigurationRepository: AdminEmailServerConfigurationRepository,
    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * Lấy danh sách cấu hình máy chủ email với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách cấu hình máy chủ email với phân trang
   */
  async findAll(queryDto: EmailServerQueryDto): Promise<PaginatedResult<AdminEmailServerConfigurationEntity>> {
    try {
      const { page = 1, limit = 10, search, userId } = queryDto;

      // Tạo query builder
      const queryBuilder = this.emailServerConfigurationRepository.createQueryBuilder('email_server');

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        queryBuilder.andWhere('(email_server.server_name LIKE :search OR email_server.host LIKE :search)', {
          search: `%${search}%`,
        });
      }

      // Lọc theo userId nếu có
      if (userId) {
        queryBuilder.andWhere('email_server.user_id = :userId', { userId });
      }

      // Thêm thông tin người dùng nếu cần
      queryBuilder.leftJoinAndSelect('email_server.user', 'user');

      // Đếm tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Lấy dữ liệu với phân trang
      const items = await queryBuilder
        .orderBy('email_server.created_at', 'DESC')
        .skip((page - 1) * limit)
        .take(limit)
        .getMany();

      // Che giấu mật khẩu
      const maskedItems = items.map(item => {
        const { password, ...rest } = item;
        return { ...rest, password: '********' } as AdminEmailServerConfigurationEntity;
      });

      return {
        items: maskedItems,
        meta: {
          totalItems: total,
          itemCount: maskedItems.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách cấu hình máy chủ email: ${error.message}`);
      throw new AppException(
        INTEGRATION_ERROR_CODES.EMAIL_SERVER_LIST_FAILED,
        'Không thể lấy danh sách cấu hình máy chủ email'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của một cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @returns Thông tin chi tiết của cấu hình máy chủ email
   */
  async findOne(id: number): Promise<AdminEmailServerConfigurationEntity> {
    try {
      const emailServer = await this.emailServerConfigurationRepository.findOne({
        where: { id },
        relations: ['user'],
      });

      if (!emailServer) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND,
          `Không tìm thấy cấu hình máy chủ email với ID ${id}`
        );
      }

      // Che giấu mật khẩu
      const { password, ...rest } = emailServer;
      return { ...rest, password: '********' } as AdminEmailServerConfigurationEntity;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy thông tin chi tiết cấu hình máy chủ email ID ${id}: ${error.message}`);
      throw new AppException(
        INTEGRATION_ERROR_CODES.EMAIL_SERVER_DETAIL_FAILED,
        `Không thể lấy thông tin chi tiết cấu hình máy chủ email: ${error.message}`
      );
    }
  }

  /**
   * Tạo mới cấu hình máy chủ email
   * @param createEmailServerDto Thông tin cấu hình máy chủ email cần tạo
   * @param employeeId ID của nhân viên tạo
   * @returns Cấu hình máy chủ email đã được tạo
   */
  async create(createEmailServerDto: CreateEmailServerDto, employeeId: number): Promise<AdminEmailServerConfigurationEntity> {
    try {
      const now = Date.now();

      // Mã hóa mật khẩu trước khi lưu
      const encryptedPassword = this.encryptionService.encrypt(createEmailServerDto.password);
      const dtoWithEncryptedPassword = {
        ...createEmailServerDto,
        password: encryptedPassword,
      };

      // Tạo đối tượng cấu hình máy chủ email
      const emailServer = this.emailServerConfigurationRepository.create({
        ...dtoWithEncryptedPassword,
        createdAt: now,
        updatedAt: now,
        createdBy: employeeId,
      });

      // Lưu vào database
      const savedServer = await this.emailServerConfigurationRepository.save(emailServer);

      // Trả về đối tượng đã lưu nhưng che mật khẩu
      const { password, ...serverWithoutPassword } = savedServer;
      return { ...serverWithoutPassword, password: '********' } as AdminEmailServerConfigurationEntity;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo cấu hình máy chủ email: ${error.message}`);
      throw new AppException(
        INTEGRATION_ERROR_CODES.EMAIL_SERVER_CREATE_FAILED,
        `Không thể tạo cấu hình máy chủ email: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật thông tin cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param updateEmailServerDto Thông tin cần cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns Cấu hình máy chủ email đã được cập nhật
   */
  async update(id: number, updateEmailServerDto: UpdateEmailServerDto, employeeId: number): Promise<AdminEmailServerConfigurationEntity> {
    try {
      // Kiểm tra cấu hình máy chủ email tồn tại
      const originalServer = await this.emailServerConfigurationRepository.findOne({
        where: { id },
      });

      if (!originalServer) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND,
          `Không tìm thấy cấu hình máy chủ email với ID ${id}`
        );
      }

      // Cập nhật thông tin
      const updatedData: any = {
        ...updateEmailServerDto,
        updatedAt: Date.now(),
        updatedBy: employeeId,
      };

      // Mã hóa mật khẩu mới nếu có
      if (updateEmailServerDto.password) {
        updatedData.password = this.encryptionService.encrypt(updateEmailServerDto.password);
      }

      // Cập nhật vào database
      await this.emailServerConfigurationRepository.update(id, updatedData);

      // Lấy dữ liệu đã cập nhật
      const updatedServer = await this.emailServerConfigurationRepository.findOne({
        where: { id },
        relations: ['user'],
      });

      // Trả về đối tượng đã cập nhật nhưng che mật khẩu
      if (updatedServer) {
        const { password, ...serverWithoutPassword } = updatedServer;
        return { ...serverWithoutPassword, password: '********' } as AdminEmailServerConfigurationEntity;
      }

      // Nếu không tìm thấy server sau khi cập nhật, trả về thông tin cơ bản
      return {
        id,
        ...updateEmailServerDto,
        password: '********',
        updatedAt: Date.now(),
        updatedBy: employeeId,
        createdAt: Date.now(),
        createdBy: employeeId,
      } as unknown as AdminEmailServerConfigurationEntity;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi cập nhật cấu hình máy chủ email ID ${id}: ${error.message}`);
      throw new AppException(
        INTEGRATION_ERROR_CODES.EMAIL_SERVER_UPDATE_FAILED,
        `Không thể cập nhật cấu hình máy chủ email: ${error.message}`
      );
    }
  }

  /**
   * Xóa cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param employeeId ID của nhân viên thực hiện
   * @returns Thông báo kết quả
   */
  async remove(id: number, _employeeId: number): Promise<{ message: string }> {
    try {
      // Kiểm tra cấu hình máy chủ email tồn tại
      const emailServer = await this.emailServerConfigurationRepository.findOne({
        where: { id },
      });

      if (!emailServer) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND,
          `Không tìm thấy cấu hình máy chủ email với ID ${id}`
        );
      }

      // Xóa khỏi database
      await this.emailServerConfigurationRepository.delete(id);

      return { message: 'Cấu hình máy chủ email đã được xóa' };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi xóa cấu hình máy chủ email ID ${id}: ${error.message}`);
      throw new AppException(
        INTEGRATION_ERROR_CODES.EMAIL_SERVER_DELETE_FAILED,
        `Không thể xóa cấu hình máy chủ email: ${error.message}`
      );
    }
  }

  /**
   * Lấy thông tin máy chủ email với mật khẩu đã giải mã
   * @param id ID của cấu hình máy chủ email
   * @returns Thông tin máy chủ email với mật khẩu đã giải mã
   */
  private async findOneWithDecryptedPassword(id: number): Promise<AdminEmailServerConfigurationEntity> {
    const emailServer = await this.emailServerConfigurationRepository.findOne({
      where: { id },
    });

    if (!emailServer) {
      throw new AppException(
        INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND,
        `Không tìm thấy cấu hình máy chủ email với ID ${id}`
      );
    }

    // Giải mã mật khẩu
    const decryptedPassword = this.encryptionService.decrypt(emailServer.password);

    return {
      ...emailServer,
      password: decryptedPassword,
    };
  }

  /**
   * Kiểm tra kết nối máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param testEmailServerDto Thông tin kiểm tra
   * @returns Kết quả kiểm tra
   */
  async testConnection(id: number, testEmailServerDto: TestEmailServerDto): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      // Lấy thông tin máy chủ email với mật khẩu đã giải mã
      const emailServer = await this.findOneWithDecryptedPassword(id);

      // Tạo transporter
      const transporter = nodemailer.createTransport({
        host: emailServer.host,
        port: emailServer.port,
        secure: emailServer.useSsl,
        auth: {
          user: emailServer.username,
          pass: emailServer.password, // Sử dụng mật khẩu đã giải mã
        },
        ...emailServer.additionalSettings,
      });

      // Kiểm tra kết nối
      await transporter.verify();

      // Gửi email kiểm tra nếu có recipientEmail
      if (testEmailServerDto.recipientEmail) {
        const subject = testEmailServerDto.subject || 'Kiểm tra kết nối máy chủ email';

        await transporter.sendMail({
          from: emailServer.username,
          to: testEmailServerDto.recipientEmail,
          subject: subject,
          text: 'Đây là email kiểm tra kết nối máy chủ email. Nếu bạn nhận được email này, kết nối đã thành công.',
          html: '<p>Đây là email kiểm tra kết nối máy chủ email. Nếu bạn nhận được email này, kết nối đã thành công.</p>',
        });

        return {
          success: true,
          message: 'Kết nối thành công! Email kiểm tra đã được gửi.',
        };
      }

      return {
        success: true,
        message: 'Kết nối thành công!',
      };
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra kết nối máy chủ email ID ${id}: ${error.message}`);
      return {
        success: false,
        message: 'Kết nối thất bại!',
        details: error.message,
      };
    }
  }
}
