import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Table, SlideInForm, ConfirmDeleteModal } from '@/shared/components/common';
import { MenuIconBar, ActiveFilters } from '@/shared/components/layout';
import { TableColumn, ColumnVisibility, SortDirection } from '@/shared/components/common/Table/types';
import { useDataTable } from '@/shared/hooks/useDataTable';
import { useSlideForm } from '@/shared/hooks/useSlideForm';

import {
  useProviderModels,
  useCreateProviderModel,
  useUpdateProviderModel,
  useDeleteProviderModel,
} from '../provider-model/hooks';
import {
  ProviderModelListItem,
  ProviderModel,
  CreateProviderModelDto,
  UpdateProviderModelDto,
  getProviderDisplayName,
  getProviderIcon,
} from '../provider-model/types';
import ProviderModelForm from '../components/ProviderModelForm';
import Icon from '@/shared/components/common/Icon';

/**
 * Trang quản lý Provider Model
 */
const ProviderModelManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho các modal/form
  const [providerModelToEdit, setProviderModelToEdit] = useState<ProviderModel | null>(null);
  const [providerModelToView, setProviderModelToView] = useState<ProviderModel | null>(null);
  const [providerModelToDelete, setProviderModelToDelete] = useState<ProviderModelListItem | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Hooks cho slide forms
  const {
    isVisible: isCreateFormVisible,
    show: showCreateForm,
    hide: hideCreateForm,
  } = useSlideForm();

  const {
    isVisible: isEditFormVisible,
    show: showEditForm,
    hide: hideEditForm,
  } = useSlideForm();

  const {
    isVisible: isViewFormVisible,
    show: showViewForm,
    hide: hideViewForm,
  } = useSlideForm();

  // Hook cho data table
  const dataTable = useDataTable({
    defaultSortBy: 'createdAt',
    defaultSortDirection: 'desc' as SortDirection,
  });

  // State cho column visibility
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Query provider models
  const {
    data: providerModelsResponse,
    isLoading,
    error,
  } = useProviderModels({
    page: dataTable.tableData.currentPage,
    limit: dataTable.tableData.pageSize,
    search: dataTable.tableData.searchTerm,
  });

  const providerModels = providerModelsResponse?.data?.items || [];
  const totalItems = providerModelsResponse?.data?.meta?.total || 0;

  // Mutations
  const createProviderModelMutation = useCreateProviderModel();
  const updateProviderModelMutation = useUpdateProviderModel();
  const deleteProviderModelMutation = useDeleteProviderModel();

  // Định nghĩa columns cho table
  const columns: TableColumn<ProviderModelListItem>[] = useMemo(() => [
    {
      key: 'name',
      title: t('admin:integration.providerModel.list.columns.name'),
      sortable: true,
      render: (item) => (
        <div className="flex items-center space-x-3">
          <Icon name={getProviderIcon(item.type)} size="sm" />
          <div>
            <div className="font-medium text-gray-900 dark:text-gray-100">
              {item.name}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {getProviderDisplayName(item.type)}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'type',
      title: t('admin:integration.providerModel.list.columns.type'),
      sortable: true,
      render: (item) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
          {getProviderDisplayName(item.type)}
        </span>
      ),
    },
    {
      key: 'createdAt',
      title: t('admin:integration.providerModel.list.columns.createdAt'),
      sortable: true,
      render: (item) => new Date(item.createdAt).toLocaleDateString(),
    },
    {
      key: 'actions',
      title: t('admin:integration.providerModel.list.columns.actions'),
      render: (item) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewProviderModel(item.id)}
          >
            {t('admin:integration.providerModel.actions.view')}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditProviderModel(item.id)}
          >
            {t('admin:integration.providerModel.actions.edit')}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteProviderModel(item)}
            className="text-red-600 hover:text-red-700"
          >
            {t('admin:integration.providerModel.actions.delete')}
          </Button>
        </div>
      ),
    },
  ], [t]);

  // Handlers cho table actions
  const handleViewProviderModel = useCallback(async (id: string) => {
    try {
      const { ProviderModelService } = await import('../provider-model/services');
      const response = await ProviderModelService.getProviderModel(id);
      setProviderModelToView(response.data);
      showViewForm();
    } catch (error) {
      console.error('Error fetching provider model:', error);
    }
  }, [showViewForm]);

  const handleEditProviderModel = useCallback(async (id: string) => {
    try {
      const { ProviderModelService } = await import('../provider-model/services');
      const response = await ProviderModelService.getProviderModel(id);
      setProviderModelToEdit(response.data);
      showEditForm();
    } catch (error) {
      console.error('Error fetching provider model:', error);
    }
  }, [showEditForm]);

  const handleDeleteProviderModel = useCallback((providerModel: ProviderModelListItem) => {
    setProviderModelToDelete(providerModel);
    setShowDeleteConfirm(true);
  }, []);

  // Handlers cho search và filter
  const handleSearch = useCallback((searchTerm: string) => {
    dataTable.updateSearch(searchTerm);
  }, [dataTable]);

  const handleClearSearch = useCallback(() => {
    dataTable.clearSearch();
  }, [dataTable]);

  const handleSortChange = useCallback((sortBy: string, sortDirection: SortDirection) => {
    dataTable.updateSort(sortBy, sortDirection);
  }, [dataTable]);

  const handlePageChange = useCallback((page: number, pageSize?: number) => {
    dataTable.updatePagination(page, pageSize);
  }, [dataTable]);

  const handleClearSort = useCallback(() => {
    dataTable.clearSort();
  }, [dataTable]);

  const handleClearAll = useCallback(() => {
    dataTable.clearAll();
  }, [dataTable]);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!providerModelToDelete) return;

    try {
      await deleteProviderModelMutation.mutateAsync(providerModelToDelete.id);
      setShowDeleteConfirm(false);
      setProviderModelToDelete(null);
    } catch (error) {
      console.error('Error deleting provider model:', error);
    }
  }, [providerModelToDelete, deleteProviderModelMutation]);

  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setProviderModelToDelete(null);
  }, []);

  // Xử lý submit form tạo mới
  const handleSubmitCreateProviderModel = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        setIsSubmitting(true);
        const createData: CreateProviderModelDto = values as unknown as CreateProviderModelDto;

        await createProviderModelMutation.mutateAsync(createData);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating provider model:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [createProviderModelMutation, hideCreateForm]
  );

  // Xử lý submit form chỉnh sửa
  const handleSubmitEditProviderModel = useCallback(
    async (values: Record<string, unknown>) => {
      if (!providerModelToEdit) return;

      try {
        setIsSubmitting(true);
        const updateData: UpdateProviderModelDto = values as unknown as UpdateProviderModelDto;

        await updateProviderModelMutation.mutateAsync({
          id: providerModelToEdit.id,
          data: updateData,
        });
        hideEditForm();
        setProviderModelToEdit(null);
      } catch (error) {
        console.error('Error updating provider model:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [providerModelToEdit, updateProviderModelMutation, hideEditForm]
  );

  // Xử lý đóng form chỉnh sửa
  const handleCloseEditForm = useCallback(() => {
    hideEditForm();
    setTimeout(() => {
      setProviderModelToEdit(null);
    }, 300);
  }, [hideEditForm]);

  // Xử lý đóng form xem chi tiết
  const handleCloseViewForm = useCallback(() => {
    hideViewForm();
    setTimeout(() => {
      setProviderModelToView(null);
    }, 300);
  }, [hideViewForm]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo<TableColumn<ProviderModelListItem>[]>(() => {
    if (visibleColumns.length === 0) {
      setVisibleColumns([
        { id: 'all', label: t('common:all'), visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns, t]);

  return (
    <div className="space-y-6">
        {/* Header */}
        <div>
          <Typography variant="h4" className="text-gray-900 dark:text-gray-100">
            {t('admin:integration.providerModel.title')}
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mt-1">
            {t('admin:integration.providerModel.description')}
          </Typography>
        </div>

        {/* Controls */}
        <div>
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showCreateForm()}
            items={[
              {
                id: 'all',
                label: t('common:all'),
                icon: 'list',
                onClick: () => '',
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
          />

          <ActiveFilters
            searchTerm={dataTable.tableData.searchTerm}
            onClearSearch={handleClearSearch}
            sortBy={dataTable.tableData.sortBy}
            sortDirection={dataTable.tableData.sortDirection as SortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <ProviderModelForm
            onSubmit={handleSubmitCreateProviderModel}
            onCancel={hideCreateForm}
            isSubmitting={isSubmitting}
          />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {providerModelToEdit && (
            <ProviderModelForm
              initialData={providerModelToEdit}
              onSubmit={handleSubmitEditProviderModel}
              onCancel={handleCloseEditForm}
              isSubmitting={isSubmitting}
            />
          )}
        </SlideInForm>

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {providerModelToView && (
            <ProviderModelForm
              initialData={providerModelToView}
              onSubmit={() => {}}
              onCancel={handleCloseViewForm}
              readOnly={true}
            />
          )}
        </SlideInForm>

        {/* Table */}
        <Card className="overflow-hidden">
          <Table<ProviderModelListItem>
            columns={filteredColumns}
            data={providerModels}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={handleSortChange}
            pagination={{
              current: dataTable.tableData.currentPage,
              pageSize: dataTable.tableData.pageSize,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin:integration.providerModel.confirmations.deleteTitle')}
        message={t('admin:integration.providerModel.confirmations.delete')}
        itemName={providerModelToDelete?.name}
      />
    </div>
  );
};

export default ProviderModelManagementPage;
