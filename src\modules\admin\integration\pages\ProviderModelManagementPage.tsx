import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ConfirmDeleteModal } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { ActiveFilters } from '@/modules/components/filters';
import ModernMenuTrigger from '@/shared/components/common/ModernMenu/ModernMenuTrigger';

import useSlideForm from '@/shared/hooks/useSlideForm';

import {
  useProviderModels,
  useCreateProviderModel,
  useUpdateProviderModel,
  useDeleteProviderModel,
} from '../provider-model/hooks';
import {
  ProviderModelListItem,
  ProviderModel,
  CreateProviderModelDto,
  UpdateProviderModelDto,
  getProviderDisplayName,
  getProviderIcon,
} from '../provider-model/types';
import ProviderModelForm from '../components/ProviderModelForm';
import Icon from '@/shared/components/common/Icon';

/**
 * Trang quản lý Provider Model
 */
const ProviderModelManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho các modal/form
  const [providerModelToEdit, setProviderModelToEdit] = useState<ProviderModel | null>(null);
  const [providerModelToView, setProviderModelToView] = useState<ProviderModel | null>(null);
  const [providerModelToDelete, setProviderModelToDelete] = useState<ProviderModelListItem | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Hooks cho slide forms
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  const {
    isVisible: isViewFormVisible,
    showForm: showViewForm,
    hideForm: hideViewForm,
  } = useSlideForm();

  // State cho column visibility
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // State cho table data
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<string | null>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // Query provider models
  const {
    data: providerModelsResponse,
    isLoading,
  } = useProviderModels({
    page: currentPage,
    limit: pageSize,
    search: searchTerm,
  });

  const providerModels = providerModelsResponse?.result?.items || [];
  const totalItems = providerModelsResponse?.result?.meta?.totalItems || 0;

  // Mutations
  const createProviderModelMutation = useCreateProviderModel();
  const updateProviderModelMutation = useUpdateProviderModel();
  const deleteProviderModelMutation = useDeleteProviderModel();

  // Handlers cho table actions
  const handleViewProviderModel = useCallback(async (id: string) => {
    try {
      const { ProviderModelService } = await import('../provider-model/services');
      const response = await ProviderModelService.getProviderModel(id);
      setProviderModelToView(response.result);
      showViewForm();
    } catch (error) {
      console.error('Error fetching provider model:', error);
    }
  }, [showViewForm]);

  const handleEditProviderModel = useCallback(async (id: string) => {
    try {
      const { ProviderModelService } = await import('../provider-model/services');
      const response = await ProviderModelService.getProviderModel(id);
      setProviderModelToEdit(response.result);
      showEditForm();
    } catch (error) {
      console.error('Error fetching provider model:', error);
    }
  }, [showEditForm]);

  const handleDeleteProviderModel = useCallback((providerModel: ProviderModelListItem) => {
    setProviderModelToDelete(providerModel);
    setShowDeleteConfirm(true);
  }, []);

  // Định nghĩa columns cho table
  const columns: TableColumn<ProviderModelListItem>[] = useMemo(() => [
    {
      key: 'name',
      title: t('admin:integration.providerModel.list.columns.name'),
      sortable: true,
      render: (_: unknown, record: ProviderModelListItem) => (
        <div className="flex items-center space-x-3">
          <Icon name={getProviderIcon(record.type)} size="sm" />
          <div>
            <div className="font-medium text-gray-900 dark:text-gray-100">
              {record.name}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {getProviderDisplayName(record.type)}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'type',
      title: t('admin:integration.providerModel.list.columns.type'),
      sortable: true,
      render: (_: unknown, record: ProviderModelListItem) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
          {getProviderDisplayName(record.type)}
        </span>
      ),
    },
    {
      key: 'createdAt',
      title: t('admin:integration.providerModel.list.columns.createdAt'),
      sortable: true,
      render: (value: unknown) => {
       
          // Kiểm tra và chuyển đổi timestamp thành Date
          if (value) {
            const timestamp = typeof value === 'string' ? parseInt(value, 10) : (value as number);
            if (!isNaN(timestamp)) {
              return new Date(timestamp).toLocaleDateString('vi-VN');
            }
          }
          return '-';
        
      }
    },
    {
      key: 'actions',
      title: t('admin:integration.providerModel.list.columns.actions'),
      render: (_: unknown, record: ProviderModelListItem) => {
        const menuItems = [
          {
            label: t('admin:integration.providerModel.actions.edit'),
            icon: 'edit',
            onClick: () => handleEditProviderModel(record.id),
          },
          {
            label: t('admin:integration.providerModel.actions.view'),
            icon: 'eye',
            onClick: () => handleViewProviderModel(record.id),
          },
          {
            label: t('admin:integration.providerModel.actions.delete'),
            icon: 'trash',
            onClick: () => handleDeleteProviderModel(record),
            variant: 'primary' as const,
          },
        ];

        return <ModernMenuTrigger items={menuItems} placement="left" />;
      },
    },
  ], [t, handleViewProviderModel, handleEditProviderModel, handleDeleteProviderModel]);

  // Handlers cho search và filter
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang đầu khi search
  }, []);

  const handleClearSearch = useCallback(() => {
    setSearchTerm('');
    setCurrentPage(1);
  }, []);

  const handleSortChange = useCallback((column: string | null, order: 'asc' | 'desc' | null) => {
    if (column && order) {
      setSortBy(column);
      setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
    }
  }, []);

  const handlePageChange = useCallback((page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
  }, []);

  const handleClearSort = useCallback(() => {
    setSortBy(null);
    setSortDirection(SortDirection.DESC);
  }, []);

  const handleClearAll = useCallback(() => {
    setSearchTerm('');
    setSortBy(null);
    setSortDirection(SortDirection.DESC);
    setCurrentPage(1);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!providerModelToDelete) return;

    try {
      await deleteProviderModelMutation.mutateAsync(providerModelToDelete.id);
      setShowDeleteConfirm(false);
      setProviderModelToDelete(null);
    } catch (error) {
      console.error('Error deleting provider model:', error);
    }
  }, [providerModelToDelete, deleteProviderModelMutation]);

  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setProviderModelToDelete(null);
  }, []);

  // Xử lý submit form tạo mới
  const handleSubmitCreateProviderModel = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        setIsSubmitting(true);
        const createData: CreateProviderModelDto = values as unknown as CreateProviderModelDto;

        await createProviderModelMutation.mutateAsync(createData);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating provider model:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [createProviderModelMutation, hideCreateForm]
  );

  // Xử lý submit form chỉnh sửa
  const handleSubmitEditProviderModel = useCallback(
    async (values: Record<string, unknown>) => {
      if (!providerModelToEdit) return;

      try {
        setIsSubmitting(true);
        const updateData: UpdateProviderModelDto = values as unknown as UpdateProviderModelDto;

        await updateProviderModelMutation.mutateAsync({
          id: providerModelToEdit.id,
          data: updateData,
        });
        hideEditForm();
        setProviderModelToEdit(null);
      } catch (error) {
        console.error('Error updating provider model:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [providerModelToEdit, updateProviderModelMutation, hideEditForm]
  );

  // Xử lý đóng form chỉnh sửa
  const handleCloseEditForm = useCallback(() => {
    hideEditForm();
    setTimeout(() => {
      setProviderModelToEdit(null);
    }, 300);
  }, [hideEditForm]);

  // Xử lý đóng form xem chi tiết
  const handleCloseViewForm = useCallback(() => {
    hideViewForm();
    setTimeout(() => {
      setProviderModelToView(null);
    }, 300);
  }, [hideViewForm]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo<TableColumn<ProviderModelListItem>[]>(() => {
    if (visibleColumns.length === 0) {
      setVisibleColumns([
        { id: 'all', label: t('common:all'), visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns, t]);

  return (
    <>
      <div className="space-y-6">
  

        {/* Controls */}
        <div>
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showCreateForm()}
            items={[
              {
                id: 'all',
                label: t('common:all'),
                icon: 'list',
                onClick: () => '',
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
          />

          <ActiveFilters
            searchTerm={searchTerm}
            onClearSearch={handleClearSearch}
            sortBy={sortBy}
            sortDirection={sortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <ProviderModelForm
            onSubmit={handleSubmitCreateProviderModel}
            onCancel={hideCreateForm}
            isSubmitting={isSubmitting}
          />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {providerModelToEdit && (
            <ProviderModelForm
              initialData={providerModelToEdit}
              onSubmit={handleSubmitEditProviderModel}
              onCancel={handleCloseEditForm}
              isSubmitting={isSubmitting}
            />
          )}
        </SlideInForm>

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {providerModelToView && (
            <ProviderModelForm
              initialData={providerModelToView}
              onSubmit={() => {}}
              onCancel={handleCloseViewForm}
              readOnly={true}
            />
          )}
        </SlideInForm>

        {/* Table */}
        <Card className="overflow-hidden">
          <Table<ProviderModelListItem>
            columns={filteredColumns}
            data={providerModels}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={handleSortChange}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin:integration.providerModel.confirmations.deleteTitle')}
        message={t('admin:integration.providerModel.confirmations.delete')}
        itemName={providerModelToDelete?.name}
      />
    </>
  );
};

export default ProviderModelManagementPage;
