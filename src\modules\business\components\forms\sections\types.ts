// Interface cho dữ liệu khách hàng chi tiết
export interface CustomerDetailData {
  id: string;
  name: string;
  email: string;
  phone: string;
  address?: string;
  avatar?: string;
  tags?: string[];
  status: 'active' | 'inactive' | 'blocked';
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lastOrderDate?: string;
  customerSince: string;
  // Overview data
  flowCount?: number;
  campaignCount?: number;
  sequenceCount?: number;
  topChannels?: TopChannel[];
  topDevices?: TopDevice[];
  socialProfiles?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
    zalo?: string;
    website?: string;
  };
  customFields?: Record<string, unknown>;
  interactions?: CustomerInteraction[];
  orders?: CustomerOrder[];
  activities?: CustomerActivity[];
}

// Interface cho top kênh
export interface TopChannel {
  id: string;
  name: string;
  count: number;
  percentage: number;
  icon?: string;
}

// Interface cho top thiết bị
export interface TopDevice {
  id: string;
  name: string;
  count: number;
  percentage: number;
  icon?: string;
}

// Interface cho tương tác khách hàng
export interface CustomerInteraction {
  id: string;
  type: 'email' | 'phone' | 'chat' | 'social' | 'meeting';
  channel: string;
  title: string;
  description?: string;
  date: string;
  status: 'completed' | 'pending' | 'failed';
}

// Interface cho đơn hàng khách hàng
export interface CustomerOrder {
  id: string;
  orderCode: string;
  date: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentMethod: string;
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  totalAmount: number;
  items: number;
  shippingAddress?: string;
}

// Interface cho hoạt động khách hàng
export interface CustomerActivity {
  id: string;
  type: 'order' | 'login' | 'profile_update' | 'support' | 'review' | 'payment';
  title: string;
  description?: string;
  date: string;
  metadata?: Record<string, unknown>;
}
