import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, Chip, Avatar, Badge, Icon } from '@/shared/components/common';
import { CustomerDetailData } from './types';

interface CustomerGeneralInfoProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị thông tin chung của khách hàng
 */
const CustomerGeneralInfo: React.FC<CustomerGeneralInfoProps> = ({ customer }) => {
  const { t } = useTranslation('business');

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  // Get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'blocked':
        return 'danger';
      default:
        return 'info';
    }
  };

  // Get status text
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return t('customer.status.active');
      case 'inactive':
        return t('customer.status.inactive');
      case 'blocked':
        return t('customer.status.blocked');
      default:
        return status;
    }
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="text-foreground">
            {t('customer.detail.generalInfo')}
          </Typography>
          <Badge variant={getStatusVariant(customer.status)}>
            {getStatusText(customer.status)}
          </Badge>
        </div>
      }
      defaultOpen={true}
    >
      <div className="space-y-6">
        {/* Avatar và thông tin cơ bản */}
        <div className="flex items-start space-x-4">
          <Avatar
            src={customer.avatar || `https://i.pravatar.cc/150?img=${customer.id}`}
            alt={customer.name}
            size="xl"
          />
          <div className="flex-1">
            <Typography variant="h5" className="text-foreground mb-1">
              {customer.name}
            </Typography>
            <Typography variant="body2" className="text-muted mb-2">
              ID: {customer.id}
            </Typography>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-1">
                <Icon name="mail" size="sm" className="text-muted" />
                <Typography variant="body2" className="text-foreground">
                  {customer.email}
                </Typography>
              </div>
              <div className="flex items-center space-x-1">
                <Icon name="phone" size="sm" className="text-muted" />
                <Typography variant="body2" className="text-foreground">
                  {customer.phone}
                </Typography>
              </div>
            </div>
          </div>
        </div>

        {/* Thông tin chi tiết */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            {customer.address && (
              <div>
                <div className="flex items-center space-x-2 mb-1">
                  <Icon name="map-pin" size="sm" className="text-muted" />
                  <Typography variant="body2" className="text-muted">
                    {t('customer.form.address')}
                  </Typography>
                </div>
                <Typography variant="body1" className="text-foreground ml-6">
                  {customer.address}
                </Typography>
              </div>
            )}

            <div>
              <div className="flex items-center space-x-2 mb-1">
                <Icon name="calendar" size="sm" className="text-muted" />
                <Typography variant="body2" className="text-muted">
                  {t('customer.detail.customerSince')}
                </Typography>
              </div>
              <Typography variant="body1" className="text-foreground ml-6">
                {formatDate(customer.customerSince)}
              </Typography>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <Icon name="tag" size="sm" className="text-muted" />
                <Typography variant="body2" className="text-muted">
                  {t('customer.form.tags')}
                </Typography>
              </div>
              <div className="flex flex-wrap gap-1 ml-6">
                {customer.tags && customer.tags.length > 0 ? (
                  customer.tags.map((tag, index) => (
                    <Chip key={index} size="sm">
                      {tag}
                    </Chip>
                  ))
                ) : (
                  <Typography variant="body2" className="text-muted">
                    {t('customer.detail.noData')}
                  </Typography>
                )}
              </div>
            </div>

            {customer.lastOrderDate && (
              <div>
                <div className="flex items-center space-x-2 mb-1">
                  <Icon name="shopping-cart" size="sm" className="text-muted" />
                  <Typography variant="body2" className="text-muted">
                    {t('customer.detail.lastOrder')}
                  </Typography>
                </div>
                <Typography variant="body1" className="text-foreground ml-6">
                  {formatDate(customer.lastOrderDate)}
                </Typography>
              </div>
            )}
          </div>
        </div>
      </div>
    </CollapsibleCard>
  );
};

export default CustomerGeneralInfo;
