import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeleteResult, FindManyOptions, FindOneOptions, FindOptionsWhere, Repository } from 'typeorm';
import { AdminAudienceCustomFieldDefinition } from '../entities/admin-audience-custom-field-definition.entity';

/**
 * Repository cho AdminAudienceCustomFieldDefinition
 */
@Injectable()
export class AdminAudienceCustomFieldDefinitionRepository {
  constructor(
    @InjectRepository(AdminAudienceCustomFieldDefinition)
    private readonly repository: Repository<AdminAudienceCustomFieldDefinition>,
  ) {}

  /**
   * Tìm kiếm nhiều trường tùy chỉnh
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách trường tùy chỉnh
   */
  async find(options?: FindManyOptions<AdminAudienceCustomFieldDefinition>): Promise<AdminAudienceCustomFieldDefinition[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một trường tùy chỉnh
   * @param options Tùy chọn tìm kiếm
   * @returns Trường tùy chỉnh hoặc null
   */
  async findOne(options?: FindOneOptions<AdminAudienceCustomFieldDefinition>): Promise<AdminAudienceCustomFieldDefinition | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Đếm số lượng trường tùy chỉnh
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng trường tùy chỉnh
   */
  async count(options?: FindManyOptions<AdminAudienceCustomFieldDefinition>): Promise<number> {
    return this.repository.count(options);
  }

  /**
   * Xóa trường tùy chỉnh theo điều kiện
   * @param criteria Điều kiện xóa
   * @returns Kết quả xóa
   */
  async delete(criteria: FindOptionsWhere<AdminAudienceCustomFieldDefinition>): Promise<DeleteResult> {
    return this.repository.delete(criteria);
  }

  /**
   * Xóa trường tùy chỉnh
   * @param customField Trường tùy chỉnh cần xóa
   * @returns Trường tùy chỉnh đã xóa
   */
  async remove(customField: AdminAudienceCustomFieldDefinition): Promise<AdminAudienceCustomFieldDefinition>;
  async remove(customField: AdminAudienceCustomFieldDefinition[]): Promise<AdminAudienceCustomFieldDefinition[]>;
  async remove(customField: AdminAudienceCustomFieldDefinition | AdminAudienceCustomFieldDefinition[]): Promise<AdminAudienceCustomFieldDefinition | AdminAudienceCustomFieldDefinition[]> {
    return this.repository.remove(customField as any);
  }

  /**
   * Tạo mới trường tùy chỉnh
   * @param data Dữ liệu trường tùy chỉnh
   * @returns Trường tùy chỉnh đã tạo
   */
  async create(data: Partial<AdminAudienceCustomFieldDefinition>): Promise<AdminAudienceCustomFieldDefinition> {
    const customField = this.repository.create(data);
    return this.repository.save(customField);
  }

  /**
   * Lưu trường tùy chỉnh
   * @param data Dữ liệu trường tùy chỉnh
   * @returns Trường tùy chỉnh đã lưu
   */
  async save(data: Partial<AdminAudienceCustomFieldDefinition>): Promise<AdminAudienceCustomFieldDefinition> {
    return this.repository.save(data);
  }
}
