import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin template ZNS
 */
export class ZnsTemplateResponseDto {
  @ApiProperty({
    description: 'ID của template trong hệ thống',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của Official Account',
    example: '*********',
  })
  oaId: string;

  @ApiProperty({
    description: 'ID của template trên Zalo',
    example: 'template*********',
  })
  templateId: string;

  @ApiProperty({
    description: 'Tên của template',
    example: 'Thông báo đơn hàng',
  })
  templateName: string;

  @ApiProperty({
    description: 'Nội dung của template',
    example: 'Đơn hàng #{orderId} của bạn đã được xác nhận. Cảm ơn bạn đã mua hàng tại {shopName}.',
  })
  templateContent: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> tham số của template',
    example: ['orderId', 'shopName'],
    type: [String],
  })
  params: string[];

  @ApiProperty({
    description: 'Trạng thái của template (approved, pending, rejected)',
    example: 'approved',
  })
  status: string;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: *************,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: *************,
  })
  updatedAt: number;
}
