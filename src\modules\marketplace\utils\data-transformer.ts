import { ProductListItem, ProductDetail, ProductCategory, ProductCategoryDisplay, ProductStatus } from '../types/product.types';
import { ApiProduct, ApiProductDetail } from '../services/marketplace-api.service';
import { fetchDocumentContent, sanitizeHtmlContent } from './content-fetcher';

/**
 * Utility để chuyển đổi dữ liệu từ backend API sang format UI
 */

/**
 * Mapping category enum sang display format
 */
const CATEGORY_DISPLAY_MAP: Record<ProductCategory, ProductCategoryDisplay> = {
  [ProductCategory.AGENT]: {
    id: 'agent',
    name: 'AI Agent',
    slug: 'ai-agents'
  },
  [ProductCategory.KNOWLEDGE_FILE]: {
    id: 'knowledge-file',
    name: 'Knowledge File',
    slug: 'knowledge-files'
  },
  [ProductCategory.FUNCTION]: {
    id: 'function',
    name: 'Function',
    slug: 'functions'
  },
  [ProductCategory.FINETUNE]: {
    id: 'finetune',
    name: 'Fine-tuned Model',
    slug: 'fine-tuned-models'
  },
  [ProductCategory.STRATEGY]: {
    id: 'strategy',
    name: 'Strategy',
    slug: 'strategies'
  }
};

/**
 * Tạo slug từ tên sản phẩm
 */
function createSlug(name: string): string {
  return name
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

/**
 * Lấy thumbnail từ mảng images
 */
function getThumbnail(images: Array<{ key: string; position: number; url: string }>): string {
  if (!images || images.length === 0) {
    return '/images/placeholder-product.jpg'; // Default placeholder
  }

  // Sắp xếp theo position và lấy ảnh đầu tiên
  const sortedImages = images.sort((a, b) => a.position - b.position);
  return sortedImages[0].url;
}

/**
 * Tính discount từ listed price và discounted price
 */
function calculateDiscount(listedPrice: number, discountedPrice: number): number {
  if (listedPrice <= discountedPrice) return 0;
  return listedPrice - discountedPrice;
}

/**
 * Kiểm tra sản phẩm có mới không (tạo trong 30 ngày)
 */
function isNewProduct(createdAt: number): boolean {
  const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
  return createdAt > thirtyDaysAgo;
}

/**
 * Tạo mock rating và review count
 */
function generateMockRatingData(soldCount: number): { rating: number; reviewCount: number } {
  // Tạo rating từ 3.5 đến 5.0 dựa trên soldCount
  const baseRating = 3.5;
  const maxRating = 5.0;
  const ratingRange = maxRating - baseRating;

  // Sử dụng soldCount để tạo rating consistent
  const normalizedSoldCount = Math.min(soldCount / 100, 1); // Normalize to 0-1
  const rating = baseRating + (ratingRange * normalizedSoldCount);

  // Review count thường là 10-30% của sold count
  const reviewCount = Math.floor(soldCount * (0.1 + Math.random() * 0.2));

  return {
    rating: Math.round(rating * 10) / 10, // Round to 1 decimal
    reviewCount: Math.max(reviewCount, 0)
  };
}

/**
 * Tạo mock tags dựa trên category
 */
function generateMockTags(category: ProductCategory): string[] {
  const tagMap: Record<ProductCategory, string[]> = {
    [ProductCategory.AGENT]: ['ai', 'automation', 'assistant'],
    [ProductCategory.KNOWLEDGE_FILE]: ['knowledge', 'data', 'training'],
    [ProductCategory.FUNCTION]: ['function', 'api', 'integration'],
    [ProductCategory.FINETUNE]: ['model', 'training', 'custom'],
    [ProductCategory.STRATEGY]: ['strategy', 'business', 'planning']
  };

  return tagMap[category] || [];
}

/**
 * Chuyển đổi ApiProduct sang ProductListItem
 */
export function transformApiProductToListItem(apiProduct: ApiProduct): ProductListItem {
  const categoryDisplay = CATEGORY_DISPLAY_MAP[apiProduct.category];
  const mockRating = generateMockRatingData(apiProduct.soldCount);
  const discount = calculateDiscount(apiProduct.listedPrice, apiProduct.discountedPrice);

  return {
    // Backend fields
    id: apiProduct.id,
    name: apiProduct.name,
    description: apiProduct.description,
    listedPrice: apiProduct.listedPrice,
    discountedPrice: apiProduct.discountedPrice,
    category: apiProduct.category as ProductCategory,
    status: apiProduct.status as ProductStatus,
    images: apiProduct.images,
    seller: apiProduct.seller,
    createdAt: apiProduct.createdAt,
    soldCount: apiProduct.soldCount,
    canPurchase: apiProduct.canPurchase,
    userManual: apiProduct.userManual,
    detail: apiProduct.detail,
    sourceId: apiProduct.sourceId,

    // Computed UI fields
    slug: createSlug(apiProduct.name),
    thumbnail: getThumbnail(apiProduct.images),
    price: apiProduct.discountedPrice,
    originalPrice: discount > 0 ? apiProduct.listedPrice : undefined,
    discount: discount > 0 ? discount : undefined,
    rating: mockRating.rating,
    reviewCount: mockRating.reviewCount,
    inStock: true, // Digital products are always in stock
    stockQuantity: 999, // Digital products have unlimited stock
    categoryDisplay,
    brand: {
      id: 'redai',
      name: 'RedAI',
      logo: '/images/redai-logo.png'
    },
    isNew: isNewProduct(apiProduct.createdAt),
    isFeatured: apiProduct.soldCount > 50, // Products with high sales are featured
    tags: generateMockTags(apiProduct.category as ProductCategory)
  };
}

/**
 * Chuyển đổi ApiProductDetail sang ProductDetail (sync version - deprecated)
 */
export function transformApiProductToDetail(apiProduct: ApiProductDetail): ProductDetail {
  console.log('🔍 [transformApiProductToDetail] Input API product:', apiProduct);

  const listItem = transformApiProductToListItem(apiProduct);
  console.log('🔍 [transformApiProductToDetail] Transformed list item:', listItem);

  const result = {
    ...listItem,
    images: apiProduct.images?.map(img => img.url) || [], // Extract URLs for gallery
    specifications: {
      'Loại sản phẩm': CATEGORY_DISPLAY_MAP[apiProduct.category]?.name || apiProduct.category,
      'Người bán': apiProduct.seller?.name || 'N/A',
      'Ngày tạo': new Date(apiProduct.createdAt).toLocaleDateString('vi-VN'),
      'Số lượng đã bán': apiProduct.soldCount?.toString() || '0',
      'Trạng thái': apiProduct.status || 'N/A'
    },
    reviews: [], // TODO: Implement reviews when backend supports it
    relatedProducts: [], // TODO: Implement related products
    detailHtml: apiProduct.detail ? `<div>Chi tiết sản phẩm sẽ được tải từ: ${apiProduct.detail}</div>` : undefined,
    usageGuideHtml: apiProduct.userManual ? `<div>Hướng dẫn sử dụng sẽ được tải từ: ${apiProduct.userManual}</div>` : undefined
  };

  console.log('✅ [transformApiProductToDetail] Final result:', result);
  return result;
}

/**
 * Chuyển đổi ApiProductDetail sang ProductDetail với fetch nội dung thực tế (async version)
 */
export async function transformApiProductToDetailAsync(apiProduct: ApiProductDetail): Promise<ProductDetail> {
  console.log('🔍 [transformApiProductToDetailAsync] Input API product:', apiProduct);

  const listItem = transformApiProductToListItem(apiProduct);
  console.log('🔍 [transformApiProductToDetailAsync] Transformed list item:', listItem);

  // Fetch nội dung chi tiết và hướng dẫn sử dụng song song
  const [detailHtml, usageGuideHtml] = await Promise.all([
    apiProduct.detail ? fetchDocumentContent(apiProduct.detail) : Promise.resolve(undefined),
    apiProduct.userManual ? fetchDocumentContent(apiProduct.userManual) : Promise.resolve(undefined)
  ]);

  console.log('✅ [transformApiProductToDetailAsync] Detail content fetched:', detailHtml ? 'Yes' : 'No');
  console.log('✅ [transformApiProductToDetailAsync] Usage guide content fetched:', usageGuideHtml ? 'Yes' : 'No');

  // Extract image URLs from API response
  const imageUrls = apiProduct.images?.map(img => img.url) || [];
  console.log('🔍 [transformApiProductToDetailAsync] API images:', apiProduct.images);
  console.log('🔍 [transformApiProductToDetailAsync] Extracted image URLs:', imageUrls);

  const result = {
    ...listItem,
    images: imageUrls, // Extract URLs for gallery
    specifications: {
      'Loại sản phẩm': CATEGORY_DISPLAY_MAP[apiProduct.category]?.name || apiProduct.category,
      'Người bán': apiProduct.seller?.name || 'N/A',
      'Ngày tạo': new Date(apiProduct.createdAt).toLocaleDateString('vi-VN'),
      'Số lượng đã bán': apiProduct.soldCount?.toString() || '0',
      'Trạng thái': apiProduct.status || 'N/A'
    },
    reviews: [], // TODO: Implement reviews when backend supports it
    relatedProducts: [], // TODO: Implement related products
    detailHtml: detailHtml ? sanitizeHtmlContent(detailHtml) : undefined,
    usageGuideHtml: usageGuideHtml ? sanitizeHtmlContent(usageGuideHtml) : undefined
  };

  console.log('✅ [transformApiProductToDetailAsync] Final result:', result);
  return result;
}

/**
 * Chuyển đổi mảng ApiProduct sang mảng ProductListItem
 */
export function transformApiProductsToListItems(apiProducts: ApiProduct[]): ProductListItem[] {
  return apiProducts.map(transformApiProductToListItem);
}

/**
 * Lấy danh sách categories để hiển thị
 */
export function getDisplayCategories(): ProductCategoryDisplay[] {
  return Object.values(CATEGORY_DISPLAY_MAP);
}

/**
 * Tìm category display từ slug
 */
export function getCategoryBySlug(slug: string): ProductCategoryDisplay | undefined {
  return Object.values(CATEGORY_DISPLAY_MAP).find(cat => cat.slug === slug);
}

/**
 * Tìm category enum từ slug
 */
export function getCategoryEnumBySlug(slug: string): ProductCategory | undefined {
  const categoryDisplay = getCategoryBySlug(slug);
  if (!categoryDisplay) return undefined;

  return Object.entries(CATEGORY_DISPLAY_MAP).find(
    ([, display]) => display.id === categoryDisplay.id
  )?.[0] as ProductCategory;
}

/**
 * Format giá tiền
 */
export function formatPrice(price: number): string {
  return new Intl.NumberFormat('vi-VN').format(price) + ' R-Point';
}

/**
 * Format ngày tháng
 */
export function formatDate(timestamp: number): string {
  return new Date(timestamp).toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}
