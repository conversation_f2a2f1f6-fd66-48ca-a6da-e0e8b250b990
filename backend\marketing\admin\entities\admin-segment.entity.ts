import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_segments trong cơ sở dữ liệu
 * Phân khúc khách hàng của admin
 */
@Entity('admin_segments')
export class AdminSegment {
  /**
   * ID của segment
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Tên segment
   */
  @Column({ name: 'name', length: 255, nullable: true, comment: 'Tên tập khách hàng' })
  name: string;

  /**
   * <PERSON>ô tả segment
   */
  @Column({ name: 'description', type: 'text', nullable: true, comment: '<PERSON>ô tả' })
  description: string;

  /**
   * Tiêu chí lọc khách hàng
   */
  @Column({ name: 'criteria', type: 'jsonb', nullable: true, comment: 'Lưu trữ điều kiện lọc khách hàng khi tạo segment' })
  criteria: any;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true, comment: 'Thời gian tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
