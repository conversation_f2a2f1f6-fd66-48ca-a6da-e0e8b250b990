import { IsEmail, IsNotEmpty, IsOptional, IsPhoneNumber, IsString, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CreateCustomFieldDto } from './create-custom-field.dto';

/**
 * DTO cho việc tạo audience mới
 */
export class CreateAudienceDto {
  /**
   * Email của khách hàng
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Email của khách hàng',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: 'Email không được để trống' })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email: string;

  /**
   * Số điện thoại của khách hàng
   * @example "+84912345678"
   */
  @ApiProperty({
    description: '<PERSON><PERSON> điện thoại của khách hàng',
    example: '+84912345678',
  })
  @IsOptional()
  @IsPhoneNumber(undefined, { message: 'Số điện thoại không hợp lệ' })
  phone?: string;

  /**
   * Danh sách các trường tùy chỉnh
   */
  @ApiProperty({
    description: 'Danh sách các trường tùy chỉnh',
    type: [CreateCustomFieldDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateCustomFieldDto)
  customFields?: CreateCustomFieldDto[];

  /**
   * Danh sách ID của các tag
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID của các tag',
    example: [1, 2, 3],
    required: false,
    type: [Number],
  })
  @IsOptional()
  tagIds?: number[];
}
