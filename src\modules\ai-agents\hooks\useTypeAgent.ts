import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import {
  getTypeAgents,
  getTypeAgentDetail,
  createTypeAgent,
  updateTypeAgent,
  deleteTypeAgent,
} from '../api/agent.api';

import {
  GetTypeAgentsQueryDto,
  CreateTypeAgentDto,
  UpdateTypeAgentDto,
  TypeAgentListResponse,
  TypeAgentDetailDto,
} from '../types';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';



/**
 * Hook để lấy danh sách type agents
 * @param params Query params
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetTypeAgents = (
  params?: GetTypeAgentsQueryDto,
  options?: UseQueryOptions<ApiResponse<TypeAgentListResponse>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_LIST, params],
    queryFn: () => getTypeAgents(params),
    staleTime: 10 * 60 * 1000, // 10 minutes (type agents ít thay đổi)
    ...options,
  });
};

/**
 * Hook để lấy chi tiết type agent
 * @param id ID của type agent
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetTypeAgentDetail = (
  id: number | undefined,
  options?: UseQueryOptions<ApiResponse<TypeAgentDetailDto>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_DETAIL, id],
    queryFn: () => getTypeAgentDetail(id as number),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

/**
 * Hook để tạo type agent mới
 * @returns Mutation result
 */
export const useCreateTypeAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.CREATE_TYPE_AGENT],
    mutationFn: (data: CreateTypeAgentDto) => createTypeAgent(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách type agents
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_LIST] });
    },
  });
};

/**
 * Hook để cập nhật type agent
 * @returns Mutation result
 */
export const useUpdateTypeAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.UPDATE_TYPE_AGENT],
    mutationFn: ({ id, data }: { id: number; data: UpdateTypeAgentDto }) =>
      updateTypeAgent(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate chi tiết type agent và danh sách
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_DETAIL, id] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_LIST] });
    },
  });
};

/**
 * Hook để xóa type agent
 * @returns Mutation result
 */
export const useDeleteTypeAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.DELETE_TYPE_AGENT],
    mutationFn: (id: number) => deleteTypeAgent(id),
    onSuccess: (_, id) => {
      // Remove type agent khỏi cache và invalidate danh sách
      queryClient.removeQueries({ queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_DETAIL, id] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_LIST] });
    },
  });
};
