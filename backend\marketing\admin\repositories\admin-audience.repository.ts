import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { AdminAudience } from '../entities/admin-audience.entity';

/**
 * Repository cho AdminAudience
 */
@Injectable()
export class AdminAudienceRepository {
  constructor(
    @InjectRepository(AdminAudience)
    private readonly repository: Repository<AdminAudience>,
  ) {}

  /**
   * Tìm kiếm nhiều audience
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách audience
   */
  async find(options?: FindManyOptions<AdminAudience>): Promise<AdminAudience[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một audience
   * @param options Tùy chọn tìm kiếm
   * @returns Audience hoặc null
   */
  async findOne(options?: FindOneOptions<AdminAudience>): Promise<AdminAudience | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Đếm số lượng audience
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng audience
   */
  async count(options?: FindManyOptions<AdminAudience>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }

  /**
   * Lưu audience
   * @param audience Audience cần lưu
   * @returns Audience đã lưu
   */
  async save(audience: AdminAudience): Promise<AdminAudience>;
  async save(audience: AdminAudience[]): Promise<AdminAudience[]>;
  async save(audience: AdminAudience | AdminAudience[]): Promise<AdminAudience | AdminAudience[]> {
    return this.repository.save(audience as any);
  }

  /**
   * Xóa audience
   * @param audience Audience cần xóa
   * @returns Audience đã xóa
   */
  async remove(audience: AdminAudience): Promise<AdminAudience>;
  async remove(audience: AdminAudience[]): Promise<AdminAudience[]>;
  async remove(audience: AdminAudience | AdminAudience[]): Promise<AdminAudience | AdminAudience[]> {
    return this.repository.remove(audience as any);
  }
}
