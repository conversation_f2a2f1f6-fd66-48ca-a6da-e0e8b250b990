import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsPhoneNumber, IsString } from 'class-validator';
import { CreateCustomFieldDto } from './create-custom-field.dto';
import { Type } from 'class-transformer';

/**
 * DTO cho tạo audience
 */
export class CreateAudienceDto {
  /**
   * Email của khách hàng
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Email của khách hàng',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: 'Email không được để trống' })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email: string;

  /**
   * Số điện thoại của khách hàng
   * @example "+84912345678"
   */
  @ApiProperty({
    description: 'S<PERSON> điện thoại của khách hàng',
    example: '+84912345678',
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber(undefined, { message: '<PERSON><PERSON> điện thoại không hợp lệ' })
  phone?: string;

  /**
   * Các trường tùy chỉnh
   */
  @ApiProperty({
    description: 'Các trường tùy chỉnh',
    type: [CreateCustomFieldDto],
    required: false,
  })
  @IsOptional()
  @Type(() => CreateCustomFieldDto)
  customFields?: CreateCustomFieldDto[];

  /**
   * Các tag ID
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Các tag ID',
    example: [1, 2, 3],
    required: false,
    type: [Number],
  })
  @IsOptional()
  tagIds?: number[];
}
