import React, { useState, useEffect, useMemo } from 'react';
import { Select, CollapsibleCard, Slider, Card, Checkbox, Textarea, Icon } from '@/shared/components/common';
import {
  useGetBaseModelsByProvider,
  convertBaseModelsToSelectOptions,
  getProviderIcon,
  normalizeModelConfig
} from '../../hooks/useBaseModel';
import { TypeProviderEnum } from '../../types';

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
}

const ProviderCard: React.FC<ProviderCardProps> = ({ provider, name, isSelected, onClick }) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-2 border-primary shadow-md'
          : 'hover:border-gray-300 hover:shadow-sm'
      }`}
      onClick={() => onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium">{name}</div>
      </div>
    </Card>
  );
};

interface ModelConfigData {
  provider: TypeProviderEnum;
  modelId: string;
  vectorStore: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  topK: number;
  instruction?: string;
}

interface ModelConfigProps {
  initialData?: ModelConfigData;
  onSave?: (data: ModelConfigData) => void;
}

/**
 * Component cấu hình model AI cho Agent
 */
const ModelConfig: React.FC<ModelConfigProps> = ({
  initialData,
  onSave
}) => {
  const [configData, setConfigData] = useState<ModelConfigData>(initialData || {
    provider: TypeProviderEnum.OPENAI,
    modelId: 'gpt-4',
    vectorStore: 'pinecone',
    maxTokens: 1229,
    temperature: 1,
    topP: 1,
    topK: 100,
    instruction: ''
  });

  // State để kiểm soát hiển thị cấu hình nâng cao
  const [showAdvancedConfig, setShowAdvancedConfig] = useState(false);

  const [isExpanded, setIsExpanded] = useState(true);

  // Lấy danh sách models theo provider đã chọn
  const {
    data: baseModelsResponse,
    isLoading: isLoadingModels,
    error: modelsError
  } = useGetBaseModelsByProvider(configData.provider);

  // Convert API response thành options cho Select với useMemo để tránh re-render
  const modelOptions = useMemo(() => {
    return baseModelsResponse?.result?.items
      ? convertBaseModelsToSelectOptions(baseModelsResponse.result.items)
      : [];
  }, [baseModelsResponse?.result?.items]);

  // Fallback options nếu API chưa load hoặc lỗi
  const fallbackOptions = useMemo(() => [
    {
      value: 'loading',
      label: isLoadingModels ? 'Đang tải...' : modelsError ? 'Lỗi tải model' : 'Không có model'
    }
  ], [isLoadingModels, modelsError]);

  // Lấy config của model hiện tại được chọn
  const selectedModelConfig = useMemo(() => {
    if (!baseModelsResponse?.result?.items || !configData.modelId) {
      return null;
    }

    const selectedModel = baseModelsResponse.result.items.find(
      model => model.model_id === configData.modelId
    );

    const rawConfig = selectedModel?.config || null;
    const normalizedConfig = normalizeModelConfig(rawConfig);

    // Debug logging
    console.log('Selected Model Config:', {
      modelId: configData.modelId,
      selectedModel,
      rawConfig,
      normalizedConfig,
      conditionalChecks: {
        'file_search === true': normalizedConfig?.file_search === true,
        'temperature === true': normalizedConfig?.temperature === true,
        'top_p === true': normalizedConfig?.top_p === true,
        'top_k === true': normalizedConfig?.top_k === true,
      },
      rawValues: {
        file_search: normalizedConfig?.file_search,
        temperature: normalizedConfig?.temperature,
        top_p: normalizedConfig?.top_p,
        top_k: normalizedConfig?.top_k
      }
    });

    return normalizedConfig;
  }, [baseModelsResponse?.result?.items, configData.modelId]);

  // Auto-update model khi có data từ API và model hiện tại không có trong danh sách
  useEffect(() => {
    if (modelOptions.length > 0) {
      const currentModelExists = modelOptions.some(option => option.value === configData.modelId);
      if (!currentModelExists) {
        // Chọn model đầu tiên trong danh sách
        const firstModel = modelOptions[0];
        const newConfigData = {
          ...configData,
          modelId: firstModel.value
        };

        setConfigData(newConfigData);

        if (onSave) {
          onSave(newConfigData);
        }
      }
    }
  }, [modelOptions, configData, onSave]);

  // Xử lý khi thay đổi select
  const handleSelectChange = (name: string, value: string | number | string[] | number[]) => {
    setConfigData(prev => ({
      ...prev,
      [name]: value
    }));

    if (onSave) {
      onSave({
        ...configData,
        [name]: value
      });
    }
  };

  // Xử lý khi thay đổi slider
  const handleSliderChange = (name: string, value: number) => {
    setConfigData(prev => ({
      ...prev,
      [name]: value
    }));

    if (onSave) {
      onSave({
        ...configData,
        [name]: value
      });
    }
  };

  // Xử lý khi thay đổi textarea
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setConfigData(prev => ({
      ...prev,
      [name]: value
    }));

    if (onSave) {
      onSave({
        ...configData,
        [name]: value
      });
    }
  };

  // Xử lý khi thay đổi checkbox
  const handleCheckboxChange = (checked: boolean) => {
    setShowAdvancedConfig(checked);
  };

  // Xử lý khi chọn provider
  const handleProviderChange = (provider: TypeProviderEnum) => {
    // Danh sách model mặc định cho mỗi provider
    const defaultModels: Record<TypeProviderEnum, string> = {
      [TypeProviderEnum.OPENAI]: 'gpt-4',
      [TypeProviderEnum.GOOGLE]: 'gemini-pro',
      [TypeProviderEnum.ANTHROPIC]: 'claude-3-opus',
      [TypeProviderEnum.META]: 'llama-3',
      [TypeProviderEnum.XAI]: 'xai-1',
      [TypeProviderEnum.DEEPSEEK]: 'deepseek-chat'
    };

    setConfigData(prev => ({
      ...prev,
      provider,
      modelId: defaultModels[provider] || prev.modelId
    }));

    if (onSave) {
      onSave({
        ...configData,
        provider,
        modelId: defaultModels[provider] || configData.modelId
      });
    }
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center">
          <span className="mr-2">
            <i className="fas fa-cog"></i>
          </span>
          <span>Cấu hình Model</span>
        </div>
      }
      defaultOpen={isExpanded}
      onToggle={setIsExpanded}
      className="mb-6"
    >
      <div className="p-4 space-y-6">
        {/* Provider selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Provider
          </label>
          <div className="grid grid-cols-6 md:grid-cols-3 lg:grid-cols-6 gap-3">
            <ProviderCard
              provider={TypeProviderEnum.OPENAI}
              name="OpenAI"
              isSelected={configData.provider === TypeProviderEnum.OPENAI}
              onClick={handleProviderChange}
            />
            <ProviderCard
              provider={TypeProviderEnum.GOOGLE}
              name="Google"
              isSelected={configData.provider === TypeProviderEnum.GOOGLE}
              onClick={handleProviderChange}
            />
            <ProviderCard
              provider={TypeProviderEnum.ANTHROPIC}
              name="Anthropic"
              isSelected={configData.provider === TypeProviderEnum.ANTHROPIC}
              onClick={handleProviderChange}
            />
            <ProviderCard
              provider={TypeProviderEnum.META}
              name="Meta"
              isSelected={configData.provider === TypeProviderEnum.META}
              onClick={handleProviderChange}
            />
            <ProviderCard
              provider={TypeProviderEnum.XAI}
              name="XAI"
              isSelected={configData.provider === TypeProviderEnum.XAI}
              onClick={handleProviderChange}
            />
            <ProviderCard
              provider={TypeProviderEnum.DEEPSEEK}
              name="Deepseek"
              isSelected={configData.provider === TypeProviderEnum.DEEPSEEK}
              onClick={handleProviderChange}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-1 gap-4 mb-6">
          <div>
            <label htmlFor="modelId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Model
            </label>
            <Select
              options={modelOptions.length > 0 ? modelOptions : fallbackOptions}
              value={configData.modelId}
              onChange={(value) => handleSelectChange('modelId', value)}
              placeholder="Chọn model"
              disabled={isLoadingModels}
            />
          </div>

          {/* Vector Store - chỉ hiển thị khi model support file_search */}
          {selectedModelConfig?.file_search === true && (
            <div>
              <label htmlFor="vectorStore" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Vector Store
              </label>
              <Select
                options={[
                  { value: 'pinecone', label: 'Pinecone' },
                  { value: 'qdrant', label: 'Qdrant' },
                  { value: 'weaviate', label: 'Weaviate' },
                  { value: 'chroma', label: 'Chroma' }
                ]}
                value={configData.vectorStore}
                onChange={(value) => handleSelectChange('vectorStore', value)}
                placeholder="Chọn vector store"
              />
            </div>
          )}
        </div>

        {/* Instruction textarea */}
        <div className="mb-6">
          <label htmlFor="instruction" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Instruction
          </label>
          <Textarea
            id="instruction"
            name="instruction"
            value={configData.instruction || ''}
            onChange={handleTextareaChange}
            placeholder="Nhập hướng dẫn cho model..."
            className="w-full"
            rows={4}
          />
        </div>

        {/* Advanced configuration checkbox */}
        <div className="mb-4">
          <Checkbox
            label="Tùy chỉnh nâng cao"
            checked={showAdvancedConfig}
            onChange={handleCheckboxChange}
          />
        </div>

        {/* Advanced configuration sliders */}
        {showAdvancedConfig && (
          <div className="space-y-6 border-t pt-4 mt-4">
            {/* Max Tokens - luôn hiển thị */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Max Tokens
              </label>
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                Số lượng token tối đa cho mỗi lần gọi API
              </div>
              <Slider
                value={configData.maxTokens}
                min={100}
                max={4096}
                step={1}
                onValueChange={(value: number) => handleSliderChange('maxTokens', value)}
                valueSuffix=""
              />
            </div>

            {/* Temperature - chỉ hiển thị khi model support */}
            {selectedModelConfig?.temperature === true && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Temperature
                </label>
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  Mức độ ngẫu nhiên trong kết quả (0-2)
                </div>
                <Slider
                  value={configData.temperature}
                  min={0}
                  max={2}
                  step={0.01}
                  onValueChange={(value: number) => handleSliderChange('temperature', value)}
                />
              </div>
            )}

            {/* Top P - chỉ hiển thị khi model support */}
            {selectedModelConfig?.top_p === true && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Top P
                </label>
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  Xác suất tích lũy cho lựa chọn token (0-1)
                </div>
                <Slider
                  value={configData.topP}
                  min={0}
                  max={1}
                  step={0.01}
                  onValueChange={(value: number) => handleSliderChange('topP', value)}
                />
              </div>
            )}

            {/* Top K - chỉ hiển thị khi model support */}
            {selectedModelConfig?.top_k === true && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Top K
                </label>
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  Số lượng token có xác suất cao nhất để xem xét
                </div>
                <Slider
                  value={configData.topK}
                  min={1}
                  max={50}
                  step={1}
                  onValueChange={(value: number) => handleSliderChange('topK', value)}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </CollapsibleCard>
  );
};

export default ModelConfig;
