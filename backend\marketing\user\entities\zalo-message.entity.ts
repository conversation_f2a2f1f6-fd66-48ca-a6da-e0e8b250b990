import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng zalo_messages trong cơ sở dữ liệu
 * Lưu trữ lịch sử tin nhắn giữa người dùng Zalo và Official Account
 */
@Entity('zalo_messages')
export class ZaloMessage {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của Official Account
   */
  @Column({ name: 'oa_id', length: 50 })
  oaId: string;

  /**
   * ID của người dùng Zalo
   */
  @Column({ name: 'user_id', length: 50 })
  userId: string;

  /**
   * ID của tin nhắn trên Zalo
   */
  @Column({ name: 'message_id', length: 50, nullable: true })
  messageId: string;

  /**
   * <PERSON><PERSON><PERSON> tin nhắn (text, image, file, template)
   */
  @Column({ name: 'message_type', length: 20 })
  messageType: string;

  /**
   * Nội dung tin nhắn
   */
  @Column({ name: 'content', type: 'text', nullable: true })
  content: string | null;

  /**
   * Dữ liệu bổ sung của tin nhắn (JSON)
   */
  @Column({ name: 'data', type: 'jsonb', nullable: true })
  data: any;

  /**
   * Hướng tin nhắn (incoming, outgoing)
   */
  @Column({ name: 'direction', length: 10 })
  direction: string;

  /**
   * ID của agent xử lý tin nhắn
   */
  @Column({ name: 'agent_id', nullable: true })
  agentId: number;

  /**
   * Thời điểm gửi/nhận (Unix timestamp)
   */
  @Column({ name: 'timestamp', type: 'bigint' })
  timestamp: number;

  /**
   * Thời điểm tạo bản ghi (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;
}
