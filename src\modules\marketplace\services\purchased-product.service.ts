import { PurchasedProduct, PurchasedProductFilterParams, PurchasedProductListResponse, PurchaseStatus, ProductCategory } from '../types/purchased-product.types';

/**
 * Service cho Purchased Products
 */
export class PurchasedProductService {
  /**
   * <PERSON><PERSON><PERSON> danh sách sản phẩm đã mua
   */
  static async getPurchasedProducts(params?: PurchasedProductFilterParams): Promise<PurchasedProductListResponse> {
    // Mock data
    const mockData: PurchasedProduct[] = [
      {
        id: '1',
        name: '<PERSON><PERSON><PERSON> lư<PERSON><PERSON> định cao AIDA',
        description: 'Chiến lư<PERSON>c marketing AIDA hiệu quả',
        image: '/images/products/product-1.jpg',
        price: 1000000,
        category: ProductCategory.AGENT,
        status: PurchaseStatus.COMPLETED,
        purchaseDate: '2023-07-15',
        seller: {
          id: '1',
          name: 'Seller 1',
          avatar: '/images/avatars/avatar-1.png'
        }
      },
      {
        id: '2',
        name: '<PERSON><PERSON><PERSON> thức 4P Marketing',
        description: '<PERSON><PERSON><PERSON> thức 4P cho chiến lược marketing',
        image: '/images/products/product-2.jpg',
        price: 1500000,
        category: ProductCategory.KNOWLEDGE_FILE,
        status: PurchaseStatus.COMPLETED,
        purchaseDate: '2023-07-16',
        seller: {
          id: '2',
          name: 'Seller 2',
          avatar: '/images/avatars/avatar-2.png'
        }
      },
      {
        id: '3',
        name: 'Mô hình 7A trong Marketing',
        description: 'Mô hình 7A hiệu quả cho marketing',
        image: '/images/products/product-3.jpg',
        price: 2000000,
        category: ProductCategory.FUNCTION,
        status: PurchaseStatus.PROCESSING,
        purchaseDate: '2023-07-17',
        seller: {
          id: '3',
          name: 'Seller 3',
          avatar: '/images/avatars/avatar-3.png'
        }
      },
      {
        id: '4',
        name: 'Chiến lược Content Marketing',
        description: 'Chiến lược content marketing hiệu quả',
        image: '/images/products/product-4.jpg',
        price: 1200000,
        category: ProductCategory.FINETUNE,
        status: PurchaseStatus.PROCESSING,
        purchaseDate: '2023-07-18',
        seller: {
          id: '1',
          name: 'Seller 1',
          avatar: '/images/avatars/avatar-1.png'
        }
      },
      {
        id: '5',
        name: 'Kỹ thuật SEO nâng cao',
        description: 'Kỹ thuật SEO nâng cao cho website',
        image: '/images/products/product-5.jpg',
        price: 1800000,
        category: ProductCategory.STRATEGY,
        status: PurchaseStatus.CANCELLED,
        purchaseDate: '2023-07-19',
        seller: {
          id: '2',
          name: 'Seller 2',
          avatar: '/images/avatars/avatar-2.png'
        }
      },
    ];

    // Filter data based on params
    let filteredData = [...mockData];
    if (params) {
      if (params.search) {
        const searchLower = params.search.toLowerCase();
        filteredData = filteredData.filter(
          (item) =>
            item.name.toLowerCase().includes(searchLower) ||
            item.description.toLowerCase().includes(searchLower) ||
            item.seller.name.toLowerCase().includes(searchLower)
        );
      }

      if (params.status && params.status !== 'all') {
        filteredData = filteredData.filter((item) => item.status === params.status);
      }

      if (params.category && params.category !== 'all') {
        filteredData = filteredData.filter((item) => item.category === params.category);
      }
    }

    // Pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    return {
      code: 200,
      message: 'Success',
      result: {
        items: paginatedData,
        meta: {
          totalItems: filteredData.length,
          itemCount: paginatedData.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(filteredData.length / limit),
          currentPage: page,
        },
      },
    };
  }

  /**
   * Lấy chi tiết sản phẩm đã mua
   */
  static async getPurchasedProduct(id: string): Promise<PurchasedProduct> {
    // Mock data
    const mockData: PurchasedProduct[] = [
      {
        id: '1',
        name: 'Chiến lược định cao AIDA',
        description: 'Chiến lược marketing AIDA hiệu quả',
        image: '/images/products/product-1.jpg',
        price: 1000000,
        category: ProductCategory.AGENT,
        status: PurchaseStatus.COMPLETED,
        purchaseDate: '2023-07-15',
        seller: {
          id: '1',
          name: 'Seller 1',
          avatar: '/images/avatars/avatar-1.png'
        }
      },
      // ... other mock data
    ];

    // Find product by id
    const product = mockData.find((item) => item.id === id);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    if (!product) {
      throw new Error('Product not found');
    }

    return product;
  }
}
