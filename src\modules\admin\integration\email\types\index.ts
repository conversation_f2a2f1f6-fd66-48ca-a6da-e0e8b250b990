/**
 * Email Server Configuration Types
 */

export interface EmailServerConfiguration {
  id: number;
  serverName: string;
  host: string;
  port: number;
  username: string;
  password: string;
  useSsl: boolean;
  useStartTls: boolean;
  additionalSettings?: Record<string, any>;
  userId?: number;
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
  createdBy: number;
  updatedBy?: number;
  user?: {
    id: number;
    email: string;
    fullName: string;
  };
}

export interface CreateEmailServerDto {
  serverName: string;
  host: string;
  port: number;
  username: string;
  password: string;
  useSsl: boolean;
  useStartTls: boolean;
  additionalSettings?: Record<string, any>;
  userId?: number;
  isActive: boolean;
}

export interface UpdateEmailServerDto {
  serverName?: string;
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  useSsl?: boolean;
  useStartTls?: boolean;
  additionalSettings?: Record<string, any>;
  userId?: number;
  isActive?: boolean;
}

export interface TestEmailServerDto {
  recipientEmail?: string;
  subject?: string;
}

export interface EmailServerQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  userId?: number;
}

export interface EmailServerTestResult {
  success: boolean;
  message: string;
  details?: any;
}

export interface EmailServerFormData extends Omit<CreateEmailServerDto, 'additionalSettings'> {
  additionalSettings?: string; // JSON string for form handling
}
