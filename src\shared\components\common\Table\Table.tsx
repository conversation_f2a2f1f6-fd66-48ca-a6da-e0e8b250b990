import React, { useState, useMemo, useCallback } from 'react';
import { TableProps, TableColumn, TablePaginationProps } from './types';
import TableHeader from './TableHeader';
import TableBody from './TableBody';
import TableSelection from './TableSelection';
import TableExpandableRow, { ExpandButton } from './TableExpandableRow';
import TableLoading from './TableLoading';
import { Pagination } from '@/shared/components/common';
import { useTableSort, useTableSelection } from './hooks';
import { get } from 'lodash';

/**
 * Component bảng
 */
function Table<T>({
  data = [],
  columns = [],
  loading = false,
  loadingType = 'overlay',
  loadingText,
  sortable = false,
  onSortChange,
  selectable = false,
  expandable = false,
  pagination = false,
  rowSelection,
  expandableConfig,
  size = 'md',
  bordered = false,
  striped = false,
  hoverable = true,
  onRow,
  onHeaderRow,
  className = '',
  style,
  rowKey = 'id',
  defaultSort,
}: TableProps<T>) {
  // X<PERSON> lý sắp xếp
  const { sortedData, sortColumn, sortOrder, handleSort } = useTableSort({
    data,
    onSortChange, // Truyền callback ra ngoài
  });

  // Dữ liệu đã sắp xếp sẽ được sử dụng trực tiếp
  const filteredData = defaultSort ? sortedData : data;

  // Xử lý chọn hàng
  const selection = useTableSelection({
    data: filteredData,
    rowKey,
    defaultSelectedRowKeys: rowSelection?.selectedRowKeys,
    onChange: rowSelection?.onChange,
    getCheckboxProps: rowSelection?.getCheckboxProps,
  });

  // State lưu trữ các khóa đã mở rộng
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>(
    expandableConfig?.expandedRowKeys || []
  );

  // State lưu trữ trang hiện tại và số mục trên mỗi trang
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(
    pagination && typeof pagination !== 'boolean' ? pagination.pageSize : 10
  );

  // Xử lý sự kiện thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize?: number) => {
      console.log(`Table handlePageChange: page=${page}, newPageSize=${newPageSize}`);

      // Cập nhật state nội bộ
      setCurrentPage(page);
      if (newPageSize && newPageSize !== pageSize) {
        console.log(`Table changing pageSize from ${pageSize} to ${newPageSize}`);
        setPageSize(newPageSize);
      }

      // Nếu có callback từ props, gọi nó
      if (pagination && typeof pagination !== 'boolean' && pagination.onChange) {
        pagination.onChange(page, newPageSize || pageSize);
      }
    },
    [pagination, pageSize]
  );

  // Xử lý sự kiện mở rộng hàng
  const handleExpandRow = useCallback(
    (key: React.Key) => {
      const newExpandedRowKeys = expandedRowKeys.includes(key)
        ? expandedRowKeys.filter(k => k !== key)
        : [...expandedRowKeys, key];

      setExpandedRowKeys(newExpandedRowKeys);

      // Nếu có callback từ props, gọi nó
      if (expandableConfig?.onExpandedRowsChange) {
        expandableConfig.onExpandedRowsChange(newExpandedRowKeys);
      }
    },
    [expandedRowKeys, expandableConfig]
  );

  // Lấy khóa duy nhất cho mỗi hàng
  const getRowKeyValue = useCallback(
    (record: T, index: number): React.Key => {
      if (typeof rowKey === 'function') {
        return rowKey(record);
      }
      const value = get(record, rowKey, index);
      return value !== undefined ? value.toString() : index.toString();
    },
    [rowKey]
  );

  // Xử lý phân trang
  const paginatedData = useMemo(() => {
    if (!pagination) {
      return filteredData;
    }

    // Nếu pagination là object và có total, sử dụng dữ liệu đã phân trang từ API
    if (typeof pagination !== 'boolean' && pagination.total !== undefined) {
      return filteredData;
    }

    // Nếu không, thực hiện phân trang ở client
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    console.log(
      `Table paginatedData: currentPage=${currentPage}, pageSize=${pageSize}, startIndex=${startIndex}, endIndex=${endIndex}, filteredData.length=${filteredData.length}`
    );

    // Đảm bảo currentPage hợp lệ
    if (startIndex >= filteredData.length && filteredData.length > 0) {
      // Nếu startIndex vượt quá độ dài của dữ liệu, đặt lại currentPage
      const newPage = Math.ceil(filteredData.length / pageSize) || 1;
      console.log(`Adjusting currentPage from ${currentPage} to ${newPage}`);
      setTimeout(() => setCurrentPage(newPage), 0);
      return filteredData.slice(0, pageSize);
    }

    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, pagination, currentPage, pageSize]);

  // Tạo cột chọn hàng
  const selectionColumn = useMemo(() => {
    if (!selectable && !rowSelection) {
      return null as unknown as TableColumn<T>;
    }

    return {
      key: 'selection',
      title: (
        <TableSelection
          checked={selection.isAllSelected}
          indeterminate={selection.selectedRowKeys.length > 0 && !selection.isAllSelected}
          onChange={checked => {
            if (checked) {
              selection.selectAll();
            } else {
              selection.deselectAll();
            }
          }}
          disabled={filteredData.length === 0} // Vô hiệu hóa checkbox khi không có dữ liệu
        />
      ),
      width: 50,
      render: (_: unknown, record: T) => {
        const key = getRowKeyValue(record, 0);
        const disabled = selection.isDisabled(record);

        return (
          <TableSelection
            checked={selection.isSelected(key)}
            disabled={disabled}
            onChange={checked => {
              if (checked) {
                selection.selectRow(key);
              } else {
                selection.deselectRow(key);
              }
            }}
          />
        );
      },
    };
  }, [selectable, rowSelection, selection, getRowKeyValue, filteredData.length]);

  // Tạo cột mở rộng
  const expandColumn = useMemo(() => {
    if (!expandable && !expandableConfig) {
      return null as unknown as TableColumn<T>;
    }

    return {
      key: 'expand',
      title: '',
      width: 50,
      render: (_: unknown, record: T, index: number) => {
        const key = getRowKeyValue(record, index);
        const canExpand = expandableConfig?.rowExpandable
          ? expandableConfig.rowExpandable(record)
          : true;

        if (!canExpand) {
          return null;
        }

        return (
          <ExpandButton
            expanded={expandedRowKeys.includes(key)}
            onChange={() => handleExpandRow(key)}
          />
        );
      },
    };
  }, [expandable, expandableConfig, expandedRowKeys, getRowKeyValue, handleExpandRow]);

  // Kết hợp tất cả các cột
  const mergedColumns = useMemo(() => {
    const cols = [];

    // Thêm cột mở rộng
    if (expandColumn) {
      cols.push(expandColumn);
    }

    // Thêm cột chọn hàng
    if (selectionColumn) {
      cols.push(selectionColumn);
    }

    // Thêm các cột từ props
    cols.push(...columns);

    return cols;
  }, [columns, expandColumn, selectionColumn]);

  // Xác định các lớp kích thước
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  }[size];

  // Kết hợp tất cả các lớp
  const tableClasses = [
    'w-full',
    'border-collapse',
    bordered ? 'border border-gray-200 dark:border-gray-700' : '',
    sizeClasses,
    className,
  ]
    .filter(Boolean)
    .join(' ');

  // Tạo cấu hình phân trang
  const paginationConfig: TablePaginationProps | false = useMemo(() => {
    if (!pagination) {
      return false;
    }

    if (typeof pagination === 'boolean') {
      return {
        total: filteredData.length,
        current: currentPage,
        pageSize,
        onChange: handlePageChange,
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
      };
    }

    // Nếu pagination có total, sử dụng giá trị đó, nếu không sử dụng độ dài của dữ liệu lọc
    const total = typeof pagination.total !== 'undefined' ? pagination.total : filteredData.length;

    return {
      ...pagination,
      total,
      current: pagination.current || currentPage,
      pageSize: pagination.pageSize || pageSize,
      onChange: handlePageChange,
    };
  }, [pagination, filteredData.length, currentPage, pageSize, handlePageChange]);

  return (
    <div className="relative w-full max-w-full">
      {/* Phần bảng với thanh cuộn ngang */}
      <div
        className="overflow-x-auto w-full max-w-full"
        style={{ WebkitOverflowScrolling: 'touch' }}
      >
        <table className={tableClasses} style={style}>
          <TableHeader
            columns={mergedColumns}
            sortable={sortable}
            sortColumn={sortColumn}
            sortOrder={sortOrder}
            onSort={handleSort}
            onHeaderRow={onHeaderRow}
          />

          {loading && loadingType === 'skeleton' ? (
            <TableLoading type="skeleton" skeletonRows={5} skeletonColumns={mergedColumns.length} />
          ) : loading && loadingType === 'inline' ? (
            <tbody>
              <tr>
                <td colSpan={mergedColumns.length}>
                  <TableLoading
                    type="inline"
                    skeletonColumns={mergedColumns.length}
                    loadingText={loadingText}
                  />
                </td>
              </tr>
            </tbody>
          ) : (
            <TableBody
              data={paginatedData}
              columns={mergedColumns}
              rowKey={rowKey}
              onRow={onRow}
              striped={striped}
              hoverable={hoverable}
            />
          )}

          {/* Hiển thị hàng mở rộng */}
          {expandable && expandableConfig?.expandedRowRender && !loading && (
            <tbody>
              {paginatedData.map((record, index) => {
                const key = getRowKeyValue(record, index);
                const canExpand = expandableConfig.rowExpandable
                  ? expandableConfig.rowExpandable(record)
                  : true;

                if (!canExpand || !expandedRowKeys.includes(key)) {
                  return null;
                }

                return (
                  <TableExpandableRow
                    key={`${key}-expanded`}
                    record={record}
                    index={index}
                    colSpan={mergedColumns.length}
                    expanded={expandedRowKeys.includes(key)}
                    expandedRowRender={(record, index) =>
                      expandableConfig.expandedRowRender?.(record, index) || <div>No content</div>
                    }
                  />
                );
              })}
            </tbody>
          )}
        </table>

        {/* Hiển thị loading overlay */}
        {loading && loadingType === 'overlay' && (
          <TableLoading type="overlay" loadingText={loadingText} />
        )}
      </div>

      {/* Hiển thị phân trang - không có thanh cuộn ngang */}
      {paginationConfig && (
        <div className="mt-4 flex justify-end w-full">
          <Pagination
            variant="compact"
            currentPage={paginationConfig.current}
            totalPages={Math.ceil(paginationConfig.total / paginationConfig.pageSize)}
            itemsPerPage={paginationConfig.pageSize}
            onPageChange={page => paginationConfig.onChange(page, paginationConfig.pageSize)}
            onItemsPerPageChange={newPageSize => {
              console.log(
                `Table onItemsPerPageChange: newPageSize=${newPageSize}, current pageSize=${paginationConfig.pageSize}`
              );
              if (newPageSize !== paginationConfig.pageSize) {
                // Cập nhật state trực tiếp
                setPageSize(newPageSize);
                setCurrentPage(1);
                // Gọi callback nếu có
                paginationConfig.onChange(1, newPageSize);
              }
            }}
            itemsPerPageOptions={paginationConfig.pageSizeOptions || [10, 20, 50, 100]}
            showFirstLastButtons={paginationConfig.showFirstLastButtons}
            showPageInfo={paginationConfig.showPageInfo}
          />
        </div>
      )}
    </div>
  );
}

export default Table;
