import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Trang tổng quan quản lý Tools của người dùng
 */
const ToolManagementPage: React.FC = () => {
  const { t } = useTranslation(['tools']);

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Tools Card */}
        <ModuleCard
          title={t('tools:tools', 'Công cụ')}
          description={t(
            'tools:toolsDescription',
            'Quản lý các công cụ của bạn, bao gồm xem, sao chép và tùy chỉnh công cụ.'
          )}
          icon="tool"
          linkTo="/tools/list"
        />

        {/* Tool Groups Card */}
        <ModuleCard
          title={t('tools:toolGroups', 'Nhóm công cụ')}
          description={t(
            'tools:toolGroupsDescription',
            'Quản lý các nhóm công cụ, phân loại và tổ chức công cụ theo nhóm chức năng.'
          )}
          icon="folder"
          linkTo="/tools/groups"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default ToolManagementPage;
