import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  WebsiteDto,
  WebsiteQueryDto,
  CreateWebsiteDto,
  UpdateWebsiteDto,
  ConnectAgentToWebsiteDto,
  DisconnectAgentFromWebsiteDto,
  VerifyWebsiteDto,
  WebsiteVerificationResponseDto,
  WebsiteStatsDto,
  WebsiteAnalyticsDto,
} from '../types/website.types';

/**
 * Website Integration API
 * Các API functions cho Website integration
 */

/**
 * Lấy danh sách Websites với phân trang và lọc
 * @param params Query parameters
 * @returns Promise với response từ API
 */
export const getWebsites = async (
  params?: WebsiteQueryDto
): Promise<ApiResponseDto<PaginatedResult<WebsiteDto>>> => {
  return apiClient.get('/integration/website', { params });
};

/**
 * Tạo mới Website
 * @param data Data để tạo website
 * @returns Promise với response từ API
 */
export const createWebsite = async (
  data: CreateWebsiteDto
): Promise<ApiResponseDto<WebsiteDto>> => {
  return apiClient.post('/integration/website', data);
};

/**
 * Lấy thông tin chi tiết một Website
 * @param websiteId ID của Website
 * @returns Promise với response từ API
 */
export const getWebsiteDetail = async (
  websiteId: string
): Promise<ApiResponseDto<WebsiteDto>> => {
  return apiClient.get(`/integration/website/${websiteId}`);
};

/**
 * Cập nhật thông tin Website
 * @param websiteId ID của Website
 * @param data Data để cập nhật
 * @returns Promise với response từ API
 */
export const updateWebsite = async (
  websiteId: string,
  data: UpdateWebsiteDto
): Promise<ApiResponseDto<WebsiteDto>> => {
  return apiClient.patch(`/integration/website/${websiteId}`, data);
};

/**
 * Xóa Website
 * @param websiteId ID của Website cần xóa
 * @returns Promise với response từ API
 */
export const deleteWebsite = async (
  websiteId: string
): Promise<ApiResponseDto<null>> => {
  return apiClient.delete(`/integration/website/${websiteId}`);
};

/**
 * Xóa nhiều Websites cùng lúc
 * @param websiteIds Danh sách website IDs cần xóa
 * @returns Promise với response từ API
 */
export const deleteManyWebsites = async (
  websiteIds: string[]
): Promise<ApiResponseDto<{ deletedCount: number; errorWebsites?: string[] }>> => {
  return apiClient.delete('/integration/website/bulk', { 
    data: { websiteIds } 
  });
};

/**
 * Kết nối Agent với Website
 * @param data Data để kết nối agent
 * @returns Promise với response từ API
 */
export const connectAgentToWebsite = async (
  data: ConnectAgentToWebsiteDto
): Promise<ApiResponseDto<WebsiteDto>> => {
  return apiClient.post('/integration/website/connect-agent', data);
};

/**
 * Ngắt kết nối Agent khỏi Website
 * @param data Data để ngắt kết nối agent
 * @returns Promise với response từ API
 */
export const disconnectAgentFromWebsite = async (
  data: DisconnectAgentFromWebsiteDto
): Promise<ApiResponseDto<WebsiteDto>> => {
  return apiClient.post('/integration/website/disconnect-agent', data);
};

/**
 * Xác minh Website
 * @param data Data để xác minh website
 * @returns Promise với response từ API
 */
export const verifyWebsite = async (
  data: VerifyWebsiteDto
): Promise<ApiResponseDto<WebsiteVerificationResponseDto>> => {
  return apiClient.post('/integration/website/verify', data);
};

/**
 * Lấy mã xác minh cho Website
 * @param websiteId ID của Website
 * @returns Promise với response từ API
 */
export const getWebsiteVerificationCode = async (
  websiteId: string
): Promise<ApiResponseDto<{ verificationCode: string }>> => {
  return apiClient.get(`/integration/website/${websiteId}/verification-code`);
};

/**
 * Kiểm tra trạng thái xác minh Website
 * @param websiteId ID của Website
 * @returns Promise với response từ API
 */
export const checkWebsiteVerification = async (
  websiteId: string
): Promise<ApiResponseDto<{ verified: boolean; lastChecked: Date }>> => {
  return apiClient.get(`/integration/website/${websiteId}/verification-status`);
};

/**
 * Lấy thống kê Websites
 * @returns Promise với response từ API
 */
export const getWebsiteStats = async (): Promise<ApiResponseDto<WebsiteStatsDto>> => {
  return apiClient.get('/integration/website/stats');
};

/**
 * Lấy analytics cho Website
 * @param websiteId ID của Website
 * @param startDate Ngày bắt đầu
 * @param endDate Ngày kết thúc
 * @returns Promise với response từ API
 */
export const getWebsiteAnalytics = async (
  websiteId: string,
  startDate: Date,
  endDate: Date
): Promise<ApiResponseDto<WebsiteAnalyticsDto>> => {
  return apiClient.get(`/integration/website/${websiteId}/analytics`, {
    params: {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    },
  });
};
