import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, ConfirmDeleteModal } from '@/shared/components/common';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { NotificationUtil } from '@/shared/utils/notification';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import ActiveFilters from '@/modules/components/filters/ActiveFilters';
import { useActiveFilters } from '@/shared/hooks/filters/useActiveFilters';

// Import components từ module admin data
import { MediaTable } from '@/modules/admin/data/components/tables';
import { MediaFilter } from '@/modules/admin/data/components/filters';
import { MediaDetailView } from '@/modules/admin/data/components/forms';

// Import hooks từ module media
import {
  useAdminMediaList,
  useDeleteMedia,
  useDeleteMultipleMedia,
} from '@/modules/admin/data/media/hooks/useMedia';

// Import types từ module media
import {
  AdminMediaDto,
  MediaQueryDto,
  MediaStatus,
} from '@/modules/admin/data/media/types/media.types';

/**
 * Trang quản lý media trong admin
 */
const MediaPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const [mediaList, setMediaList] = useState<AdminMediaDto[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [mediaToDelete, setMediaToDelete] = useState<AdminMediaDto | null>(null);
  const [mediaToView, setMediaToView] = useState<AdminMediaDto | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedMediaIds, setSelectedMediaIds] = useState<string[]>([]);
  const [showBatchDeleteConfirm, setShowBatchDeleteConfirm] = useState(false);

  // State cho tìm kiếm
  const [searchTerm, setSearchTerm] = useState('');

  // State cho filter
  const [statusFilter, setStatusFilter] = useState<MediaStatus | 'all'>('all');

  // State cho sắp xếp
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isViewFormVisible,
    showForm: showViewSlideForm,
    hideForm: hideViewForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo<MediaQueryDto>(() => {
    const params: MediaQueryDto = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection === SortDirection.ASC ? 'ASC' : 'DESC',
    };

    if (statusFilter !== 'all') {
      params.status = statusFilter;
    }

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection, statusFilter]);

  // Hooks để gọi API
  const {
    data: mediaData,
    isLoading: isLoadingMedia,
    error: mediaError,
  } = useAdminMediaList(queryParams);

  const { mutateAsync: deleteMedia } = useDeleteMedia();
  const { mutateAsync: deleteMultipleMedia } = useDeleteMultipleMedia();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (mediaData) {
      setMediaList(mediaData.items);
      setTotalItems(mediaData.meta.totalItems);
    }

    setIsLoading(isLoadingMedia);
  }, [mediaData, mediaError, isLoadingMedia]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý thay đổi filter
  const handleFilterChange = useCallback((status: MediaStatus | 'all') => {
    setStatusFilter(status);
    setCurrentPage(1); // Reset về trang 1 khi thay đổi filter
  }, []);

  // Xử lý reset filter
  const handleResetFilter = useCallback(() => {
    setSearchTerm('');
    setStatusFilter('all');
    setSortBy('createdAt');
    setSortDirection(SortDirection.DESC);
    setCurrentPage(1);
  }, []);

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch,
      setSelectedFilterId: (id) => handleFilterChange(id as MediaStatus | 'all'),
      setDateRange: () => {}, // Không sử dụng date range
      handleSortChange,
      selectedFilterValue: statusFilter,
      filterValueLabelMap: {
        [MediaStatus.ACTIVE]: t('admin:data.media.status.ACTIVE', 'Hoạt động'),
        [MediaStatus.PENDING]: t('admin:data.media.status.PENDING', 'Đang xử lý'),
        [MediaStatus.INACTIVE]: t('admin:data.media.status.INACTIVE', 'Không hoạt động'),
      },
      t,
    });

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((media: AdminMediaDto) => {
    setMediaToDelete(media);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setMediaToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!mediaToDelete) return;

    try {
      await deleteMedia(mediaToDelete.id);
      setShowDeleteConfirm(false);
      setMediaToDelete(null);
      
      NotificationUtil.success({
        message: t('admin:data.media.messages.deleteSuccess', 'Xóa media thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting media:', error);
      
      NotificationUtil.error({
        message: t('admin:data.media.messages.deleteError', 'Có lỗi xảy ra khi xóa media'),
        duration: 3000,
      });
    }
  }, [mediaToDelete, deleteMedia, t]);

  // Xử lý hiển thị popup xác nhận xóa hàng loạt
  const handleShowBatchDeleteConfirm = useCallback(() => {
    if (selectedMediaIds.length === 0) return;
    setShowBatchDeleteConfirm(true);
  }, [selectedMediaIds]);

  // Xử lý hủy xóa hàng loạt
  const handleCancelBatchDelete = useCallback(() => {
    setShowBatchDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa hàng loạt
  const handleConfirmBatchDelete = useCallback(async () => {
    if (selectedMediaIds.length === 0) return;

    try {
      await deleteMultipleMedia(selectedMediaIds);
      setShowBatchDeleteConfirm(false);
      setSelectedMediaIds([]);
      
      NotificationUtil.success({
        message: t('admin:data.media.messages.batchDeleteSuccess', 'Xóa {{count}} media thành công', {
          count: selectedMediaIds.length,
        }),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error batch deleting media:', error);
      
      NotificationUtil.error({
        message: t('admin:data.media.messages.batchDeleteError', 'Có lỗi xảy ra khi xóa hàng loạt'),
        duration: 3000,
      });
    }
  }, [selectedMediaIds, deleteMultipleMedia, t]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý hiển thị form xem chi tiết media
  const handleShowViewForm = useCallback(
    (media: AdminMediaDto) => {
      setMediaToView(media);
      showViewSlideForm();
    },
    [showViewSlideForm]
  );

  // Lấy danh sách các cột hiển thị
  const visibleColumnIds = useMemo(() => {
    if (visibleColumns.length === 0) {
      return [];
    }
    return visibleColumns.filter(col => col.visible).map(col => col.id);
  }, [visibleColumns]);

  return (
    <div>
      <div className="space-y-4">
        {/* Filter */}
        <MediaFilter
          onSearch={handleSearch}
          onFilterChange={handleFilterChange}
          onReset={handleResetFilter}
          initialSearchTerm={searchTerm}
          initialStatus={statusFilter}
        />

        <div>
          <MenuIconBar
            onSearch={handleSearch}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
            items={[]}
            additionalIcons={[
              {
                icon: 'trash',
                tooltip: t('common:bulkDelete', 'Xóa nhiều'),
                variant: 'primary',
                onClick: () => {
                  if (selectedMediaIds.length > 0) {
                    handleShowBatchDeleteConfirm();
                  } else {
                    NotificationUtil.info({
                      message: t('admin:data.media.selectFilesToDelete', 'Vui lòng chọn ít nhất một file để xóa'),
                      duration: 3000,
                    });
                  }
                },
                className: 'text-red-500',
                condition: selectedMediaIds.length > 0,
              }
            ]}
          />
        </div>

        {/* Hiển thị ActiveFilters */}
        <ActiveFilters
          searchTerm={searchTerm}
          onClearSearch={handleClearSearch}
          filterValue={statusFilter}
          filterLabel={getFilterLabel()}
          onClearFilter={handleClearFilter}
          sortBy={sortBy}
          sortDirection={sortDirection}
          onClearSort={handleClearSort}
          onClearAll={handleClearAll}
        />

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {mediaToView && <MediaDetailView media={mediaToView} onClose={hideViewForm} />}
        </SlideInForm>

        <Card className="overflow-hidden">
          <MediaTable
            data={mediaList}
            loading={isLoading}
            totalItems={totalItems}
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
            sortBy={sortBy}
            sortDirection={sortDirection}
            selectedRowKeys={selectedMediaIds}
            visibleColumns={visibleColumnIds}
            onPageChange={handlePageChange}
            onSortChange={handleSortChange}
            onRowSelectionChange={(selectedRowKeys) => setSelectedMediaIds(selectedRowKeys as string[])}
            onView={handleShowViewForm}
            onDelete={handleShowDeleteConfirm}
          />
        </Card>

        {/* Modal xác nhận xóa */}
        <ConfirmDeleteModal
          isOpen={showDeleteConfirm}
          onClose={handleCancelDelete}
          onConfirm={handleConfirmDelete}
          title={t('admin:data.common.confirmDelete', 'Xác nhận xóa')}
          message={t('admin:data.media.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa media này?')}
        />

        {/* Modal xác nhận xóa hàng loạt */}
        <ConfirmDeleteModal
          isOpen={showBatchDeleteConfirm}
          onClose={handleCancelBatchDelete}
          onConfirm={handleConfirmBatchDelete}
          title={t('admin:data.common.confirmBatchDelete', 'Xác nhận xóa hàng loạt')}
          message={t(
            'admin:data.media.confirmBatchDeleteMessage',
            'Bạn có chắc chắn muốn xóa {{count}} media đã chọn?',
            { count: selectedMediaIds.length }
          )}
        />
      </div>
    </div>
  );
};

export default MediaPage;
