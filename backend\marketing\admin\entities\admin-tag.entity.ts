import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_tags trong cơ sở dữ liệu
 * Bảng nhãn của admin
 */
@Entity('admin_tags')
export class AdminTag {
  /**
   * ID của tag
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Tên tag
   */
  @Column({ name: 'name', length: 255, nullable: true })
  name: string;

  /**
   * <PERSON><PERSON>u sắc của tag
   */
  @Column({ name: 'color', length: 7, nullable: true })
  color: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;

  /**
   * ID của nhân viên tạo
   */
  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  /**
   * ID của nhân viên cập nhật
   */
  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  // Không sử dụng quan hệ với các bảng khác, chỉ lưu ID
}
