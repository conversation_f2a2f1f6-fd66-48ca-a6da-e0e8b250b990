import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Form,
  FormItem,
  Input,
  Icon,
  CollapsibleCard,
  Typography,
  Loading,
} from '@/shared/components/common';
import { SearchableSelect } from '@/shared/components/common/Select';
import { FormStatus } from '../types/profile.types';
import { useBankInfo, useBanks, useUpdateBankInfo } from '../hooks/useUser';
import { useProfileNotification } from '../contexts/ProfileNotificationContext';
import { BankQueryDto } from '../types/user.types';
import { createBankInfoSchema, BankInfoSchema } from '../schemas';

/**
 * Component form thông tin ngân hàng
 */
const BankInfoForm: React.FC = () => {
  const { t } = useTranslation(['profile', 'validation']);
  const [formStatus, setFormStatus] = useState<FormStatus>(FormStatus.IDLE);
  const { showNotification } = useProfileNotification();

  // Tạo schema với hàm t để hỗ trợ đa ngôn ngữ
  const bankInfoSchema = createBankInfoSchema(t);

  // Sử dụng hooks để lấy và cập nhật dữ liệu
  const { data: bankInfo, isLoading: isLoadingBankInfo, error: bankInfoError } = useBankInfo();
  const updateBankInfoMutation = useUpdateBankInfo();

  // Lấy danh sách ngân hàng
  const bankQuery: BankQueryDto = {
    page: 1,
    limit: 100,
    isActive: true,
  };
  const {
    data: banksData,
    isLoading: isLoadingBanks,
    refetch: refetchBanks,
  } = useBanks(bankQuery, {
    // Đảm bảo dữ liệu được tải ngay từ đầu
    enabled: true,
    // Sử dụng giá trị mặc định từ hook (1 ngày cho staleTime, 7 ngày cho gcTime)
  });
  // Tải dữ liệu ngân hàng khi component được mount
  useEffect(() => {
    // Tải dữ liệu ngân hàng ngay khi component được mount
    console.log('Component mounted, fetching banks data');
    refetchBanks();
  }, [refetchBanks]);

  // Xử lý khi submit form
  const onSubmit = (data: BankInfoSchema) => {
    // Sử dụng dữ liệu từ form
    const submittingData: BankInfoSchema = data;

    // Kiểm tra dữ liệu trước khi gửi
    if (!submittingData.bankCode) {
      showNotification('error', t('profile:validation.bankCodeRequired'));
      return;
    }

    if (!submittingData.accountNumber) {
      showNotification('error', t('profile:validation.accountNumberRequired'));
      return;
    }

    if (!submittingData.accountHolder) {
      showNotification('error', t('profile:validation.accountHolderRequired'));
      return;
    }

    // Cập nhật trạng thái form
    setFormStatus(FormStatus.SUBMITTING);

    // Gọi mutation để cập nhật thông tin ngân hàng
    updateBankInfoMutation.mutate(submittingData, {
      onSuccess: () => {
        // Cập nhật trạng thái form
        setFormStatus(FormStatus.IDLE);



        // Hiển thị thông báo thành công
        showNotification('success', t('profile:messages.updateSuccess'));
      },
      onError: error => {
        // Cập nhật trạng thái form
        setFormStatus(FormStatus.IDLE);
        // Hiển thị thông báo lỗi
        let errorMessage = t('profile:messages.updateError');

        // Kiểm tra xem error có phải là AxiosError không
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as { response?: { data?: { message?: string } } };
          if (axiosError.response?.data?.message) {
            errorMessage = axiosError.response.data.message;
          }
        }
        showNotification('error', errorMessage);
      },
    });
  };

  // Xử lý khi hủy thay đổi (reset về giá trị ban đầu)
  const handleCancel = (e: React.MouseEvent<HTMLButtonElement>) => {
    // Ngăn chặn sự kiện mặc định (submit form)
    e.preventDefault();
    e.stopPropagation();

    // Reset form về giá trị ban đầu từ bankInfo data
    // Sẽ được implement sau khi có form ref
  };

  const cardTitle = (
    <div className="flex items-center">
      <Icon name="document" className="mr-2 text-primary" />
      <Typography variant="subtitle1" weight="semibold" color="dark">
        {t('profile:bankInfo.title')}
      </Typography>
    </div>
  );

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoadingBankInfo) {
    return (
      <CollapsibleCard title={cardTitle} className="mb-6">
        <div className="flex justify-center items-center py-8">
          <Loading size="lg" />
        </div>
      </CollapsibleCard>
    );
  }

  // Hiển thị thông báo lỗi nếu có
  if (bankInfoError) {
    return (
      <CollapsibleCard title={cardTitle} className="mb-6">
        <div className="text-red-500 py-4">{t('profile:error.loadingBankInfo')}</div>
      </CollapsibleCard>
    );
  }

  // Tạo danh sách ngân hàng từ API
  const bankOptions = Array.isArray(banksData)
    ? banksData.map((bank: { bankCode: string; bankName: string; logoPath: string }) => {
        // Lưu logo vào localStorage để đảm bảo nó luôn có sẵn
        if (bank.logoPath && bank.bankCode) {
          try {
            localStorage.setItem(`select-option-image-${bank.bankCode}`, bank.logoPath);
            console.log(`Saved bank logo to localStorage: ${bank.bankCode} -> ${bank.logoPath}`);
          } catch (e) {
            console.error('Failed to save bank logo to localStorage:', e);
          }
        }

        return {
          value: bank.bankCode,
          label: bank.bankName,
          data: { image: bank.logoPath },
        };
      })
    : [];

  return (
    <CollapsibleCard title={cardTitle} className="mb-6">
      <Form
        schema={bankInfoSchema}
        defaultValues={{
          bankCode: bankInfo?.bankCode || '',
          accountNumber: bankInfo?.accountNumber || '',
          accountHolder: bankInfo?.accountHolder || '',
          bankBranch: bankInfo?.bankBranch || '',
        }}
        onSubmit={data => {
          // Kiểm tra xem form có đang ở trạng thái submitting không
          if (formStatus === FormStatus.SUBMITTING) {
            return;
          }

          onSubmit(data as BankInfoSchema);
        }}

      >
        <div className="space-y-6">
          {/* Tên ngân hàng */}
          <div>
            <FormItem name="bankCode" label={t('profile:bankInfo.bankName')}>
              <SearchableSelect
                options={bankOptions}
                placeholder={t('profile:banksData.bankName')}
                fullWidth
                loading={isLoadingBanks}
                inlineSearch={true}
                renderValue={(value, options) => {
                  // Tìm option đã chọn
                  const selectedOption = options.find(opt => opt.value === value);
                  if (!selectedOption) return value.toString();

                  // Lấy URL hình ảnh
                  let imageUrl = selectedOption.imageUrl;

                  // Nếu không có imageUrl trực tiếp, kiểm tra trong data
                  if (!imageUrl && selectedOption.data && typeof selectedOption.data === 'object') {
                    if ('imageUrl' in selectedOption.data) {
                      imageUrl = String(selectedOption.data.imageUrl);
                    } else if ('image' in selectedOption.data) {
                      imageUrl = String(selectedOption.data.image);
                    }
                  }

                  // Lấy URL hình ảnh từ localStorage nếu không có trong data
                  const cachedImageUrl = !imageUrl
                    ? localStorage.getItem(`select-option-image-${value}`)
                    : undefined;

                  // Sử dụng URL hình ảnh từ data hoặc từ localStorage
                  const finalImageUrl = imageUrl || cachedImageUrl;

                  return (
                    <div className="flex items-center">
                      {finalImageUrl ? (
                        <div className="w-6 h-6 mr-2.5 flex-shrink-0 border border-border/30 rounded-sm overflow-hidden bg-white flex items-center justify-center">
                          <img
                            src={finalImageUrl}
                            alt={selectedOption.label}
                            className="w-5 h-5 object-contain"
                            loading="eager"
                            onError={e => {
                              // Fallback if image fails to load
                              (e.target as HTMLImageElement).style.display = 'none';
                              // Hiển thị fallback khi lỗi
                              const parent = (e.target as HTMLImageElement).parentElement;
                              if (parent) {
                                parent.innerHTML = `<span class="text-xs font-bold text-primary">${selectedOption.label.charAt(0)}</span>`;
                                parent.classList.add('bg-primary/10');
                              }
                            }}
                          />
                        </div>
                      ) : (
                        // Fallback icon if no image
                        <div className="w-6 h-6 mr-2.5 flex-shrink-0 bg-primary/10 rounded-sm flex items-center justify-center">
                          <span className="text-xs font-bold text-primary">
                            {selectedOption.label.charAt(0)}
                          </span>
                        </div>
                      )}
                      <span className="truncate font-medium">{selectedOption.label}</span>
                    </div>
                  );
                }}
              />
            </FormItem>
          </div>

          {/* Số tài khoản */}
          <div>
            <FormItem name="accountNumber" label={t('profile:bankInfo.accountNumber')}>
              <Input
                placeholder={t('profile:bankInfo.accountNumber')}
                fullWidth
              />
            </FormItem>
          </div>

          {/* Chi nhánh */}
          <div>
            <FormItem name="bankBranch" label={t('profile:bankInfo.branch')}>
              <Input placeholder={t('profile:bankInfo.branch')} fullWidth />
            </FormItem>
          </div>

          {/* Chủ tài khoản */}
          <div>
            <FormItem name="accountHolder" label={t('profile:bankInfo.accountHolder')}>
              <Input
                placeholder={t('profile:bankInfo.accountHolder')}
                fullWidth
              />
            </FormItem>
          </div>

          {/* Buttons - Luôn hiển thị để test */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={formStatus === FormStatus.SUBMITTING}
            >
              {t('profile:buttons.cancel')}
            </Button>
            <Button
              variant="primary"
              type="submit"
              isLoading={formStatus === FormStatus.SUBMITTING}
            >
              {t('profile:buttons.save')}
            </Button>
          </div>
        </div>
      </Form>
    </CollapsibleCard>
  );
};

export default BankInfoForm;
