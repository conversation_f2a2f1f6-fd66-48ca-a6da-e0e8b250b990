import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { AdminSegmentService } from '../services/admin-segment.service';
import { CreateSegmentDto, UpdateSegmentDto, SegmentResponseDto, SegmentQueryDto } from '../dto/segment';
import { PaginatedResponseDto } from '../dto/common';
import { ApiResponseDto as AppApiResponse } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';
/**
 * Controller cho AdminSegment
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_SEGMENT)
@Controller('admin/marketing/segments')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth()
export class AdminSegmentController {
  constructor(private readonly adminSegmentService: AdminSegmentService) {}

  /**
   * Tạo segment mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo segment mới' })
  @ApiResponse({ status: 201, description: 'Segment đã tạo', type: SegmentResponseDto })
  async create(
    @Body() createSegmentDto: CreateSegmentDto,
  ): Promise<AppApiResponse<SegmentResponseDto>> {
    const result = await this.adminSegmentService.create(createSegmentDto);
    return wrapResponse(result, 'Segment đã được tạo thành công');
  }

  /**
   * Lấy danh sách segment với phân trang và filter
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách segment với phân trang và filter' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách segment với phân trang',
    type: PaginatedResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/PaginatedResponseDto' },
        {
          properties: {
            data: {
              type: 'array',
              items: { $ref: '#/components/schemas/SegmentResponseDto' }
            }
          }
        }
      ]
    }
  })
  async findAll(
    @Query() query: SegmentQueryDto
  ): Promise<AppApiResponse<PaginatedResponseDto<SegmentResponseDto>>> {
    const result = await this.adminSegmentService.findAll(query);
    return wrapResponse(result, 'Danh sách segment');
  }

  /**
   * Lấy segment theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy segment theo ID' })
  @ApiResponse({ status: 200, description: 'Segment', type: SegmentResponseDto })
  @ApiResponse({ status: 404, description: 'Segment không tồn tại' })
  async findOne(
    @Param('id') id: string,
  ): Promise<AppApiResponse<SegmentResponseDto>> {
    const result = await this.adminSegmentService.findOne(+id);
    return wrapResponse(result, 'Thông tin segment');
  }

  /**
   * Cập nhật segment
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật segment' })
  @ApiResponse({ status: 200, description: 'Segment đã cập nhật', type: SegmentResponseDto })
  @ApiResponse({ status: 404, description: 'Segment không tồn tại' })
  async update(
    @Param('id') id: string,
    @Body() updateSegmentDto: UpdateSegmentDto,
  ): Promise<AppApiResponse<SegmentResponseDto>> {
    const result = await this.adminSegmentService.update(+id, updateSegmentDto);
    return wrapResponse(result, 'Segment đã được cập nhật thành công');
  }

  /**
   * Xóa segment
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa segment' })
  @ApiResponse({ status: 200, description: 'true nếu xóa thành công', type: Boolean })
  @ApiResponse({ status: 404, description: 'Segment không tồn tại' })
  async remove(
    @Param('id') id: string,
  ): Promise<AppApiResponse<boolean>> {
    const result = await this.adminSegmentService.remove(+id);
    return wrapResponse(result, 'Segment đã được xóa thành công');
  }
}
