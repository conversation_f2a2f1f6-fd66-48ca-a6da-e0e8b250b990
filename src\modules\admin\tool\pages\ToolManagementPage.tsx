import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Trang tổng quan quản lý Tools
 */
const ToolManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin']);

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Tools Card */}
        <ModuleCard
          title={t('admin:tool.tools', 'Tools')}
          description={t(
            'admin:tool.toolsDescription',
            'Quản lý các tools trong hệ thống, bao gồ<PERSON> thêm, s<PERSON><PERSON>, x<PERSON>a và phê duyệt tools.'
          )}
          icon="tool"
          linkTo="/admin/tools/list"
        />

        {/* Tool Groups Card */}
        <ModuleCard
          title={t('admin:tool.toolGroups', 'Nhóm Tools')}
          description={t(
            'admin:tool.toolGroupsDescription',
            'Quản lý các nhóm tools, phân loại và tổ chức tools theo nhóm chức năng.'
          )}
          icon="folder"
          linkTo="/admin/tools/groups"
        />

        {/* Tool Versions Card */}
        <ModuleCard
          title={t('admin:tool.toolVersions', 'Phiên bản Tools')}
          description={t(
            'admin:tool.toolVersionsDescription',
            'Quản lý các phiên bản của tools, theo dõi lịch sử thay đổi và cập nhật.'
          )}
          icon="git-branch"
          linkTo="/admin/tools/versions"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default ToolManagementPage;
