import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString, MinLength } from 'class-validator';

/**
 * DTO cho việc đăng nhập nhân viên
 */
export class EmployeeLoginDto {
  /**
   * Email nhân viên
   */
  @ApiProperty({
    description: 'Email nhân viên',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: 'Email không được để trống' })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email: string;

  /**
   * Mật khẩu nhân viên
   */
  @ApiProperty({
    description: 'Mật khẩu nhân viên',
    example: 'password123',
  })
  @IsNotEmpty({ message: 'Mật khẩu không được để trống' })
  @IsString({ message: 'M<PERSON>t khẩu phải là chuỗi' })
  @MinLength(6, { message: 'Mật khẩu phải có ít nhất 6 ký tự' })
  password: string;

  @ApiProperty({
    description: 'Token reCAPTCHA',
    example: '03AGdBq24PBCbwiDRVu...',
  })
  @IsString({ message: 'Token reCAPTCHA phải là chuỗi' })
  @IsOptional({ message: 'Token reCAPTCHA không được để trống' })
  recaptchaToken: string;
}
