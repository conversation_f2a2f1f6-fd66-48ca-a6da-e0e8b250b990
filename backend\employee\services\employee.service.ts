import { Injectable, Logger } from '@nestjs/common';
import { EmployeeRepository, EmployeeHasRoleRepository } from '@modules/employee/repositories';
import { CreateEmployeeDto, EmployeeQueryDto } from '@modules/employee/dto';
import { Employee, EmployeeRole } from '@modules/employee/entities';
import { EmployeeAvatarUploadDto, UpdateEmployeeAvatarDto } from '@modules/employee/dto';
import { S3Service } from '@/shared/services/s3.service';
import { CategoryFolderEnum, TimeIntervalEnum } from '@/shared/utils';
import { generateS3Key } from '@/shared/utils/generators';
import { ChangeEmployeePasswordDto } from '@modules/employee/dto';
import { AssignEmployeeRoleDto } from '@modules/employee/dto';
import { Transactional } from 'typeorm-transactional';
import { EmployeePasswordService } from './employee-password.service';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions';
import { EMPLOYEE_ERROR_CODES } from '../exceptions/employee-error.code';

/**
 * Service xử lý logic liên quan đến nhân viên
 */
@Injectable()
export class EmployeeService {
  private readonly logger = new Logger(EmployeeService.name);
  private readonly AVATAR_FOLDER = 'employee-avatars';

  constructor(
    private readonly employeeRepository: EmployeeRepository,
    private readonly employeeHasRoleRepository: EmployeeHasRoleRepository,
    private readonly s3Service: S3Service,
    private readonly passwordService: EmployeePasswordService,
  ) {}

  /**
   * Tạo nhân viên mới
   * @param createEmployeeDto Thông tin nhân viên mới
   * @returns Nhân viên đã được tạo và thông tin URL tạm thời để upload avatar (nếu có)
   */
  @Transactional()
  async createEmployee(createEmployeeDto: CreateEmployeeDto): Promise<{
    id: number;
    fullName: string;
    email: string;
    phoneNumber: string;
    address: string;
    createdAt: number;
    updatedAt: number;
    enable: boolean;
    avatar?: string;
    avatarUploadUrl?: string;
    avatarKey?: string;
    avatarUrlExpiresAt?: number;
  }> {
    try {
      // Kiểm tra email đã tồn tại chưa
      const existingEmployee = await this.employeeRepository.findByEmail(createEmployeeDto.email);
      if (existingEmployee) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMAIL_ALREADY_EXISTS,
          `Email ${createEmployeeDto.email} đã được sử dụng`
        );
      }

      // Kiểm tra số điện thoại đã tồn tại chưa
      const existingEmployeeByPhone = await this.employeeRepository.findByPhoneNumber(createEmployeeDto.phoneNumber);
      if (existingEmployeeByPhone) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.PHONE_NUMBER_ALREADY_EXISTS,
          `Số điện thoại ${createEmployeeDto.phoneNumber} đã được sử dụng`
        );
      }

      // Kiểm tra độ mạnh của mật khẩu
      try {
        this.passwordService.validatePasswordStrength(createEmployeeDto.password);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.PASSWORD_TOO_WEAK,
          error.message || 'Mật khẩu không đủ mạnh'
        );
      }

      // Mã hóa mật khẩu
      let hashedPassword: string;
      try {
        hashedPassword = await this.passwordService.hashPassword(createEmployeeDto.password);
      } catch (error) {
        this.logger.error(`Error hashing password: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_CREATION_FAILED,
          'Không thể mã hóa mật khẩu'
        );
      }

      // Tạo nhân viên mới
      let employee: Employee;
      try {
        employee = await this.employeeRepository.createEmployee(createEmployeeDto, hashedPassword);
      } catch (error) {
        this.logger.error(`Error creating employee in repository: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_CREATION_FAILED,
          'Tạo nhân viên thất bại'
        );
      }

      // Nếu có roleIds, gán vai trò cho nhân viên
      if (createEmployeeDto.roleIds && createEmployeeDto.roleIds.length > 0) {
        try {
          await this.employeeHasRoleRepository.assignRolesToEmployee(employee.id, createEmployeeDto.roleIds);
        } catch (error) {
          this.logger.error(`Error assigning roles to employee: ${error.message}`, error.stack);
          throw new AppException(
            EMPLOYEE_ERROR_CODES.ROLE_ASSIGNMENT_FAILED,
            'Gán vai trò cho nhân viên thất bại'
          );
        }
      }

      // Kết quả trả về
      const result = {
        id: employee.id,
        fullName: employee.fullName,
        email: employee.email,
        phoneNumber: employee.phoneNumber,
        address: employee.address,
        createdAt: employee.createdAt,
        updatedAt: employee.updatedAt,
        enable: employee.enable,
      };

      // Nếu có yêu cầu tạo URL tạm thời để upload avatar
      if (createEmployeeDto.avatarImageType && createEmployeeDto.avatarMaxSize) {
        // Tạo key cho avatar trên S3
        const avatarKey = generateS3Key({
          baseFolder: employee.id.toString(),
          categoryFolder: CategoryFolderEnum.EMPLOYEE_AVATAR_FOLDER,
        });

        // Tạo URL tạm thời để tải lên avatar
        const uploadUrl = await this.s3Service.createPresignedWithID(
          avatarKey,
          TimeIntervalEnum.FIVE_MINUTES,
          createEmployeeDto.avatarImageType,
          createEmployeeDto.avatarMaxSize,
        );

        // Tính toán thời điểm hết hạn thực tế (timestamp)
        const expiresAt = Date.now() + TimeIntervalEnum.FIVE_MINUTES * 1000;

        // Thêm thông tin URL tạm thời vào kết quả
        return {
          ...result,
          avatarUploadUrl: uploadUrl,
          avatarKey,
          avatarUrlExpiresAt: expiresAt,
        };
      }

      return result;
    } catch (error) {
      this.logger.error(`Error creating employee: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.EMPLOYEE_CREATION_FAILED,
        'Tạo nhân viên thất bại'
      );
    }
  }

  /**
   * Tạo URL tạm thời để tải lên avatar nhân viên
   * @param employeeId ID của nhân viên
   * @param avatarUploadDto Thông tin về loại và kích thước avatar
   * @returns URL tạm thời và thông tin khóa S3
   */
  async createAvatarUploadUrl(employeeId: number, avatarUploadDto: EmployeeAvatarUploadDto): Promise<{
    uploadUrl: string;
    avatarKey: string;
    expiresIn: number;
    expiresAt: number;
  }> {
    try {
      // Kiểm tra xem nhân viên có tồn tại không
      try {
        await this.employeeRepository.findById(employeeId);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      // Kiểm tra loại hình ảnh
      if (!avatarUploadDto.imageType) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.INVALID_IMAGE_TYPE,
          'Loại hình ảnh không được để trống'
        );
      }

      // Tạo key cho avatar trên S3
      const avatarKey = generateS3Key({
        baseFolder: employeeId.toString(),
        categoryFolder: CategoryFolderEnum.EMPLOYEE_AVATAR_FOLDER,
      });

      try {
        // Tạo URL tạm thời để tải lên avatar
        const uploadUrl = await this.s3Service.createPresignedWithID(
          avatarKey,
          TimeIntervalEnum.FIVE_MINUTES,
          avatarUploadDto.imageType,
          avatarUploadDto.maxSize,
        );

        // Tính toán thời điểm hết hạn thực tế (timestamp)
        const expiresAt = Date.now() + TimeIntervalEnum.FIVE_MINUTES * 1000;

        return {
          uploadUrl,
          avatarKey,
          expiresIn: TimeIntervalEnum.FIVE_MINUTES,
          expiresAt,
        };
      } catch (error) {
        this.logger.error(`Error creating presigned URL: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.AVATAR_URL_CREATION_FAILED,
          'Tạo URL tải lên avatar thất bại'
        );
      }
    } catch (error) {
      this.logger.error(`Error creating avatar upload URL: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.AVATAR_URL_CREATION_FAILED,
        'Tạo URL tải lên avatar thất bại'
      );
    }
  }

  /**
   * Cập nhật avatar cho nhân viên
   * @param employeeId ID của nhân viên
   * @param updateAvatarDto Thông tin avatar mới
   * @returns Nhân viên đã được cập nhật
   */
  @Transactional()
  async updateAvatar(employeeId: number, updateAvatarDto: UpdateEmployeeAvatarDto): Promise<Employee> {
    try {
      // Kiểm tra xem nhân viên có tồn tại không
      try {
        await this.employeeRepository.findById(employeeId);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      // Kiểm tra avatarKey
      if (!updateAvatarDto.avatarKey) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.AVATAR_UPDATE_FAILED,
          'Khóa avatar không được để trống'
        );
      }

      try {
        return await this.employeeRepository.updateAvatar(employeeId, updateAvatarDto.avatarKey);
      } catch (error) {
        this.logger.error(`Error updating avatar in repository: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.AVATAR_UPDATE_FAILED,
          'Cập nhật avatar thất bại'
        );
      }
    } catch (error) {
      this.logger.error(`Error updating avatar: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.AVATAR_UPDATE_FAILED,
        'Cập nhật avatar thất bại'
      );
    }
  }

  /**
   * Đổi mật khẩu cho nhân viên
   * @param employeeId ID của nhân viên
   * @param changePasswordDto Thông tin mật khẩu mới
   * @returns Thông báo kết quả
   */
  @Transactional()
  async changePassword(employeeId: number, changePasswordDto: ChangeEmployeePasswordDto): Promise<{ message: string }> {
    try {
      // Kiểm tra xem nhân viên có tồn tại không
      try {
        await this.employeeRepository.findById(employeeId);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      try {
        // Kiểm tra độ mạnh của mật khẩu
        this.passwordService.validatePasswordStrength(changePasswordDto.newPassword);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.PASSWORD_TOO_WEAK,
          error.message || 'Mật khẩu không đủ mạnh'
        );
      }

      try {
        // Mã hóa mật khẩu mới
        const hashedPassword = await this.passwordService.hashPassword(changePasswordDto.newPassword);

        // Cập nhật mật khẩu
        await this.employeeRepository.changePassword(employeeId, hashedPassword);

        return { message: 'Đổi mật khẩu thành công' };
      } catch (error) {
        this.logger.error(`Error in password change process: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.PASSWORD_CHANGE_FAILED,
          'Đổi mật khẩu thất bại'
        );
      }
    } catch (error) {
      this.logger.error(`Error changing password: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.PASSWORD_CHANGE_FAILED,
        'Đổi mật khẩu thất bại'
      );
    }
  }

  /**
   * Gán vai trò cho nhân viên
   * @param employeeId ID của nhân viên
   * @param assignRoleDto Thông tin vai trò cần gán
   * @returns Nhân viên đã được cập nhật
   */
  @Transactional()
  async assignRoles(employeeId: number, assignRoleDto: AssignEmployeeRoleDto): Promise<Employee> {
    try {
      // Kiểm tra xem nhân viên có tồn tại không
      try {
        await this.employeeRepository.findById(employeeId);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      // Kiểm tra danh sách vai trò
      if (!assignRoleDto.roleIds || assignRoleDto.roleIds.length === 0) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.ROLE_ASSIGNMENT_FAILED,
          'Danh sách vai trò không được để trống'
        );
      }

      try {
        return await this.employeeHasRoleRepository.assignRolesToEmployee(employeeId, assignRoleDto.roleIds);
      } catch (error) {
        this.logger.error(`Error in role assignment process: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.ROLE_ASSIGNMENT_FAILED,
          'Gán vai trò thất bại'
        );
      }
    } catch (error) {
      this.logger.error(`Error assigning roles: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.ROLE_ASSIGNMENT_FAILED,
        'Gán vai trò thất bại'
      );
    }
  }

  /**
   * Lấy danh sách vai trò của nhân viên
   * @param employeeId ID của nhân viên
   * @returns Danh sách vai trò
   */
  async getEmployeeRoles(employeeId: number): Promise<EmployeeRole[]> {
    try {
      // Kiểm tra xem nhân viên có tồn tại không
      try {
        await this.employeeRepository.findById(employeeId);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      try {
        return await this.employeeHasRoleRepository.findRolesByEmployeeId(employeeId);
      } catch (error) {
        this.logger.error(`Error fetching roles from repository: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.ROLE_NOT_FOUND,
          'Không thể lấy danh sách vai trò'
        );
      }
    } catch (error) {
      this.logger.error(`Error getting employee roles: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.ROLE_NOT_FOUND,
        'Không thể lấy danh sách vai trò'
      );
    }
  }

  /**
   * Lấy danh sách nhân viên với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách nhân viên với phân trang
   */
  async findAll(queryDto: EmployeeQueryDto): Promise<PaginatedResult<Employee>> {
    try {
      this.logger.log(`Finding employees with query: ${JSON.stringify(queryDto)}`);
      try {
        return await this.employeeRepository.findAll(queryDto);
      } catch (error) {
        this.logger.error(`Error fetching employees from repository: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          'Không thể lấy danh sách nhân viên'
        );
      }
    } catch (error) {
      this.logger.error(`Error finding employees: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
        'Không thể lấy danh sách nhân viên'
      );
    }
  }
}
