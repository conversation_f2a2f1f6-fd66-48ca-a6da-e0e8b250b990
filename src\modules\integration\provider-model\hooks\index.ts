import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ProviderModelService } from '../services';
import {
  ProviderModelQueryParams,
  CreateProviderModelDto,
  UpdateProviderModelDto,
} from '../types';

/**
 * Query Keys
 */
export const providerModelQueryKeys = {
  all: ['admin', 'integration', 'provider-models'] as const,
  lists: () => [...providerModelQueryKeys.all, 'list'] as const,
  list: (params?: ProviderModelQueryParams) => [...providerModelQueryKeys.lists(), params] as const,
  details: () => [...providerModelQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...providerModelQueryKeys.details(), id] as const,
};

/**
 * Hook để lấy danh sách provider models
 */
export const useProviderModels = (params?: ProviderModelQueryParams) => {
  return useQuery({
    queryKey: providerModelQueryKeys.list(params),
    queryFn: () => ProviderModelService.getProviderModels(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết provider model
 */
export const useProviderModel = (id: string) => {
  return useQuery({
    queryKey: providerModelQueryKeys.detail(id),
    queryFn: () => ProviderModelService.getProviderModel(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo provider model
 */
export const useCreateProviderModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProviderModelDto) => ProviderModelService.createProviderModel(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: providerModelQueryKeys.lists() });
    },
  });
};

/**
 * Hook để cập nhật provider model
 */
export const useUpdateProviderModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProviderModelDto }) =>
      ProviderModelService.updateProviderModel(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: providerModelQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: providerModelQueryKeys.detail(id) });
    },
  });
};

/**
 * Hook để xóa provider model
 */
export const useDeleteProviderModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ProviderModelService.deleteProviderModel(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: providerModelQueryKeys.lists() });
    },
  });
};
