import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity cho log chiến dịch Zalo
 */
@Entity('zalo_campaign_logs')
export class ZaloCampaignLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'campaign_id' })
  campaignId: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'oa_id' })
  oaId: string;

  @Column({ name: 'follower_id' })
  followerId: number;

  @Column({ name: 'follower_user_id' })
  followerUserId: string;

  @Column({ name: 'message_id', nullable: true })
  messageId?: string;

  @Column()
  status: string;

  @Column({ nullable: true })
  error?: string;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;
}
