import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, Table, Badge, Icon, ActionMenu } from '@/shared/components/common';
import { CustomerDetailData, CustomerOrder } from './types';
import { TableColumn } from '@/shared/components/common/Table/types';

interface CustomerOrdersProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị đơn hàng của khách hàng
 */
const CustomerOrders: React.FC<CustomerOrdersProps> = ({ customer }) => {
  const { t } = useTranslation('business');

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'success';
      case 'shipped':
        return 'info';
      case 'processing':
        return 'warning';
      case 'pending':
        return 'info';
      case 'cancelled':
        return 'danger';
      default:
        return 'info';
    }
  };

  // Get payment status variant
  const getPaymentStatusVariant = (status: string) => {
    switch (status) {
      case 'paid':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'danger';
      case 'refunded':
        return 'info';
      default:
        return 'info';
    }
  };

  // Get status text
  const getStatusText = (status: string) => {
    return t(`customer.order.status.${status}`, status);
  };

  // Get payment status text
  const getPaymentStatusText = (status: string) => {
    return t(`customer.order.paymentStatus.${status}`, status);
  };

  // Table columns
  const columns: TableColumn<CustomerOrder>[] = [
    {
      key: 'orderCode',
      title: t('customer.detail.orderCode'),
      dataIndex: 'orderCode',
      render: (value: unknown) => (
        <div className="flex items-center space-x-2">
          <Icon name="shopping-cart" size="sm" className="text-muted" />
          <Typography variant="body2" className="text-foreground font-medium">
            {String(value)}
          </Typography>
        </div>
      ),
    },
    {
      key: 'date',
      title: t('customer.detail.orderDate'),
      dataIndex: 'date',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-foreground">
          {formatDate(String(value))}
        </Typography>
      ),
    },
    {
      key: 'status',
      title: t('customer.detail.orderStatus'),
      dataIndex: 'status',
      render: (value: unknown) => {
        const statusValue = String(value);
        return (
          <Badge variant={getStatusVariant(statusValue)} size="sm">
            {getStatusText(statusValue)}
          </Badge>
        );
      },
    },
    {
      key: 'paymentStatus',
      title: t('customer.detail.paymentStatus'),
      dataIndex: 'paymentStatus',
      render: (value: unknown) => {
        const paymentStatusValue = String(value);
        return (
          <Badge variant={getPaymentStatusVariant(paymentStatusValue)} size="sm">
            {getPaymentStatusText(paymentStatusValue)}
          </Badge>
        );
      },
    },
    {
      key: 'totalAmount',
      title: t('customer.detail.totalAmount'),
      dataIndex: 'totalAmount',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-foreground font-medium">
          {formatCurrency(Number(value))}
        </Typography>
      ),
    },
    {
      key: 'items',
      title: t('customer.detail.items'),
      dataIndex: 'items',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-muted">
          {Number(value)} {t('customer.detail.itemsUnit')}
        </Typography>
      ),
    },
    {
      key: 'actions',
      title: t('common.actions'),
      render: (_, record: CustomerOrder) => (
        <ActionMenu
          items={[
            {
              id: 'view',
              label: t('common.view'),
              icon: 'eye',
              onClick: () => handleViewOrder(record.id),
            },
            {
              id: 'edit',
              label: t('common.edit'),
              icon: 'edit',
              onClick: () => handleEditOrder(record.id),
            },
            {
              id: 'divider1',
              divider: true,
            },
            {
              id: 'cancel',
              label: t('customer.order.cancel'),
              icon: 'x',
              onClick: () => handleCancelOrder(record.id),
              disabled: record.status === 'delivered' || record.status === 'cancelled',
            },
          ]}
        />
      ),
    },
  ];

  // Action handlers
  const handleViewOrder = (orderId: string) => {
    console.log('View order:', orderId);
    // Implement view order logic
  };

  const handleEditOrder = (orderId: string) => {
    console.log('Edit order:', orderId);
    // Implement edit order logic
  };

  const handleCancelOrder = (orderId: string) => {
    console.log('Cancel order:', orderId);
    // Implement cancel order logic
  };

  // Check if customer has orders
  const hasOrders = customer.orders && customer.orders.length > 0;

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="text-foreground">
            {t('customer.detail.orders')}
          </Typography>
          <div className="flex items-center space-x-4">
            <Typography variant="body2" className="text-muted">
              {customer.totalOrders} {t('customer.detail.totalOrders').toLowerCase()}
            </Typography>
            {customer.totalOrders > 0 && (
              <Typography variant="body2" className="text-success font-medium">
                {formatCurrency(customer.totalSpent)}
              </Typography>
            )}
          </div>
        </div>
      }
      defaultOpen={false}
    >
      {hasOrders ? (
        <div className="space-y-4">
          {/* Orders summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="p-4 bg-muted/20 rounded-lg text-center">
              <Typography variant="h4" className="text-primary mb-1">
                {customer.totalOrders}
              </Typography>
              <Typography variant="body2" className="text-muted">
                {t('customer.detail.totalOrders')}
              </Typography>
            </div>
            <div className="p-4 bg-muted/20 rounded-lg text-center">
              <Typography variant="h4" className="text-success mb-1">
                {formatCurrency(customer.totalSpent)}
              </Typography>
              <Typography variant="body2" className="text-muted">
                {t('customer.detail.totalSpent')}
              </Typography>
            </div>
            <div className="p-4 bg-muted/20 rounded-lg text-center">
              <Typography variant="h4" className="text-info mb-1">
                {formatCurrency(customer.averageOrderValue)}
              </Typography>
              <Typography variant="body2" className="text-muted">
                {t('customer.detail.averageOrderValue')}
              </Typography>
            </div>
          </div>

          {/* Orders table */}
          <Table
            data={customer.orders || []}
            columns={columns}
            rowKey="id"
            pagination={customer.orders && customer.orders.length > 10}
            size="sm"
            className="border border-border rounded-lg"
          />
        </div>
      ) : (
        <div className="text-center py-8">
          <div className="mb-4">
            <Icon name="shopping-cart" size="lg" className="text-muted mx-auto" />
          </div>
          <Typography variant="body1" className="text-muted mb-2">
            {t('customer.detail.noOrders')}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {t('customer.detail.noOrdersDesc')}
          </Typography>
        </div>
      )}
    </CollapsibleCard>
  );
};

export default CustomerOrders;
