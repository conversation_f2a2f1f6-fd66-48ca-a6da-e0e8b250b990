import { TableColumn } from './types';
import TableRow from './TableRow';
import TableCell from './TableCell';
import { get } from 'lodash';

interface TableBodyProps<T = unknown> {
  /**
   * Dữ liệu hiển thị
   */
  data: T[];

  /**
   * C<PERSON>u trúc cột
   */
  columns: TableColumn<T>[];

  /**
   * Khóa duy nhất cho mỗi hàng
   */
  rowKey?: string | ((record: T) => string);

  /**
   * Sự kiện onRow
   */
  onRow?: (record: T, index: number) => Record<string, unknown>;

  /**
   * C<PERSON> sọc không
   */
  striped?: boolean;

  /**
   * Có hover không
   */
  hoverable?: boolean;

  /**
   * Class tùy chỉnh
   */
  className?: string;
}

/**
 * Component body của bảng
 */
function TableBody<T>({
  data,
  columns,
  rowKey = 'id',
  onRow,
  striped = false,
  hoverable = false,
  className = '',
}: TableBodyProps<T>) {
  // Lấy khóa duy nhất cho mỗi hàng
  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    const value = get(record, rowKey);
    // Xử lý trường hợp value là undefined, null hoặc không có toString method
    if (value == null) {
      console.warn(`Row key "${rowKey}" is null or undefined for record:`, record);
      return index.toString();
    }
    return String(value);
  };

  // Lấy giá trị của ô
  const getCellValue = (record: T, column: TableColumn<T>, index: number) => {
    const { dataIndex, render } = column;

    // Nếu có hàm render, sử dụng nó
    if (render) {
      return render(dataIndex ? get(record, dataIndex) : record, record, index);
    }

    // Nếu không, lấy giá trị từ dataIndex
    return dataIndex ? get(record, dataIndex) : null;
  };

  return (
    <tbody className={className}>
      {data.map((record, index) => {
        // Xử lý sự kiện onRow
        const rowProps = onRow ? onRow(record, index) : {};

        // Xác định các lớp cho hàng
        const rowClasses = [
          striped && index % 2 === 1 ? 'bg-gray-50 dark:bg-gray-800/50' : '',
          hoverable ? 'hover:bg-gray-100 dark:hover:bg-gray-800' : '',
        ]
          .filter(Boolean)
          .join(' ');

        return (
          <TableRow key={getRowKey(record, index)} className={rowClasses} {...rowProps}>
            {columns.map(column => (
              <TableCell key={column.key} align={column.align} className={column.className}>
                {getCellValue(record, column, index)}
              </TableCell>
            ))}
          </TableRow>
        );
      })}

      {/* Hiển thị thông báo khi không có dữ liệu */}
      {data.length === 0 && (
        <TableRow>
          <TableCell
            colSpan={columns.length}
            className="text-center py-8 text-gray-500 dark:text-gray-400"
          >
            Không có dữ liệu
          </TableCell>
        </TableRow>
      )}
    </tbody>
  );
}

export default TableBody;
