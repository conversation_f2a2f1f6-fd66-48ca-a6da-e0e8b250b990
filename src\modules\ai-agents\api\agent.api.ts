import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

// Import types từ thư mục types
import {
  GetAgentsQueryDto,
  GetTypeAgentsQueryDto,
  GetBaseModelsQueryDto,
  AgentStatisticsQueryDto,
  CreateAgentDto,
  UpdateAgentDto,
  CreateTypeAgentDto,
  UpdateTypeAgentDto,
  CreateAgentResponseDto,
  UpdateAgentResponseDto,
  AgentStatisticsResponseDto,
  UpdateAgentVectorStoreDto,
  AgentListResponse,
  TypeAgentListResponse,
  BaseModelListResponse,
  AgentDetailDto,
  TypeAgentDetailDto
} from '../types';

// Tất cả interfaces đã được chuyển sang thư mục types
// File này chỉ chứa các API functions

/**
 * Lấy danh sách agents
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getAgents = async (
  params?: GetAgentsQueryDto
): Promise<ApiResponse<AgentListResponse>> => {
  return apiClient.get('/user/agents', { params });
};

/**
 * Lấy chi tiết agent theo ID
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const getAgentDetail = async (
  id: string
): Promise<ApiResponse<AgentDetailDto>> => {
  return apiClient.get(`/user/agents/${id}`);
};

/**
 * Tạo agent mới
 * @param data Dữ liệu tạo agent
 * @returns Promise với response từ API
 */
export const createAgent = async (
  data: CreateAgentDto
): Promise<ApiResponse<CreateAgentResponseDto>> => {
  return apiClient.post('/user/agents', data);
};

/**
 * Cập nhật agent
 * @param id ID của agent
 * @param data Dữ liệu cập nhật
 * @returns Promise với response từ API
 */
export const updateAgent = async (
  id: string,
  data: UpdateAgentDto
): Promise<ApiResponse<UpdateAgentResponseDto>> => {
  return apiClient.patch(`/user/agents/${id}`, data);
};

/**
 * Xóa agent
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const deleteAgent = async (id: string): Promise<ApiResponse<void>> => {
  return apiClient.delete(`/user/agents/${id}`);
};

/**
 * Bật/tắt trạng thái hoạt động của agent
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const toggleAgentActive = async (
  id: string
): Promise<ApiResponse<{ active: boolean }>> => {
  return apiClient.patch(`/user/agents/${id}/active`);
};

/**
 * Lấy thống kê agent
 * @param id ID của agent
 * @param params Tham số query thống kê
 * @returns Promise với response từ API
 */
export const getAgentStatistics = async (
  id: string,
  params?: AgentStatisticsQueryDto
): Promise<ApiResponse<AgentStatisticsResponseDto>> => {
  return apiClient.get(`/user/agents/${id}/statistics`, { params });
};

/**
 * Cập nhật vector store cho agent
 * @param id ID của agent
 * @param data Dữ liệu vector store
 * @returns Promise với response từ API
 */
export const updateAgentVectorStore = async (
  id: string,
  data: UpdateAgentVectorStoreDto
): Promise<ApiResponse<void>> => {
  return apiClient.patch(`/user/agents/${id}/vector-store`, data);
};

/**
 * Lấy danh sách type agents
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getTypeAgents = async (
  params?: GetTypeAgentsQueryDto
): Promise<ApiResponse<TypeAgentListResponse>> => {
  return apiClient.get('/user/type-agents', { params });
};

/**
 * Lấy chi tiết type agent theo ID
 * @param id ID của type agent
 * @returns Promise với response từ API
 */
export const getTypeAgentDetail = async (
  id: number
): Promise<ApiResponse<TypeAgentDetailDto>> => {
  return apiClient.get(`/user/type-agents/${id}`);
};

/**
 * Tạo type agent mới
 * @param data Dữ liệu tạo type agent
 * @returns Promise với response từ API
 */
export const createTypeAgent = async (
  data: CreateTypeAgentDto
): Promise<ApiResponse<void>> => {
  return apiClient.post('/user/type-agents', data);
};

/**
 * Cập nhật type agent
 * @param id ID của type agent
 * @param data Dữ liệu cập nhật
 * @returns Promise với response từ API
 */
export const updateTypeAgent = async (
  id: number,
  data: UpdateTypeAgentDto
): Promise<ApiResponse<void>> => {
  return apiClient.patch(`/user/type-agents/${id}`, data);
};

/**
 * Xóa type agent
 * @param id ID của type agent
 * @returns Promise với response từ API
 */
export const deleteTypeAgent = async (id: number): Promise<ApiResponse<void>> => {
  return apiClient.delete(`/user/type-agents/${id}`);
};

/**
 * Lấy danh sách base models
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getBaseModels = async (
  params?: GetBaseModelsQueryDto
): Promise<ApiResponse<BaseModelListResponse>> => {
  return apiClient.get('/user/base-models', { params });
};
