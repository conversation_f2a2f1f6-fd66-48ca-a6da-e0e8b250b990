import { useMutation, useQueryClient } from '@tanstack/react-query';
import { MarketplaceApiService, CreateProductDto, CreateProductResponse, UpdateProductDto, UpdateProductResponse } from '../services/marketplace-api.service';
import { PRODUCT_QUERY_KEYS } from '../constants/product-query-keys';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Hook để tạo sản phẩm mới
 */
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductDto) => MarketplaceApiService.createProduct(data),
    onSuccess: (newProduct: CreateProductResponse) => {
      // Làm mới danh sách sản phẩm của user (tất cả queries với prefix này)
      queryClient.invalidateQueries({
        queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCTS,
        exact: false
      });

      NotificationUtil.success({
        message: `Sản phẩm "${newProduct.product.name}" đã được tạo thành công!`
      });

      return newProduct;
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: `Lỗi khi tạo sản phẩm: ${error.message || 'Vui lòng thử lại'}`
      });
      throw error;
    }
  });
};

/**
 * Hook để cập nhật sản phẩm
 */
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, data }: { productId: number; data: UpdateProductDto }) =>
      MarketplaceApiService.updateProduct(productId, data),
    onSuccess: (updateResponse: UpdateProductResponse) => {
      // Làm mới danh sách sản phẩm của user (tất cả queries với prefix này)
      queryClient.invalidateQueries({
        queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCTS,
        exact: false
      });

      // Làm mới chi tiết sản phẩm nếu có
      queryClient.invalidateQueries({
        queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCT_DETAIL(updateResponse.product.id)
      });

      NotificationUtil.success({
        message: `Sản phẩm "${updateResponse.product.name}" đã được cập nhật thành công!`
      });

      return updateResponse;
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: `Lỗi khi cập nhật sản phẩm: ${error.message || 'Vui lòng thử lại'}`
      });
      throw error;
    }
  });
};

/**
 * Hook để xóa sản phẩm
 */
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productId: number) => {
      console.log('🔍 [useDeleteProduct] Deleting product ID:', productId);
      return MarketplaceApiService.deleteProduct(productId);
    },
    onSuccess: () => {
      console.log('✅ [useDeleteProduct] Product deleted successfully, invalidating queries...');

      // Làm mới tất cả queries có prefix USER_PRODUCTS (bao gồm cả có params)
      queryClient.invalidateQueries({
        queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCTS,
        exact: false // Invalidate tất cả queries bắt đầu với key này
      });

      // Cũng invalidate các queries có thể có params
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return Array.isArray(queryKey) &&
                 queryKey.length >= 3 &&
                 queryKey[0] === 'marketplace' &&
                 queryKey[1] === 'products' &&
                 queryKey[2] === 'user';
        }
      });

      console.log('✅ [useDeleteProduct] Queries invalidated');

      NotificationUtil.success({
        message: 'Sản phẩm đã được xóa thành công!'
      });
    },
    onError: (error: Error) => {
      console.error('❌ [useDeleteProduct] Error deleting product:', error);
      NotificationUtil.error({
        message: `Lỗi khi xóa sản phẩm: ${error.message || 'Vui lòng thử lại'}`
      });
      throw error;
    }
  });
};

/**
 * Hook để gửi sản phẩm để duyệt
 */
export const useSubmitProductForApproval = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productId: number) => MarketplaceApiService.submitProductForApproval(productId),
    onSuccess: (updatedProduct) => {
      // Làm mới danh sách sản phẩm của user
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCTS });

      // Làm mới chi tiết sản phẩm nếu có
      queryClient.invalidateQueries({
        queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCT_DETAIL(updatedProduct.id)
      });

      NotificationUtil.success({
        message: `Sản phẩm "${updatedProduct.name}" đã được gửi để duyệt!`
      });

      return updatedProduct;
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: `Lỗi khi gửi sản phẩm để duyệt: ${error.message || 'Vui lòng thử lại'}`
      });
      throw error;
    }
  });
};

/**
 * Hook để hủy gửi duyệt sản phẩm
 */
export const useCancelProductSubmission = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productId: number) => MarketplaceApiService.cancelProductSubmission(productId),
    onSuccess: (updatedProduct) => {
      // Làm mới danh sách sản phẩm của user
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCTS });

      // Làm mới chi tiết sản phẩm nếu có
      queryClient.invalidateQueries({
        queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCT_DETAIL(updatedProduct.id)
      });

      NotificationUtil.success({
        message: `Đã hủy gửi duyệt sản phẩm "${updatedProduct.name}"!`
      });

      return updatedProduct;
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: `Lỗi khi hủy gửi duyệt: ${error.message || 'Vui lòng thử lại'}`
      });
      throw error;
    }
  });
};
