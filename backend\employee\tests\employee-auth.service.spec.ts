import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeAuthService } from '../services/employee-auth.service';
import { EmployeeRepository } from '../repositories/employee.repository';
import { EmployeeHasRoleRepository } from '../repositories/employee-has-role.repository';
import { JwtUtilService } from '@modules/auth/guards/jwt.util';
import { UnauthorizedException } from '@nestjs/common';
import { Employee } from '../entities/employee.entity';
import { EmployeeRole } from '../entities/employee-role.entity';
import { Permission } from '../entities/permission.entity';
import * as bcrypt from 'bcrypt';

jest.mock('bcrypt');

describe('EmployeeAuthService', () => {
  let service: EmployeeAuthService;
  let employeeRepository: jest.Mocked<EmployeeRepository>;
  let employeeHasRoleRepository: jest.Mocked<EmployeeHasRoleRepository>;
  let jwtUtilService: jest.Mocked<JwtUtilService>;

  const mockEmployee: Employee = {
    id: 1,
    fullName: 'Test Employee',
    email: '<EMAIL>',
    phoneNumber: '0987654321',
    password: 'hashedPassword',
    address: 'Test Address',
    createdAt: Date.now(),
    updatedAt: Date.now(),
    enable: true,
    avatar: 'avatar-url',
    roles: []
  };

  const mockRoles: EmployeeRole[] = [
    {
      id: 1,
      name: 'Admin',
      description: 'Administrator role',
      permissions: [
        {
          id: 1,
          module: 'users',
          action: 'read',
          description: 'Read users'
        } as Permission,
        {
          id: 2,
          module: 'users',
          action: 'write',
          description: 'Write users'
        } as Permission
      ]
    }
  ];

  beforeEach(async () => {
    // Create mock implementations
    const employeeRepoMock = {
      findByEmail: jest.fn(),
    };

    const employeeHasRoleRepoMock = {
      findRolesByEmployeeId: jest.fn(),
    };

    const jwtUtilServiceMock = {
      generateEmployeeAccessToken: jest.fn(),
      generateRefreshToken: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmployeeAuthService,
        { provide: EmployeeRepository, useValue: employeeRepoMock },
        { provide: EmployeeHasRoleRepository, useValue: employeeHasRoleRepoMock },
        { provide: JwtUtilService, useValue: jwtUtilServiceMock },
      ],
    }).compile();

    service = module.get<EmployeeAuthService>(EmployeeAuthService);
    employeeRepository = module.get(EmployeeRepository) as jest.Mocked<EmployeeRepository>;
    employeeHasRoleRepository = module.get(EmployeeHasRoleRepository) as jest.Mocked<EmployeeHasRoleRepository>;
    jwtUtilService = module.get(JwtUtilService) as jest.Mocked<JwtUtilService>;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('login', () => {
    it('should successfully login an employee with valid credentials', async () => {
      // Arrange
      const loginDto = { email: '<EMAIL>', password: 'password123' };

      employeeRepository.findByEmail.mockResolvedValue(mockEmployee);
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);
      employeeHasRoleRepository.findRolesByEmployeeId.mockResolvedValue(mockRoles);

      jwtUtilService.generateEmployeeAccessToken.mockReturnValue({
        token: 'access-token',
        expiresInSeconds: 3600
      });

      jwtUtilService.generateRefreshToken.mockReturnValue({
        token: 'refresh-token',
        expiresInSeconds: 86400
      });

      // Act
      const result = await service.login(loginDto);

      // Assert
      expect(employeeRepository.findByEmail).toHaveBeenCalledWith(loginDto.email);
      expect(bcrypt.compare).toHaveBeenCalledWith(loginDto.password, mockEmployee.password);
      expect(employeeHasRoleRepository.findRolesByEmployeeId).toHaveBeenCalledWith(mockEmployee.id);

      expect(jwtUtilService.generateEmployeeAccessToken).toHaveBeenCalledWith({
        sub: mockEmployee.id,
        username: mockEmployee.email,
        permissions: ['users:read', 'users:write'],
      });

      expect(result).toEqual({
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresIn: 3600,
        refreshExpiresIn: 86400,
        employee: {
          id: mockEmployee.id,
          email: mockEmployee.email,
          fullName: mockEmployee.fullName,
          avatar: mockEmployee.avatar,
          roles: [{ id: 1, name: 'Admin', code: 'admin' }],
          permissions: ['users:read', 'users:write'],
        },
      });
    });

    it('should throw UnauthorizedException when employee is not found', async () => {
      // Arrange
      const loginDto = { email: '<EMAIL>', password: 'password123' };
      employeeRepository.findByEmail.mockResolvedValue(null);

      // Act & Assert
      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
      expect(employeeRepository.findByEmail).toHaveBeenCalledWith(loginDto.email);
    });

    it('should throw UnauthorizedException when password is incorrect', async () => {
      // Arrange
      const loginDto = { email: '<EMAIL>', password: 'wrongpassword' };
      employeeRepository.findByEmail.mockResolvedValue(mockEmployee);
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);

      // Act & Assert
      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
      expect(employeeRepository.findByEmail).toHaveBeenCalledWith(loginDto.email);
      expect(bcrypt.compare).toHaveBeenCalledWith(loginDto.password, mockEmployee.password);
    });

    it('should throw UnauthorizedException when employee account is disabled', async () => {
      // Arrange
      const loginDto = { email: '<EMAIL>', password: 'password123' };
      const disabledEmployee = { ...mockEmployee, enable: false, email: '<EMAIL>' };

      employeeRepository.findByEmail.mockResolvedValue(disabledEmployee);
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);

      // Act & Assert
      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
      expect(employeeRepository.findByEmail).toHaveBeenCalledWith(loginDto.email);
      expect(bcrypt.compare).toHaveBeenCalledWith(loginDto.password, disabledEmployee.password);
    });

    it('should handle empty permissions correctly', async () => {
      // Arrange
      const loginDto = { email: '<EMAIL>', password: 'password123' };
      const rolesWithoutPermissions = [{ ...mockRoles[0], permissions: [] }];

      employeeRepository.findByEmail.mockResolvedValue(mockEmployee);
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);
      employeeHasRoleRepository.findRolesByEmployeeId.mockResolvedValue(rolesWithoutPermissions);

      jwtUtilService.generateEmployeeAccessToken.mockReturnValue({
        token: 'access-token',
        expiresInSeconds: 3600
      });

      jwtUtilService.generateRefreshToken.mockReturnValue({
        token: 'refresh-token',
        expiresInSeconds: 86400
      });

      // Act
      const result = await service.login(loginDto);

      // Assert
      expect(result.employee.permissions).toEqual([]);
    });
  });
});
