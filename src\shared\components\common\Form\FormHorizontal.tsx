import * as React from 'react';
import { ReactNode, isValidElement, cloneElement } from 'react';

export type LabelWidth = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'auto' | 'full';
export type FieldWidth = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'auto' | 'full';
export type HorizontalGap = 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';

export interface FormHorizontalProps {
  /**
   * Nội dung của FormHorizontal (thường là các FormItem)
   */
  children: ReactNode;

  /**
   * Chiều rộng của label
   * @default 'md'
   */
  labelWidth?: LabelWidth;

  /**
   * Chiều rộng của field
   * @default 'full'
   */
  fieldWidth?: FieldWidth;

  /**
   * Khoảng cách giữa label và field
   * @default 'md'
   */
  gap?: HorizontalGap;

  /**
   * Khoảng cách giữa các hàng
   * @default 'md'
   */
  rowGap?: HorizontalGap;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Class bổ sung cho label
   */
  labelClassName?: string;

  /**
   * Class bổ sung cho field
   */
  fieldClassName?: string;

  /**
   * Tự động chuyển sang layout dọc trên mobile
   * @default false
   */
  responsive?: boolean;

  /**
   * Breakpoint để chuyển sang layout dọc
   * @default 'sm'
   */
  responsiveBreakpoint?: 'sm' | 'md' | 'lg';

  /**
   * Alignment của label
   * @default 'right'
   */
  labelAlign?: 'left' | 'right' | 'center';

  /**
   * Alignment của wrapper
   * @default 'left'
   */
  wrapperAlign?: 'left' | 'right' | 'center';

  /**
   * Hiển thị dấu hai chấm sau label
   * @default false
   */
  colon?: boolean;

  /**
   * Cho phép label wrap xuống dòng
   * @default false
   */
  labelWrap?: boolean;

  /**
   * Vertical alignment của items trong row
   * @default 'start'
   */
  verticalAlign?: 'start' | 'center' | 'end' | 'baseline' | 'stretch';

  /**
   * Kích thước của form items
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Compact mode với reduced spacing
   * @default false
   */
  compact?: boolean;
}

/**
 * Component tạo horizontal layout cho form (label bên trái, field bên phải)
 *
 * @example
 * <FormHorizontal labelWidth="md" gap="md">
 *   <FormItem name="name" label="Họ tên">
 *     <Input />
 *   </FormItem>
 *   <FormItem name="email" label="Email">
 *     <Input type="email" />
 *   </FormItem>
 * </FormHorizontal>
 */
const FormHorizontal: React.FC<FormHorizontalProps> = ({
  children,
  labelWidth = 'md',
  fieldWidth = 'full',
  gap = 'md',
  rowGap = 'md',
  className = '',
  labelClassName = '',
  fieldClassName = '',
  responsive = false,
  responsiveBreakpoint = 'sm',
  labelAlign = 'right',
  wrapperAlign = 'left',
  colon = false,
  labelWrap = false,
  verticalAlign = 'start',
  size = 'md',
  compact = false,
}) => {
  // Map label width to Tailwind class
  const labelWidthMap: Record<LabelWidth, string> = {
    xs: 'w-24',
    sm: 'w-32',
    md: 'w-40',
    lg: 'w-48',
    xl: 'w-56',
    auto: 'w-auto',
    full: 'w-full',
  };

  // Map field width to Tailwind class
  const fieldWidthMap: Record<FieldWidth, string> = {
    xs: 'w-24',
    sm: 'w-32',
    md: 'w-40',
    lg: 'w-48',
    xl: 'w-56',
    auto: 'w-auto',
    full: 'w-full',
  };

  // Map gap to Tailwind class
  const gapMap: Record<HorizontalGap, string> = {
    none: 'gap-0',
    xs: 'gap-1',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  };

  // Map row gap to Tailwind class
  const rowGapMap: Record<HorizontalGap, string> = {
    none: 'gap-y-0',
    xs: 'gap-y-1',
    sm: 'gap-y-2',
    md: 'gap-y-4',
    lg: 'gap-y-6',
    xl: 'gap-y-8',
  };

  // Alignment classes
  const labelAlignMap = {
    left: 'text-left',
    right: 'text-right',
    center: 'text-center',
  };

  const wrapperAlignMap = {
    left: 'justify-start',
    right: 'justify-end',
    center: 'justify-center',
  };

  const verticalAlignMap = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    baseline: 'items-baseline',
    stretch: 'items-stretch',
  };

  // Responsive breakpoint classes
  const responsiveBreakpointMap = {
    sm: 'sm:flex',
    md: 'md:flex',
    lg: 'lg:flex',
  };

  // Size classes
  const sizeClasses = {
    sm: compact ? 'space-y-1' : 'space-y-2',
    md: compact ? 'space-y-2' : 'space-y-4',
    lg: compact ? 'space-y-3' : 'space-y-6',
  };

  // Build container class
  const containerClass = [
    sizeClasses[size],
    rowGapMap[rowGap],
    className,
  ].filter(Boolean).join(' ');

  // Build label class
  const labelClass = [
    labelWidthMap[labelWidth],
    labelAlignMap[labelAlign],
    labelWrap ? '' : 'whitespace-nowrap',
    'flex-shrink-0',
    responsive ? 'mb-1' : 'pr-2',
    responsive && responsiveBreakpoint ? `${responsiveBreakpoint}:mb-0 ${responsiveBreakpoint}:pr-2` : '',
    labelClassName,
  ]
    .filter(Boolean)
    .join(' ');

  // Build field class
  const fieldClass = [
    fieldWidthMap[fieldWidth],
    wrapperAlignMap[wrapperAlign],
    'flex-1',
    fieldClassName,
  ].filter(Boolean).join(' ');

  // Build row class
  const rowClass = [
    'flex',
    responsive ? 'flex-col' : 'flex-row',
    responsive && responsiveBreakpoint ? responsiveBreakpointMap[responsiveBreakpoint] : '',
    verticalAlignMap[verticalAlign],
    gapMap[gap],
  ].filter(Boolean).join(' ');

  // Clone children to add horizontal layout props
  // Thêm kiểm tra an toàn cho React.Children API
  let modifiedChildren;

  try {
    // Kiểm tra xem React.Children có tồn tại không
    if (React.Children && React.Children.map) {
      modifiedChildren = React.Children.map(children, child => {
        if (isValidElement(child)) {
          // Kiểm tra xem child có phải là FormItem không
          const childType = child.type as React.ComponentType<unknown> & {
            displayName?: string;
            name?: string;
          };
          const isFormItem =
            childType &&
            (childType.displayName === 'FormItem' ||
              (typeof childType === 'function' && childType.name === 'FormItem'));

          if (isFormItem) {
            // Lấy props hiện tại của child
            const childProps = child.props as Record<string, unknown>;

            // Tạo props mới với enhanced features
            const newProps = {
              ...childProps,
              labelClassName: `${labelClass} ${childProps.labelClassName || ''}`,
              className: `${rowClass} ${childProps.className || ''}`,
              // Add a wrapper div around the input to apply field class
              children: <div className={fieldClass}>{childProps.children as React.ReactNode}</div>,
              // Set labelPosition to left for horizontal layout
              labelPosition: 'left',
              // Pass through colon setting
              colon: colon || childProps.colon,
              // Pass through size
              size: size,
              // Set inline to true to prevent default FormItem layout
              inline: true,
            };

            return cloneElement(child, newProps);
          }
        }
        return child;
      });
    } else {
      // Fallback khi React.Children không khả dụng
      console.warn('React.Children API không khả dụng, sử dụng fallback');
      modifiedChildren = children;
    }
  } catch (error) {
    // Xử lý lỗi khi React.Children.map gặp vấn đề
    console.error('Lỗi khi sử dụng React.Children.map:', error);
    modifiedChildren = children;
  }

  return <div className={containerClass}>{modifiedChildren}</div>;
};

export default FormHorizontal;
