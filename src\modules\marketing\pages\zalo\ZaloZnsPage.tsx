import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  FileText,
  Send
} from 'lucide-react';
import { Card } from '@/shared/components/common';
import { Button } from '@/shared/components/common';
import { Input } from '@/shared/components/common';
import { Badge } from '@/shared/components/common';
import { Modal } from '@/shared/components/common';
import { Pagination } from '@/shared/components/common';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import { CreateZnsTemplateForm } from '../../components/zalo/CreateZnsTemplateForm';
import { ZNSTemplateDto, ZNSTemplateStatus } from '../../types/zalo.types';

/**
 * Trang quản lý ZNS Templates
 */
export function ZaloZnsPage() {
  const { t } = useTranslation('marketing');

  const [query, setQuery] = useState({
    page: 1,
    limit: 20,
    search: '',
    status: '',
  });
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ZNSTemplateDto | null>(null);

  // Mock data cho demo
  const templatesData = {
    items: [
      {
        id: 1,
        userId: 1,
        oaId: 1,
        templateId: 'ZNS_001',
        name: 'Xác nhận đơn hàng',
        content: 'Xin chào {{customer_name}}, đơn hàng {{order_id}} của bạn đã được xác nhận với tổng tiền {{total_amount}} VNĐ.',
        params: ['customer_name', 'order_id', 'total_amount'],
        status: 'ENABLE' as ZNSTemplateStatus,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 2,
        userId: 1,
        oaId: 1,
        templateId: 'ZNS_002',
        name: 'Thông báo khuyến mãi',
        content: 'Xin chào {{customer_name}}, bạn có ưu đãi {{discount_percent}}% hết hạn {{expiry_date}}.',
        params: ['customer_name', 'discount_percent', 'expiry_date'],
        status: 'PENDING_REVIEW' as ZNSTemplateStatus,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 3,
        userId: 1,
        oaId: 1,
        templateId: 'ZNS_003',
        name: 'Nhắc nhở thanh toán',
        content: 'Xin chào {{customer_name}}, bạn có khoản thanh toán {{amount_due}} VNĐ đến hạn {{due_date}}.',
        params: ['customer_name', 'amount_due', 'due_date'],
        status: 'REJECT' as ZNSTemplateStatus,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
    ],
    meta: {
      totalItems: 3,
      itemCount: 3,
      itemsPerPage: 20,
      totalPages: 1,
      currentPage: 1,
    },
  };

  const handleSearch = (value: string) => {
    setQuery(prev => ({ ...prev, search: value, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setQuery(prev => ({ ...prev, page }));
  };

  const handleCreateSuccess = () => {
    setShowCreateModal(false);
    // Refresh data
  };

  const handlePreviewTemplate = (template: ZNSTemplateDto) => {
    setSelectedTemplate(template);
    setShowPreviewModal(true);
  };

  // Removed unused function

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ENABLE':
        return <Badge variant="success">Đã duyệt</Badge>;
      case 'PENDING_REVIEW':
        return <Badge variant="warning">Chờ duyệt</Badge>;
      case 'REJECT':
        return <Badge variant="danger">Từ chối</Badge>;
      case 'DISABLE':
        return <Badge variant="info">Vô hiệu hóa</Badge>;
      default:
        return <Badge variant="info">{status}</Badge>;
    }
  };

  // Removed unused function getQualityBadge

  return (
    <div className="space-y-6">
      {/* Header */}
      <MarketingViewHeader
        title={t('zalo.zns.title', 'ZNS Templates')}
        description={t('zalo.zns.description', 'Quản lý template thông báo Zalo Notification Service')}
        actions={
          <Button onClick={() => setShowCreateModal(true)} className="gap-2">
            <Plus className="h-4 w-4" />
            {t('zalo.zns.createTemplate', 'Tạo Template')}
          </Button>
        }
      />

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Tổng Templates</span>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">{templatesData.meta.totalItems}</div>
          <p className="text-xs text-muted-foreground">+2 template mới</p>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Đã duyệt</span>
              <FileText className="h-4 w-4 text-green-600" />
            </div>
          }
        >
          <div className="text-2xl font-bold text-green-600">
            {templatesData.items.filter(t => t.status === ZNSTemplateStatus.APPROVED).length}
          </div>
          <p className="text-xs text-muted-foreground">Sẵn sàng sử dụng</p>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Chờ duyệt</span>
              <FileText className="h-4 w-4 text-yellow-600" />
            </div>
          }
        >
          <div className="text-2xl font-bold text-yellow-600">
            {templatesData.items.filter(t => t.status === ZNSTemplateStatus.PENDING).length}
          </div>
          <p className="text-xs text-muted-foreground">Đang xem xét</p>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Chi phí TB</span>
              <Send className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">
            770 VNĐ
          </div>
          <p className="text-xs text-muted-foreground">Mỗi tin nhắn</p>
        </Card>
      </div>

      {/* Filters */}
      <Card title="Bộ lọc">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('zalo.zns.searchPlaceholder', 'Tìm kiếm template...')}
                value={query.search}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Bộ lọc nâng cao
          </Button>
        </div>
      </Card>

      {/* Templates Table */}
      <Card
        title="Danh sách Templates"
        subtitle={`Tổng cộng ${templatesData.meta.totalItems} templates`}
      >
        <div className="rounded-md border overflow-hidden">
          <table className="w-full">
            <thead className="bg-muted/50">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium">Template</th>
                <th className="px-4 py-3 text-left text-sm font-medium">Template ID</th>
                <th className="px-4 py-3 text-left text-sm font-medium">Trạng thái</th>
                <th className="px-4 py-3 text-left text-sm font-medium">Chất lượng</th>
                <th className="px-4 py-3 text-left text-sm font-medium">Chi phí</th>
                <th className="px-4 py-3 text-left text-sm font-medium">Tham số</th>
                <th className="px-4 py-3 text-left text-sm font-medium">Cập nhật</th>
                <th className="px-4 py-3 text-left text-sm font-medium">Thao tác</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              {templatesData.items.map((template) => (
                <tr key={template.id} className="hover:bg-muted/50">
                  <td className="px-4 py-3">
                    <div>
                      <div className="font-medium">{template.name}</div>
                      <div className="text-xs text-muted-foreground">
                        ID: {template.templateId}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 font-mono text-sm">
                    {template.templateId}
                  </td>
                  <td className="px-4 py-3">
                    {getStatusBadge(template.status)}
                  </td>
                  <td className="px-4 py-3">
                    <Badge variant="info">Normal</Badge>
                  </td>
                  <td className="px-4 py-3 font-medium">
                    770 VNĐ
                  </td>
                  <td className="px-4 py-3">
                    <div className="text-sm">
                      {template.params.length} tham số
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm text-muted-foreground">
                    {new Date(template.updatedAt).toLocaleDateString('vi-VN')}
                  </td>
                  <td className="px-4 py-3">
                    <div className="relative">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePreviewTemplate(template)}
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {templatesData.meta.totalPages > 1 && (
          <div className="mt-4">
            <Pagination
              currentPage={templatesData.meta.currentPage}
              totalPages={templatesData.meta.totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </Card>

      {/* Create Template Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="Tạo ZNS Template"
        size="lg"
      >
        <CreateZnsTemplateForm onSuccess={handleCreateSuccess} />
      </Modal>

      {/* Preview Template Modal */}
      <Modal
        isOpen={showPreviewModal}
        onClose={() => setShowPreviewModal(false)}
        title="Xem trước Template"
        size="md"
      >
        {selectedTemplate && (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">{selectedTemplate.name}</h4>
              <p className="text-sm text-muted-foreground">
                ID: {selectedTemplate.templateId}
              </p>
            </div>

            <div className="space-y-2">
              <h5 className="text-sm font-medium">Nội dung:</h5>
              <div className="p-3 bg-muted rounded-lg">
                <p className="text-sm">{selectedTemplate.content}</p>
              </div>
            </div>

            <div className="space-y-2">
              <h5 className="text-sm font-medium">Tham số:</h5>
              <div className="space-y-1">
                {selectedTemplate.params.map((param: string, index: number) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="font-mono">{param}</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="info" className="text-xs">
                        STRING
                      </Badge>
                      <Badge variant="danger" className="text-xs">
                        Bắt buộc
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}

export default ZaloZnsPage;
