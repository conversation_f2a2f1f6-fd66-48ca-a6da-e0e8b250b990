import { IsIn, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Enum cho các loại máy chủ
 */
export enum ServerType {
  SMTP = 'smtp',
  API = 'api',
  SMS_GATEWAY = 'sms_gateway',
  FIREBASE = 'firebase',
}

/**
 * DTO cho thông tin máy chủ gửi
 */
export class CampaignServerDto {
  /**
   * Loại máy chủ
   * @example "smtp"
   */
  @ApiProperty({
    description: 'Loại máy chủ',
    enum: ServerType,
    example: ServerType.SMTP,
  })
  @IsNotEmpty({ message: '<PERSON>ạ<PERSON> máy chủ không được để trống' })
  @IsIn(Object.values(ServerType), {
    message: `Loại máy chủ phải là một trong các giá trị: ${Object.values(ServerType).join(', ')}`,
  })
  type: ServerType;

  /**
   * Tên máy chủ
   * @example "Gmail SMTP"
   */
  @ApiProperty({
    description: 'Tên máy chủ',
    example: 'Gmail SMTP',
  })
  @IsNotEmpty({ message: 'Tên máy chủ không được để trống' })
  @IsString({ message: 'Tên máy chủ phải là chuỗi' })
  name: string;

  /**
   * Cấu hình máy chủ
   * @example { "host": "smtp.gmail.com", "port": 587, "secure": false, "auth": { "user": "<EMAIL>", "pass": "password" } }
   */
  @ApiProperty({
    description: 'Cấu hình máy chủ',
    example: {
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'password',
      },
    },
  })
  @IsNotEmpty({ message: 'Cấu hình máy chủ không được để trống' })
  config: any;

  /**
   * Địa chỉ email người gửi (chỉ áp dụng cho máy chủ email)
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Địa chỉ email người gửi (chỉ áp dụng cho máy chủ email)',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Địa chỉ email người gửi phải là chuỗi' })
  fromEmail?: string;

  /**
   * Tên người gửi (chỉ áp dụng cho máy chủ email)
   * @example "Marketing Team"
   */
  @ApiProperty({
    description: 'Tên người gửi (chỉ áp dụng cho máy chủ email)',
    example: 'Marketing Team',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên người gửi phải là chuỗi' })
  fromName?: string;
}
