import { useApiQuery, useApiMutation } from '@/shared/api/hooks';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import { apiClient } from '@/shared/api/axios';
import {
  CreateUrlDto,
  UpdateUrlDto,
  UrlDto,
  FindAllUrlDto,
  PaginatedUrlResult,
  CrawlDto,
  CrawlResultDto,
} from '../types/url.types';
import { PaginatedFileResult } from '@/modules/data/knowledge-files/types/knowledge-files.types';
import { deleteMultipleUrls } from '../services/url.service';

// Đ<PERSON><PERSON> nghĩa các query key
export const URL_QUERY_KEYS = {
  all: ['data', 'url'] as const,
  lists: () => [...URL_QUERY_KEYS.all, 'list'] as const,
  list: (filters: FindAllUrlDto) => [...URL_QUERY_KEYS.lists(), filters] as const,
  details: () => [...URL_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...URL_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách URL
 * @param queryDto Tham số truy vấn
 * @returns Query object
 */
export const useUrls = (queryDto?: FindAllUrlDto) => {
  return useApiQuery<PaginatedUrlResult>(URL_QUERY_KEYS.list(queryDto || {}), '/data/url', {
    params: queryDto,
  });
};

/**
 * Hook để lấy thông tin chi tiết URL
 * @param id ID của URL
 * @returns Query object
 */
export const useUrlDetail = (id: string) => {
  return useApiQuery<UrlDto>(URL_QUERY_KEYS.detail(id), `/data/url/${id}`);
};

/**
 * Hook để tạo URL mới
 * @returns Mutation object
 */
export const useCreateUrl = () => {
  const queryClient = useQueryClient();

  return useApiMutation<UrlDto, CreateUrlDto>('/data/url', {
    onSuccess: () => {
      // Invalidate và refetch danh sách URL
      queryClient.invalidateQueries({ queryKey: URL_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để cập nhật thông tin URL
 * @param id ID của URL
 * @returns Mutation object
 */
export const useUpdateUrl = (id: string) => {
  const queryClient = useQueryClient();

  return useApiMutation<UrlDto, UpdateUrlDto>(`/data/url/${id}`, {
    onSuccess: () => {
      // Invalidate và refetch thông tin chi tiết URL
      queryClient.invalidateQueries({ queryKey: URL_QUERY_KEYS.detail(id) });
      // Invalidate và refetch danh sách URL
      queryClient.invalidateQueries({ queryKey: URL_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa nhiều URL cùng lúc
 * @returns Mutation object
 */
export const useDeleteMultipleUrls = () => {
  const queryClient = useQueryClient();

  return useMutation<{ success: boolean }, unknown, { ids: string[] }>({
    mutationFn: async (variables: { ids: string[] }) => {
      // Sử dụng axios trực tiếp để có thể truyền body trong DELETE request
      const response = await deleteMultipleUrls(variables.ids);
      return response;
    },
    onSuccess: () => {
      // Invalidate và refetch danh sách URL
      queryClient.invalidateQueries({ queryKey: URL_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa URL
 * @returns Mutation object
 */
export const useDeleteUrl = () => {
  const { mutateAsync: deleteMultiple } = useDeleteMultipleUrls();

  return {
    mutateAsync: async (id: string) => {
      // Sử dụng API batch để xóa một URL
      return deleteMultiple({ ids: [id] });
    }
  };
};

/**
 * Hook để crawl URL
 * @returns Mutation object
 */
export const useCrawlUrl = () => {
  const queryClient = useQueryClient();

  return useApiMutation<CrawlResultDto, CrawlDto>('/data/url/crawl', {
    onSuccess: () => {
      // Invalidate và refetch danh sách URL
      queryClient.invalidateQueries({ queryKey: URL_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để lấy danh sách file tri thức (sử dụng cho gán vào vector store)
 * @returns Function để lấy danh sách file
 */
export const useGetUnassignedFiles = () => {
  const getUnassignedFiles = async (params: {
    vectorStoreId: string;
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      // Sử dụng API từ knowledge-files thay vì files/unassigned
      const response = await apiClient.get<PaginatedFileResult>('/user/knowledge-files', {
        params: {
          page: params.page || 1,
          limit: params.limit || 10,
          search: params.search || '',
          sortBy: 'createdAt',
          sortDirection: 'DESC'
        },
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching knowledge files:', error);
      throw error;
    }
  };

  return { getUnassignedFiles };
};
