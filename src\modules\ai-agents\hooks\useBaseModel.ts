import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { getBaseModels } from '../api/agent.api';
import {
  GetBaseModelsQueryDto,
  BaseModelListResponse,
  TypeProviderEnum,
  BaseModelSortByEnum,
  SortDirection,
} from '../types';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

/**
 * Hook để lấy danh sách base models
 * @param params Query params
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetBaseModels = (
  params?: GetBaseModelsQueryDto,
  options?: UseQueryOptions<ApiResponse<BaseModelListResponse>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.BASE_MODEL_LIST, params],
    queryFn: () => getBaseModels(params),
    staleTime: 10 * 60 * 1000, // 10 minutes (base models ít thay đổi)
    ...options,
  });
};

/**
 * Hook để lấy base models theo provider
 * @param providerType Loại provider
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetBaseModelsByProvider = (
  providerType: TypeProviderEnum,
  options?: UseQueryOptions<ApiResponse<BaseModelListResponse>>
) => {
  const params: GetBaseModelsQueryDto = {
    provider_type: providerType,
    sortBy: BaseModelSortByEnum.CREATED_AT,
    sortDirection: SortDirection.DESC,
    limit: 100, // Lấy nhiều để có đủ models
  };

  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.BASE_MODEL_LIST, 'by-provider', providerType],
    queryFn: () => getBaseModels(params),
    staleTime: 15 * 60 * 1000, // 15 minutes (longer cache for provider-specific data)
    enabled: !!providerType, // Chỉ chạy khi có providerType
    ...options,
  });
};

/**
 * Hook để lấy tất cả base models cho dropdown selection
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetAllBaseModels = (
  options?: UseQueryOptions<ApiResponse<BaseModelListResponse>>
) => {
  const params: GetBaseModelsQueryDto = {
    sortBy: BaseModelSortByEnum.CREATED_AT,
    sortDirection: SortDirection.DESC,
    limit: 200, // Lấy nhiều để có đủ models
  };

  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.BASE_MODEL_LIST, 'all'],
    queryFn: () => getBaseModels(params),
    staleTime: 20 * 60 * 1000, // 20 minutes (longest cache for all models)
    ...options,
  });
};

/**
 * Utility function để convert BaseModelListItemDto thành Select options
 */
export const convertBaseModelsToSelectOptions = (
  models: BaseModelListResponse['items']
) => {
  return models.map((model) => ({
    value: model.model_id,
    label: `${model.model_id} - ${model.description}`,
    data: model, // Lưu toàn bộ data để sử dụng sau
  }));
};

/**
 * Utility function để group models theo provider
 */
export const groupBaseModelsByProvider = (
  models: BaseModelListResponse['items']
) => {
  return models.reduce((acc, model) => {
    const provider = model.providerType;
    if (!acc[provider]) {
      acc[provider] = [];
    }
    acc[provider].push(model);
    return acc;
  }, {} as Record<TypeProviderEnum, BaseModelListResponse['items']>);
};

/**
 * Utility function để lấy provider name hiển thị
 */
export const getProviderDisplayName = (provider: TypeProviderEnum): string => {
  const providerNames: Record<TypeProviderEnum, string> = {
    [TypeProviderEnum.OPENAI]: 'OpenAI',
    [TypeProviderEnum.ANTHROPIC]: 'Anthropic',
    [TypeProviderEnum.GOOGLE]: 'Google',
    [TypeProviderEnum.META]: 'Meta',
    [TypeProviderEnum.DEEPSEEK]: 'DeepSeek',
    [TypeProviderEnum.XAI]: 'XAI',
  };

  return providerNames[provider] || provider;
};

/**
 * Utility function để lấy provider icon
 */
export const getProviderIcon = (provider: TypeProviderEnum): string => {
  const providerIcons: Record<TypeProviderEnum, string> = {
    [TypeProviderEnum.OPENAI]: 'openai',
    [TypeProviderEnum.ANTHROPIC]: 'anthropic',
    [TypeProviderEnum.GOOGLE]: 'google',
    [TypeProviderEnum.META]: 'meta',
    [TypeProviderEnum.DEEPSEEK]: 'robot',
    [TypeProviderEnum.XAI]: 'robot',
  };

  return providerIcons[provider] || 'robot';
};

/**
 * Type cho config có thể có cả 2 format
 */
type ModelConfigInput = {
  // Old format
  top_p?: boolean;
  top_k?: boolean;
  function?: boolean;
  file_search?: boolean;
  temperature?: boolean;
  response_format?: string[];
  code_interpreter?: boolean;
  reasoning_effort?: string[];

  // New format
  hasText?: boolean;
  hasTopK?: boolean;
  hasTopP?: boolean;
  hasAudio?: boolean;
  hasImage?: boolean;
  hasVideo?: boolean;
  hasFunction?: boolean;
  hasTemperature?: boolean;
  hasReasoningEffort?: string[];
  hasParallelToolCall?: boolean;
} | null | undefined;

/**
 * Utility function để normalize config từ cả 2 format (old và new)
 */
export const normalizeModelConfig = (config: ModelConfigInput) => {
  if (!config) return null;

  return {
    // File search / Vector store - sử dụng file_search hoặc hasFunction làm fallback
    file_search: config.file_search ?? (config.hasFunction !== undefined ? config.hasFunction : true),

    // Temperature
    temperature: config.temperature ?? (config.hasTemperature !== undefined ? config.hasTemperature : true),

    // Top P
    top_p: config.top_p ?? (config.hasTopP !== undefined ? config.hasTopP : true),

    // Top K
    top_k: config.top_k ?? (config.hasTopK !== undefined ? config.hasTopK : true),

    // Function calling
    function: config.function ?? (config.hasFunction !== undefined ? config.hasFunction : true),

    // Other fields
    response_format: config.response_format ?? [],
    code_interpreter: config.code_interpreter ?? false,
    reasoning_effort: config.reasoning_effort ?? config.hasReasoningEffort ?? [],

    // New format fields
    hasText: config.hasText ?? false,
    hasAudio: config.hasAudio ?? false,
    hasImage: config.hasImage ?? false,
    hasVideo: config.hasVideo ?? false,
    hasParallelToolCall: config.hasParallelToolCall ?? false,
  };
};
