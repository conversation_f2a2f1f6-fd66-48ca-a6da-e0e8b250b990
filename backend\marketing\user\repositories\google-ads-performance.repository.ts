import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { GoogleAdsPerformance } from '../entities/google-ads-performance.entity';

@Injectable()
export class GoogleAdsPerformanceRepository {
  constructor(
    @InjectRepository(GoogleAdsPerformance)
    private readonly repository: Repository<GoogleAdsPerformance>,
  ) {}

  /**
   * Tìm báo cáo hiệu suất Google Ads theo ID
   * @param id ID của báo cáo hiệu suất
   * @returns Báo cáo hiệu suất Google Ads
   */
  async findById(id: number): Promise<GoogleAdsPerformance | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm báo cáo hiệu suất Google Ads theo ngày và chiến dịch
   * @param campaignId ID của chiến dịch
   * @param date Ng<PERSON><PERSON> của báo cáo (YYYY-MM-DD)
   * @param userId ID của người dùng
   * @returns Báo cáo hiệu suất Google Ads
   */
  async findByDateAndCampaignId(
    campaignId: number,
    date: string,
    userId: number,
  ): Promise<GoogleAdsPerformance | null> {
    return this.repository.findOne({
      where: { campaignId, date, userId },
    });
  }

  /**
   * Lấy danh sách báo cáo hiệu suất Google Ads của chiến dịch
   * @param campaignId ID của chiến dịch
   * @param userId ID của người dùng
   * @returns Danh sách báo cáo hiệu suất Google Ads
   */
  async findByCampaignId(campaignId: number, userId: number): Promise<GoogleAdsPerformance[]> {
    return this.repository.find({
      where: { campaignId, userId },
      order: { date: 'ASC' },
    });
  }

  /**
   * Lấy danh sách báo cáo hiệu suất Google Ads của chiến dịch trong khoảng thời gian
   * @param campaignId ID của chiến dịch
   * @param startDate Ngày bắt đầu (YYYY-MM-DD)
   * @param endDate Ngày kết thúc (YYYY-MM-DD)
   * @param userId ID của người dùng
   * @returns Danh sách báo cáo hiệu suất Google Ads
   */
  async findByCampaignIdAndDateRange(
    campaignId: number,
    startDate: string,
    endDate: string,
    userId: number,
  ): Promise<GoogleAdsPerformance[]> {
    return this.repository.find({
      where: {
        campaignId,
        userId,
        date: Between(startDate, endDate),
      },
      order: { date: 'ASC' },
    });
  }

  /**
   * Tạo báo cáo hiệu suất Google Ads mới
   * @param data Dữ liệu báo cáo hiệu suất
   * @returns Báo cáo hiệu suất Google Ads đã tạo
   */
  async create(data: Partial<GoogleAdsPerformance>): Promise<GoogleAdsPerformance> {
    const performance = this.repository.create(data);
    return this.repository.save(performance);
  }

  /**
   * Tạo nhiều báo cáo hiệu suất Google Ads
   * @param dataList Danh sách dữ liệu báo cáo hiệu suất
   * @returns Danh sách báo cáo hiệu suất Google Ads đã tạo
   */
  async createMany(dataList: Partial<GoogleAdsPerformance>[]): Promise<GoogleAdsPerformance[]> {
    const performances = dataList.map(data => this.repository.create(data));
    return this.repository.save(performances);
  }

  /**
   * Cập nhật báo cáo hiệu suất Google Ads
   * @param id ID của báo cáo hiệu suất
   * @param data Dữ liệu cập nhật
   * @returns true nếu cập nhật thành công
   */
  async update(id: number, data: Partial<GoogleAdsPerformance>): Promise<boolean> {
    const result = await this.repository.update(id, data);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Xóa báo cáo hiệu suất Google Ads
   * @param id ID của báo cáo hiệu suất
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Xóa tất cả báo cáo hiệu suất của chiến dịch
   * @param campaignId ID của chiến dịch
   * @param userId ID của người dùng
   * @returns true nếu xóa thành công
   */
  async deleteByCampaignId(campaignId: number, userId: number): Promise<boolean> {
    const result = await this.repository.delete({ campaignId, userId });
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }
}
