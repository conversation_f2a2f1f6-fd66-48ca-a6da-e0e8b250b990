import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  WebsiteDto,
  WebsiteQueryDto,
  UpdateWebsiteDto,
  WebsiteStatsDto,
  WebsiteAnalyticsDto,
} from '../types/website.types';
import {
  getWebsites,
  createWebsite,
  getWebsiteDetail,
  updateWebsite,
  deleteWebsite,
  deleteManyWebsites,
  connectAgentToWebsite,
  disconnectAgentFromWebsite,
  verifyWebsite,
  getWebsiteVerificationCode,
  checkWebsiteVerification,
  getWebsiteStats,
  getWebsiteAnalytics,
} from '../api/website.api';

/**
 * Website Integration Hooks
 * React Query hooks cho Website integration
 */

// Query Keys
export const WEBSITE_QUERY_KEYS = {
  WEBSITES: 'websites',
  WEBSITE_DETAIL: 'website-detail',
  WEBSITE_VERIFICATION_CODE: 'website-verification-code',
  WEBSITE_VERIFICATION_STATUS: 'website-verification-status',
  WEBSITE_STATS: 'website-stats',
  WEBSITE_ANALYTICS: 'website-analytics',
} as const;

/**
 * Hook để lấy danh sách Websites
 * @param params Query parameters
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetWebsites = (
  params?: WebsiteQueryDto,
  options?: UseQueryOptions<ApiResponseDto<PaginatedResult<WebsiteDto>>>
) => {
  return useQuery({
    queryKey: [WEBSITE_QUERY_KEYS.WEBSITES, params],
    queryFn: () => getWebsites(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để lấy chi tiết Website
 * @param websiteId ID của Website
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetWebsiteDetail = (
  websiteId: string,
  options?: UseQueryOptions<ApiResponseDto<WebsiteDto>>
) => {
  return useQuery({
    queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_DETAIL, websiteId],
    queryFn: () => getWebsiteDetail(websiteId),
    enabled: !!websiteId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để tạo mới Website
 * @returns Mutation function
 */
export const useCreateWebsite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { websiteName: string; host: string }) => createWebsite(data),
    onSuccess: () => {
      // Invalidate websites queries
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITES],
      });
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_STATS],
      });
    },
  });
};

/**
 * Hook để cập nhật Website
 * @returns Mutation function
 */
export const useUpdateWebsite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ websiteId, data }: { websiteId: string; data: UpdateWebsiteDto }) =>
      updateWebsite(websiteId, data),
    onSuccess: (_, variables) => {
      // Invalidate websites queries
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITES],
      });
      // Invalidate specific website detail
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_DETAIL, variables.websiteId],
      });
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_STATS],
      });
    },
  });
};

/**
 * Hook để xóa Website
 * @returns Mutation function
 */
export const useDeleteWebsite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteWebsite,
    onSuccess: () => {
      // Invalidate websites queries
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITES],
      });
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_STATS],
      });
    },
  });
};

/**
 * Hook để xóa nhiều Websites
 * @returns Mutation function
 */
export const useDeleteManyWebsites = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteManyWebsites,
    onSuccess: () => {
      // Invalidate websites queries
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITES],
      });
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_STATS],
      });
    },
  });
};

/**
 * Hook để kết nối Agent với Website
 * @returns Mutation function
 */
export const useConnectAgentToWebsite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { agentId: string; websiteId: string }) =>
      connectAgentToWebsite(data),
    onSuccess: (_, variables) => {
      // Invalidate websites queries
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITES],
      });
      // Invalidate specific website detail
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_DETAIL, variables.websiteId],
      });
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_STATS],
      });
    },
  });
};

/**
 * Hook để ngắt kết nối Agent khỏi Website
 * @returns Mutation function
 */
export const useDisconnectAgentFromWebsite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { websiteId: string }) => disconnectAgentFromWebsite(data),
    onSuccess: (_, variables) => {
      // Invalidate websites queries
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITES],
      });
      // Invalidate specific website detail
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_DETAIL, variables.websiteId],
      });
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_STATS],
      });
    },
  });
};

/**
 * Hook để xác minh Website
 * @returns Mutation function
 */
export const useVerifyWebsite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { websiteId: string; verificationCode?: string }) =>
      verifyWebsite(data),
    onSuccess: (_, variables) => {
      // Invalidate websites queries
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITES],
      });
      // Invalidate specific website detail
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_DETAIL, variables.websiteId],
      });
      // Invalidate verification status
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_VERIFICATION_STATUS, variables.websiteId],
      });
      queryClient.invalidateQueries({
        queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_STATS],
      });
    },
  });
};

/**
 * Hook để lấy mã xác minh Website
 * @param websiteId ID của Website
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetWebsiteVerificationCode = (
  websiteId: string,
  options?: UseQueryOptions<ApiResponseDto<{ verificationCode: string }>>
) => {
  return useQuery({
    queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_VERIFICATION_CODE, websiteId],
    queryFn: () => getWebsiteVerificationCode(websiteId),
    enabled: !!websiteId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

/**
 * Hook để kiểm tra trạng thái xác minh Website
 * @param websiteId ID của Website
 * @param options TanStack Query options
 * @returns Query result
 */
export const useCheckWebsiteVerification = (
  websiteId: string,
  options?: UseQueryOptions<ApiResponseDto<{ verified: boolean; lastChecked: Date }>>
) => {
  return useQuery({
    queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_VERIFICATION_STATUS, websiteId],
    queryFn: () => checkWebsiteVerification(websiteId),
    enabled: !!websiteId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

/**
 * Hook để lấy thống kê Websites
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetWebsiteStats = (
  options?: UseQueryOptions<ApiResponseDto<WebsiteStatsDto>>
) => {
  return useQuery({
    queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_STATS],
    queryFn: getWebsiteStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để lấy analytics Website
 * @param websiteId ID của Website
 * @param startDate Ngày bắt đầu
 * @param endDate Ngày kết thúc
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetWebsiteAnalytics = (
  websiteId: string,
  startDate: Date,
  endDate: Date,
  options?: UseQueryOptions<ApiResponseDto<WebsiteAnalyticsDto>>
) => {
  return useQuery({
    queryKey: [WEBSITE_QUERY_KEYS.WEBSITE_ANALYTICS, websiteId, startDate, endDate],
    queryFn: () => getWebsiteAnalytics(websiteId, startDate, endDate),
    enabled: !!websiteId && !!startDate && !!endDate,
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};
