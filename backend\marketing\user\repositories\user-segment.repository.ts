import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { UserSegment } from '../entities/user-segment.entity';

/**
 * Repository cho UserSegment
 */
@Injectable()
export class UserSegmentRepository {
  constructor(
    @InjectRepository(UserSegment)
    private readonly repository: Repository<UserSegment>,
  ) {}

  /**
   * Tìm kiếm nhiều segment
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách segment
   */
  async find(options?: FindManyOptions<UserSegment>): Promise<UserSegment[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một segment
   * @param options Tùy chọn tìm kiếm
   * @returns Segment hoặc null
   */
  async findOne(options?: FindOneOptions<UserSegment>): Promise<UserSegment | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Lưu segment
   * @param segment Segment cần lưu
   * @returns Segment đã lưu
   */
  async save(segment: UserSegment): Promise<UserSegment>;
  async save(segment: UserSegment[]): Promise<UserSegment[]>;
  async save(segment: UserSegment | UserSegment[]): Promise<UserSegment | UserSegment[]> {
    return this.repository.save(segment as any);
  }

  /**
   * Xóa segment
   * @param segment Segment cần xóa
   * @returns Segment đã xóa
   */
  async remove(segment: UserSegment): Promise<UserSegment>;
  async remove(segment: UserSegment[]): Promise<UserSegment[]>;
  async remove(segment: UserSegment | UserSegment[]): Promise<UserSegment | UserSegment[]> {
    return this.repository.remove(segment as any);
  }

  /**
   * Đếm số lượng segment
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng segment
   */
  async count(options?: FindManyOptions<UserSegment>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }

  /**
   * Thực hiện truy vấn SQL trực tiếp
   * @param query Câu truy vấn SQL
   * @param parameters Tham số cho câu truy vấn
   * @returns Kết quả truy vấn
   */
  async executeQuery(query: string, parameters?: any[]): Promise<any> {
    return this.repository.query(query, parameters);
  }
}
