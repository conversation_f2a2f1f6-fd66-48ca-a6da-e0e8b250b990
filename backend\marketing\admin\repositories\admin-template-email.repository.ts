import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, FindOptionsWhere, FindOneOptions } from 'typeorm';
import { AdminTemplateEmail } from '@modules/marketing/admin/entities';
import { CategoryTemplateAutoEnum } from '@modules/email/interface/category-template-auto.enum';

/**
 * Repository cho AdminTemplateEmail
 */
@Injectable()
export class AdminTemplateEmailRepository {
  constructor(
    @InjectRepository(AdminTemplateEmail)
    readonly repository: Repository<AdminTemplateEmail>,
  ) {}

  /**
   * Tìm template email theo category
   * @param category Danh mục của template
   * @returns Template email
   */
  async findByCategory(category: string): Promise<AdminTemplateEmail> {
    const template = await this.repository.findOne({ where: { category } });
    if (!template) {
      throw new NotFoundException(`Template email với category ${category} không tồn tại`);
    }
    return template;
  }

  /**
   * Tìm template email theo category sử dụng enum
   * @param category Danh mục của template (enum)
   * @returns Template email
   */
  async findTemplateAutoByCategory(category: CategoryTemplateAutoEnum): Promise<AdminTemplateEmail> {
    return this.findByCategory(category);
  }

  /**
   * Lấy tất cả template email
   * @returns Danh sách template email
   */
  async findAll(): Promise<AdminTemplateEmail[]> {
    return this.repository.find();
  }

  /**
   * Tìm kiếm nhiều template email
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách template email
   */
  async find(options?: FindManyOptions<AdminTemplateEmail>): Promise<AdminTemplateEmail[]> {
    return this.repository.find(options || {});
  }

  /**
   * Đếm số lượng template email
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng template email
   */
  async count(options?: FindManyOptions<AdminTemplateEmail>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }

  /**
   * Lấy template email theo ID
   * @param id ID của template
   * @returns Template email
   */
  async findById(id: number): Promise<AdminTemplateEmail> {
    const template = await this.repository.findOne({ where: { id } });
    if (!template) {
      throw new NotFoundException(`Template email với ID ${id} không tồn tại`);
    }
    return template;
  }

  /**
   * Tạo mới template email
   * @param data Dữ liệu template email
   * @returns Template email đã tạo
   */
  async create(data: Partial<AdminTemplateEmail>): Promise<AdminTemplateEmail> {
    const template = this.repository.create(data);
    return this.repository.save(template);
  }

  /**
   * Cập nhật template email
   * @param id ID của template
   * @param data Dữ liệu cập nhật
   * @returns Template email đã cập nhật
   */
  async update(id: number, data: Partial<AdminTemplateEmail>): Promise<AdminTemplateEmail> {
    await this.findById(id); // Kiểm tra template tồn tại
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa template email
   * @param id ID của template
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    await this.findById(id); // Kiểm tra template tồn tại
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }
}
