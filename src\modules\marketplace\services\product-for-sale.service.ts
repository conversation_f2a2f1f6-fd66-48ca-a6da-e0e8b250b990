import { ProductForSale, ProductForSaleFilterParams, ProductForSaleListResponse, ProductStatus, ProductCategory } from '../types/product-for-sale.types';

/**
 * Service cho Products for Sale
 */
export class ProductForSaleService {
  /**
   * <PERSON><PERSON><PERSON> danh sách sản phẩm đăng bán
   */
  static async getProductsForSale(params?: ProductForSaleFilterParams): Promise<ProductForSaleListResponse> {
    // Mock data với categories đúng
    const mockData: ProductForSale[] = [
      {
        id: '1',
        name: 'AI Agent Hỗ trợ Khách hàng',
        description: 'Agent AI thông minh hỗ trợ khách hàng 24/7',
        image: '/images/products/product-1.jpg',
        quantity: 100,
        price: 1000000,
        category: ProductCategory.AGENT,
        status: ProductStatus.ACTIVE,
        createdAt: '2023-07-15',
        updatedAt: '2023-07-15',
      },
      {
        id: '2',
        name: 'Knowledge File Tài liệu <PERSON>p lý',
        description: '<PERSON>ộ tài liệu pháp lý đầy đủ cho doanh nghiệp',
        image: '/images/products/product-2.jpg',
        quantity: 50,
        price: 1500000,
        category: ProductCategory.KNOWLEDGE_FILE,
        status: ProductStatus.ACTIVE,
        createdAt: '2023-07-16',
        updatedAt: '2023-07-16',
      },
      {
        id: '3',
        name: 'Function API Tích hợp Thanh toán',
        description: 'Function API tích hợp thanh toán đa nền tảng',
        image: '/images/products/product-3.jpg',
        quantity: 75,
        price: 2000000,
        category: ProductCategory.FUNCTION,
        status: ProductStatus.ACTIVE,
        createdAt: '2023-07-17',
        updatedAt: '2023-07-17',
      },
      {
        id: '4',
        name: 'Fine-tuned Model Phân tích Cảm xúc',
        description: 'Model AI đã được fine-tune để phân tích cảm xúc',
        image: '/images/products/product-4.jpg',
        quantity: 30,
        price: 1200000,
        category: ProductCategory.FINETUNE,
        status: ProductStatus.DRAFT,
        createdAt: '2023-07-18',
        updatedAt: '2023-07-18',
      },
      {
        id: '5',
        name: 'Strategy Phát triển Sản phẩm',
        description: 'Chiến lược phát triển sản phẩm toàn diện',
        image: '/images/products/product-5.jpg',
        quantity: 20,
        price: 1800000,
        category: ProductCategory.STRATEGY,
        status: ProductStatus.INACTIVE,
        createdAt: '2023-07-19',
        updatedAt: '2023-07-19',
      },
    ];

    // Filter data based on params
    let filteredData = [...mockData];
    if (params) {
      if (params.search) {
        const searchLower = params.search.toLowerCase();
        filteredData = filteredData.filter(
          (item) =>
            item.name.toLowerCase().includes(searchLower) ||
            item.description.toLowerCase().includes(searchLower)
        );
      }

      if (params.status && params.status !== 'all') {
        filteredData = filteredData.filter((item) => item.status === params.status);
      }

      if (params.category && params.category !== 'all') {
        filteredData = filteredData.filter((item) => item.category === params.category);
      }
    }

    // Pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    return {
      code: 200,
      message: 'Success',
      result: {
        items: paginatedData,
        meta: {
          totalItems: filteredData.length,
          itemCount: paginatedData.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(filteredData.length / limit),
          currentPage: page,
        },
      },
    };
  }

  /**
   * Lấy chi tiết sản phẩm đăng bán
   */
  static async getProductForSale(id: string): Promise<ProductForSale> {
    // Sử dụng lại mock data từ getProductsForSale
    const mockData: ProductForSale[] = [
      {
        id: '1',
        name: 'AI Agent Hỗ trợ Khách hàng',
        description: 'Agent AI thông minh hỗ trợ khách hàng 24/7',
        image: '/images/products/product-1.jpg',
        quantity: 100,
        price: 1000000,
        category: ProductCategory.AGENT,
        status: ProductStatus.ACTIVE,
        createdAt: '2023-07-15',
        updatedAt: '2023-07-15',
      },
      {
        id: '2',
        name: 'Knowledge File Tài liệu Pháp lý',
        description: 'Bộ tài liệu pháp lý đầy đủ cho doanh nghiệp',
        image: '/images/products/product-2.jpg',
        quantity: 50,
        price: 1500000,
        category: ProductCategory.KNOWLEDGE_FILE,
        status: ProductStatus.ACTIVE,
        createdAt: '2023-07-16',
        updatedAt: '2023-07-16',
      },
      // ... other mock data
    ];

    // Find product by id
    const product = mockData.find((item) => item.id === id);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    if (!product) {
      throw new Error('Product not found');
    }

    return product;
  }

  /**
   * Tạo sản phẩm đăng bán mới
   */
  static async createProductForSale(data: Omit<ProductForSale, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProductForSale> {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Create new product
    const newProduct: ProductForSale = {
      id: Math.random().toString(36).substring(2, 11),
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return newProduct;
  }

  /**
   * Cập nhật sản phẩm đăng bán
   */
  static async updateProductForSale(id: string, data: Partial<ProductForSale>): Promise<ProductForSale> {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Mock updated product
    const updatedProduct: ProductForSale = {
      id,
      name: data.name || 'Updated Product',
      description: data.description || 'Updated description',
      image: data.image || '/images/products/product-1.jpg',
      quantity: data.quantity || 100,
      price: data.price || 1000000,
      category: data.category || ProductCategory.AGENT,
      status: data.status || ProductStatus.ACTIVE,
      createdAt: '2023-07-15',
      updatedAt: new Date().toISOString(),
    };

    return updatedProduct;
  }

  /**
   * Xóa sản phẩm đăng bán
   */
  static async deleteProductForSale(/* id: string */): Promise<{ success: boolean }> {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    return { success: true };
  }
}
