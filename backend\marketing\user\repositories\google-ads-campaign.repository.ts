import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GoogleAdsCampaign } from '../entities/google-ads-campaign.entity';

@Injectable()
export class GoogleAdsCampaignRepository {
  constructor(
    @InjectRepository(GoogleAdsCampaign)
    private readonly repository: Repository<GoogleAdsCampaign>,
  ) {}

  /**
   * Tìm chiến dịch Google Ads theo ID
   * @param id ID của chiến dịch
   * @returns Chiến dịch Google Ads
   */
  async findById(id: number): Promise<GoogleAdsCampaign | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm chiến dịch Google Ads theo ID và ID người dùng
   * @param id ID của chiến dịch
   * @param userId ID của người dùng
   * @returns Chiến dịch Google Ads
   */
  async findByIdAndUserId(id: number, userId: number): Promise<GoogleAdsCampaign | null> {
    return this.repository.findOne({ where: { id, userId } });
  }

  /**
   * Tìm chiến dịch Google Ads theo ID chiến dịch trên Google Ads
   * @param campaignId ID của chiến dịch trên Google Ads
   * @param userId ID của người dùng
   * @returns Chiến dịch Google Ads
   */
  async findByCampaignId(campaignId: string, userId: number): Promise<GoogleAdsCampaign | null> {
    return this.repository.findOne({ where: { campaignId, userId } });
  }

  /**
   * Tìm chiến dịch Google Ads theo ID chiến dịch marketing
   * @param userCampaignId ID của chiến dịch marketing
   * @param userId ID của người dùng
   * @returns Chiến dịch Google Ads
   */
  async findByUserCampaignId(userCampaignId: number, userId: number): Promise<GoogleAdsCampaign | null> {
    return this.repository.findOne({ where: { userCampaignId, userId } });
  }

  /**
   * Lấy danh sách chiến dịch Google Ads của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách chiến dịch Google Ads
   */
  async findByUserId(userId: number): Promise<GoogleAdsCampaign[]> {
    return this.repository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Lấy danh sách chiến dịch Google Ads của tài khoản
   * @param accountId ID của tài khoản Google Ads
   * @param userId ID của người dùng
   * @returns Danh sách chiến dịch Google Ads
   */
  async findByAccountId(accountId: number, userId: number): Promise<GoogleAdsCampaign[]> {
    return this.repository.find({
      where: { accountId, userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tạo chiến dịch Google Ads mới
   * @param data Dữ liệu chiến dịch
   * @returns Chiến dịch Google Ads đã tạo
   */
  async create(data: Partial<GoogleAdsCampaign>): Promise<GoogleAdsCampaign> {
    const campaign = this.repository.create(data);
    return this.repository.save(campaign);
  }

  /**
   * Cập nhật chiến dịch Google Ads
   * @param id ID của chiến dịch
   * @param data Dữ liệu cập nhật
   * @returns true nếu cập nhật thành công
   */
  async update(id: number, data: Partial<GoogleAdsCampaign>): Promise<boolean> {
    const result = await this.repository.update(id, data);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Xóa chiến dịch Google Ads
   * @param id ID của chiến dịch
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }
}
