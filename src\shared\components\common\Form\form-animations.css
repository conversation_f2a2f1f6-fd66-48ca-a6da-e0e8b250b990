/* Form animations for enhanced UX */

/* Fade in animation for error messages */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide down animation for error messages */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 100px;
  }
}

/* Animation classes */
.animate-fadeIn {
  animation: fadeIn 0.2s ease-out;
}

.animate-slideDown {
  animation: slideDown 0.3s ease-out;
}

/* Form item status styles */
.form-item--success {
  /* Success state styles */
}

.form-item--warning {
  /* Warning state styles */
}

.form-item--error {
  /* Error state styles */
}

.form-item--validating {
  /* Validating state styles */
}

/* Required field indicator */
.required::after {
  content: " *";
  color: #ef4444; /* text-red-500 */
}

/* Tooltip styles */
.form-tooltip {
  position: absolute;
  bottom: 100%;
  left: 0;
  margin-bottom: 8px;
  padding: 4px 8px;
  background-color: #1f2937; /* gray-800 */
  color: white;
  font-size: 12px;
  border-radius: 4px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  z-index: 10;
}

.form-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 8px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #1f2937; /* gray-800 */
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .form-item {
    /* Mobile-specific styles */
  }
  
  .form-tooltip {
    position: fixed;
    bottom: auto;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 8px;
    margin-bottom: 0;
  }
  
  .form-tooltip::after {
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    border-top: none;
    border-bottom: 4px solid #1f2937;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .form-tooltip {
    background-color: #374151; /* gray-700 */
  }
  
  .form-tooltip::after {
    border-top-color: #374151; /* gray-700 */
  }
  
  @media (max-width: 640px) {
    .form-tooltip::after {
      border-bottom-color: #374151; /* gray-700 */
    }
  }
}
