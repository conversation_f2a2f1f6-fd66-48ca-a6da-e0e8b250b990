/**
 * Marketing Module
 *
 * Module quản lý marketing, bao gồm:
 * - Quản lý Audience
 * - Quản lý Segment
 * - Quản lý Chiến dịch
 */

// Export components
export * from './components';

// Export pages
export * from './pages';

// Export hooks
export * from './hooks';

// Export types
export * from './types';

// Export schemas
export * from './schemas';

// Export services
export * from './services';

// Export routes
export { default as marketingRoutes } from './marketingRoutes';

// Export pages
export { MarketingDashboardPage } from './pages/MarketingDashboardPage';
export { ZaloOverviewPage } from './pages/zalo/ZaloOverviewPage';
export { ZaloAccountsPage } from './pages/zalo/ZaloAccountsPage';
export { ZaloFollowersPage } from './pages/zalo/ZaloFollowersPage';
export { ZaloZnsPage } from './pages/zalo/ZaloZnsPage';
export { EmailOverviewPage } from './pages/email/EmailOverviewPage';
export { EmailTemplatesPage } from './pages/email/EmailTemplatesPage';

// Export components
export { ConnectZaloAccountForm } from './components/zalo/ConnectZaloAccountForm';
export { CreateZnsTemplateForm } from './components/zalo/CreateZnsTemplateForm';
export { CreateEmailTemplateForm } from './components/email/CreateEmailTemplateForm';
export { MarketingViewHeader } from './components/common/MarketingViewHeader';

// Export hooks
export { useZaloAccounts, useZaloAccount, useZaloAccountManagement } from './hooks/zalo/useZaloAccounts';
export { useZaloFollowers, useZaloFollowerManagement } from './hooks/zalo/useZaloFollowers';
export { useEmailTemplates, useEmailTemplateManagement, useCreateEmailTemplate } from './hooks/email/useEmailTemplates';

// Export services
export { ZaloService } from './services/zalo.service';
export { EmailService } from './services/email.service';

// Export types
export type * from './types/zalo.types';
export type * from './types/email.types';

// Export schemas
export * from './schemas/zalo.schema';
export * from './schemas/email.schema';
