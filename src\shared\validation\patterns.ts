import { ValidationPatterns } from './types';

/**
 * Common validation patterns and regular expressions
 */
export const VALIDATION_PATTERNS: ValidationPatterns = {
  // Email pattern - RFC 5322 compliant
  email: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,

  // Phone patterns
  phone: /^[+]?[\d\s\-()]{10,15}$/,

  // URL pattern
  url: /^https?:\/\/(?:[-\w.])+(?::[0-9]+)?(?:\/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)?$/,

  // IP address patterns
  ipv4: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  ipv6: /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/,

  // UUID pattern
  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,

  // Credit card pattern (basic)
  creditCard: /^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3[0-9]{13}|6(?:011|5[0-9]{2})[0-9]{12})$/,

  // Postal code patterns
  postalCode: /^[0-9]{5}(?:-[0-9]{4})?$/,

  // Password strength patterns
  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  mediumPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{6,}$/,
  weakPassword: /^.{4,}$/,
};

/**
 * Vietnamese-specific validation patterns
 */
export const VIETNAMESE_PATTERNS = {
  // Vietnamese phone number
  phoneVN: /^(0|\+84)[3-9][0-9]{8}$/,

  // Vietnamese postal code
  postalCodeVN: /^[0-9]{6}$/,

  // Vietnamese citizen ID
  citizenIdVN: /^[0-9]{9}$|^[0-9]{12}$/,

  // Vietnamese name pattern (allows Vietnamese characters)
  nameVN: /^[a-zA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵýỷỹ\s]+$/,

  // Vietnamese address pattern
  addressVN: /^[a-zA-Z0-9ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵýỷỹ\s,.-/]+$/,
};

/**
 * Business validation patterns
 */
export const BUSINESS_PATTERNS = {
  // Tax ID patterns
  taxIdUS: /^[0-9]{2}-[0-9]{7}$/,
  taxIdVN: /^[0-9]{10}$|^[0-9]{13}$/,

  // Bank account patterns
  bankAccountVN: /^[0-9]{6,19}$/,

  // Credit card patterns by type
  visa: /^4[0-9]{12}(?:[0-9]{3})?$/,
  mastercard: /^5[1-5][0-9]{14}$/,
  amex: /^3[47][0-9]{13}$/,
  discover: /^6(?:011|5[0-9]{2})[0-9]{12}$/,

  // Currency patterns
  currency: /^\d+(\.\d{1,2})?$/,
  currencyWithCommas: /^\d{1,3}(,\d{3})*(\.\d{1,2})?$/,

  // Percentage pattern
  percentage: /^(100(\.0{1,2})?|[0-9]{1,2}(\.[0-9]{1,2})?)$/,
};

/**
 * File validation patterns
 */
export const FILE_PATTERNS = {
  // Image file extensions
  imageExtensions: /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i,

  // Document file extensions
  documentExtensions: /\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|rtf)$/i,

  // Video file extensions
  videoExtensions: /\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i,

  // Audio file extensions
  audioExtensions: /\.(mp3|wav|flac|aac|ogg|wma)$/i,

  // Archive file extensions
  archiveExtensions: /\.(zip|rar|7z|tar|gz|bz2)$/i,
};

/**
 * Security validation patterns
 */
export const SECURITY_PATTERNS = {
  // SQL injection detection
  sqlInjection: /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)|('|('')|;|--|\/\*|\*\/)/i,

  // XSS detection
  xss: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,

  // HTML tags
  htmlTags: /<[^>]*>/g,

  // Base64 pattern
  base64: /^[A-Za-z0-9+/]*={0,2}$/,

  // JWT token pattern
  jwt: /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,
};

/**
 * Date and time patterns
 */
export const DATE_TIME_PATTERNS = {
  // Date formats
  dateISO: /^\d{4}-\d{2}-\d{2}$/,
  dateUS: /^\d{2}\/\d{2}\/\d{4}$/,
  dateEU: /^\d{2}\/\d{2}\/\d{4}$/,
  dateVN: /^\d{2}\/\d{2}\/\d{4}$/,

  // Time formats
  time24: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
  time12: /^(0?[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM)$/i,

  // DateTime formats
  dateTimeISO: /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/,
};

/**
 * Network patterns
 */
export const NETWORK_PATTERNS = {
  // Domain name
  domain: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,

  // MAC address
  macAddress: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,

  // Port number
  port: /^([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$/,
};

/**
 * Utility functions for pattern validation
 */
export const PatternUtils = {
  /**
   * Test if a value matches a pattern
   */
  test: (pattern: RegExp, value: string): boolean => {
    return pattern.test(value);
  },

  /**
   * Get all matches for a pattern in a string
   */
  getMatches: (pattern: RegExp, value: string): RegExpMatchArray[] => {
    const matches: RegExpMatchArray[] = [];
    let match;
    const globalPattern = new RegExp(pattern.source, pattern.flags + (pattern.global ? '' : 'g'));

    while ((match = globalPattern.exec(value)) !== null) {
      matches.push(match);
    }

    return matches;
  },

  /**
   * Replace all matches of a pattern in a string
   */
  replaceAll: (pattern: RegExp, value: string, replacement: string): string => {
    return value.replace(new RegExp(pattern.source, pattern.flags + 'g'), replacement);
  },

  /**
   * Extract specific parts from a pattern match
   */
  extract: (pattern: RegExp, value: string, groupIndex = 0): string | null => {
    const match = pattern.exec(value);
    return match ? match[groupIndex] : null;
  },

  /**
   * Validate multiple patterns against a value
   */
  validateMultiple: (patterns: RegExp[], value: string, mode: 'all' | 'any' = 'all'): boolean => {
    if (mode === 'all') {
      return patterns.every(pattern => pattern.test(value));
    } else {
      return patterns.some(pattern => pattern.test(value));
    }
  },

  /**
   * Create a combined pattern from multiple patterns
   */
  combine: (patterns: RegExp[], operator: 'and' | 'or' = 'or'): RegExp => {
    const sources = patterns.map(p => `(${p.source})`);
    const combined = operator === 'and'
      ? sources.join('.*')
      : sources.join('|');

    return new RegExp(combined);
  },

  /**
   * Escape special regex characters in a string
   */
  escape: (value: string): string => {
    return value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  },

  /**
   * Create a pattern for exact match
   */
  exact: (pattern: RegExp): RegExp => {
    const source = pattern.source.startsWith('^') ? pattern.source : `^${pattern.source}`;
    const exactSource = source.endsWith('$') ? source : `${source}$`;
    return new RegExp(exactSource, pattern.flags);
  },
};
