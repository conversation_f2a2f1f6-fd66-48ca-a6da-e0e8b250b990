// Export components
export { default as ProductCard } from './components/ProductCard';
export { default as ProductGallery } from './components/ProductGallery';
export { default as QuantityInput } from './components/QuantityInput';
export { default as ProductForSaleForm } from './components/forms/ProductForSaleForm';

// Export pages
export { default as ProductListPage } from './pages/ProductListPage';
export { default as ProductDetailPage } from './pages/ProductDetailPage';
export { default as MarketplacePage } from './pages/MarketplacePage';
export { default as ProductsForSalePage } from './pages/ProductsForSalePage';
export { default as PurchasedProductsPage } from './pages/PurchasedProductsPage';

// Export hooks
export { useProductList } from './hooks/useProductList';
export { useProductDetail } from './hooks/useProductDetail';
export { useCart, useCartManager } from './hooks/useCartApi';
export { useCartRedux } from './hooks/useCartRedux';
export { useSafeCartRedux } from './hooks/useSafeCartRedux';
export {
  useCreateProduct,
  useUpdateProduct,
  useDeleteProduct,
  useSubmitProductForApproval,
  useCancelProductSubmission,
} from './hooks/useUserProducts';

// Export routes
export { default as marketplaceRoutes } from './routers/marketplaceRoutes';

// Export constants
export { PRODUCT_QUERY_KEYS } from './constants/product-query-keys';

// Export types
export * from './types/product-for-sale.types';
// Re-export specific types from purchased-product.types to avoid naming conflicts
export { PurchaseStatus } from './types/purchased-product.types';
export type {
  PurchasedProduct,
  PurchasedProductListResponse,
  PurchasedProductFilterParams,
  Seller
} from './types/purchased-product.types';
