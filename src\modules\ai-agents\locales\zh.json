{"aiAgents": {"common": {"save": "保存", "cancel": "取消", "add": "添加", "edit": "编辑", "delete": "删除", "search": "搜索", "filter": "筛选", "sort": "排序", "required": "必填", "update": "更新", "create": "创建", "select": "选择", "configure": "配置", "name": "名称", "description": "描述", "type": "类型", "status": "状态", "createdAt": "创建时间", "updatedAt": "更新时间", "actions": "操作", "noData": "暂无数据", "loading": "加载中...", "error": "发生错误", "success": "成功", "confirm": "确认", "confirmDelete": "确定要删除吗？", "yes": "是", "no": "否", "close": "关闭", "back": "返回", "next": "下一步", "previous": "上一步", "finish": "完成"}, "agentCreate": {"title": "创建智能助手", "customAgentButton": "创建自定义智能助手", "selectAgentType": "选择智能助手类型", "selectAgentDescription": "从下面的列表中选择一种智能助手类型，或创建您自己的自定义智能助手。", "configureAgent": "配置 {name}", "sortBy": "排序方式", "sortName": "名称", "sortDate": "创建日期", "order": "顺序", "orderAsc": "升序", "orderDesc": "降序", "agentTypeDescription": "选择适合您需求的智能助手类型。每种智能助手类型都有不同的功能和特点。"}, "profileConfig": {"title": "个人信息", "name": "姓名", "birthDate": "出生日期", "gender": "性别", "language": "语言", "education": "教育程度", "country": "国家", "position": "职位", "skills": "技能", "personality": "性格", "avatar": "头像", "male": "男", "female": "女", "other": "其他", "highSchool": "高中", "college": "大专", "university": "本科", "postgraduate": "研究生", "addSkill": "添加技能", "addPersonality": "添加性格特点", "skillPlaceholder": "输入标签并按回车", "personalityPlaceholder": "输入标签并按回车"}, "modelConfig": {"title": "模型配置", "provider": "提供商", "model": "模型", "vectorStore": "向量存储", "advancedSettings": "高级设置", "maxTokens": "最大令牌数", "temperature": "温度", "topP": "Top P", "topK": "Top K", "instructions": "指令", "instructionsPlaceholder": "输入模型指令..."}, "integrationConfig": {"title": "集成", "facebook": "Facebook", "website": "网站", "addFacebook": "添加 Facebook 集成", "addWebsite": "添加网站集成", "noFacebookIntegration": "暂无 Facebook 集成", "noWebsiteIntegration": "暂无网站集成", "selectFacebook": "选择 Facebook 页面", "selectWebsite": "选择网站", "facebookPageName": "Facebook 页面名称", "websiteName": "网站名称", "websiteUrl": "网站 URL"}, "strategyConfig": {"title": "策略", "selectStrategy": "为您的智能助手选择策略", "selectStrategyDescription": "选择一个策略并配置处理步骤", "basicStrategy": "基础策略", "basicStrategyDescription": "带有默认设置的简单策略", "advancedStrategy": "高级策略", "advancedStrategyDescription": "带有高级选项和复杂处理的策略", "customStrategy": "自定义策略", "customStrategyDescription": "创建您自己的完全自定义设置的策略", "configureStrategy": "配置策略", "step": "步骤 {number}", "input": "输入"}, "convertConfig": {"title": "转换配置", "configureFields": "配置要收集的数据字段", "configureFieldsDescription": "选择智能助手将从用户那里收集的数据字段", "noFields": "尚未配置数据字段", "addField": "添加新字段", "editField": "编辑字段", "fieldName": "字段名称", "fieldDescription": "描述", "fieldType": "数据类型", "fieldRequired": "必填字段", "fieldNamePlaceholder": "输入字段名称（例如：email）", "fieldDescriptionPlaceholder": "输入字段描述（例如：收集用户的所有电子邮件）", "pleaseEnterAllFields": "请输入所有字段信息", "text": "文本", "email": "电子邮件", "phone": "电话", "number": "数字", "date": "日期", "address": "地址", "name": "姓名"}, "responseConfig": {"title": "响应资源", "configureResources": "为智能助手配置响应资源", "configureResourcesDescription": "选择智能助手可以用来响应用户的资源", "media": "媒体资源", "url": "URL 资源", "product": "产品资源", "noMedia": "暂无媒体资源", "noUrl": "暂无 URL 资源", "noProduct": "暂无产品资源", "selectMedia": "选择媒体", "selectUrl": "选择 URL", "selectProduct": "选择产品"}}}