import { InputHTMLAttributes, forwardRef, ReactNode } from 'react';

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  fullWidth?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    { label, error, helperText, leftIcon, rightIcon, fullWidth = false, className = '', ...rest },
    ref
  ) => {
    // Base classes - bỏ border trong light mode
    const baseClasses =
      'input focus:outline-none focus:border-primary bg-card-muted text-foreground border-0 dark:border dark:border-border';

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Error class - chỉ áp dụng border error trong dark mode
    const errorClass = error ? 'dark:border-error focus:ring-error' : '';

    // Icon padding classes
    const leftPaddingClass = leftIcon ? 'pl-10' : '';
    const rightPaddingClass = rightIcon ? 'pr-10' : '';

    // Combine all classes
    const inputClasses = [
      baseClasses,
      widthClass,
      errorClass,
      leftPaddingClass,
      rightPaddingClass,
      className,
    ].join(' ');

    return (
      <div className={`${fullWidth ? 'w-full' : ''}`}>
        {label && <label className="block text-sm font-medium text-foreground mb-1">{label}</label>}

        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              {leftIcon}
            </div>
          )}

          <input ref={ref} className={inputClasses} {...rest} />

          {rightIcon && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">{rightIcon}</div>
          )}
        </div>

        {error && <p className="mt-1 text-sm text-error">{error}</p>}

        {helperText && !error && <p className="mt-1 text-sm text-muted">{helperText}</p>}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
