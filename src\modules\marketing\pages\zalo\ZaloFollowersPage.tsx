import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import {
  Search,
  Filter,
  Users,
  MessageCircle,
  Tag as TagIcon,
  Download,
  UserPlus,
  Settings
} from 'lucide-react';
import { Card } from '@/shared/components/common';
import { Button } from '@/shared/components/common';
import { Input } from '@/shared/components/common';
import { Modal } from '@/shared/components/common';
import { Pagination } from '@/shared/components/common';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import { useZaloFollowers, useZaloFollowerManagement } from '../../hooks/zalo/useZaloFollowers';
import { useZaloAccount } from '../../hooks/zalo/useZaloAccounts';
import type { ZaloFollowerQueryDto, ZaloFollowerDto } from '../../types/zalo.types';

/**
 * Trang quản lý Zalo Followers
 */
export function ZaloFollowersPage() {
  const { t } = useTranslation('marketing');
  const { oaId } = useParams<{ oaId: string }>();

  const [query, setQuery] = useState<ZaloFollowerQueryDto>({
    page: 1,
    limit: 20,
    search: '',
  });
  const [selectedFollowers, setSelectedFollowers] = useState<string[]>([]);
  const [showBulkModal, setShowBulkModal] = useState(false);

  const oaIdNumber = parseInt(oaId || '0');
  const { data: accountData } = useZaloAccount(oaIdNumber);
  const { data: followersData } = useZaloFollowers(oaIdNumber, query);
  const { bulkOperation } = useZaloFollowerManagement();

  const handleSearch = (value: string) => {
    setQuery(prev => ({ ...prev, search: value, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setQuery(prev => ({ ...prev, page }));
  };

  const handleBulkAddTag = async (tagName: string) => {
    await bulkOperation.mutateAsync({
      oaId: oaIdNumber,
      data: {
        followerIds: selectedFollowers,
        operation: 'ADD_TAG',
        tagName,
      },
    });
    setSelectedFollowers([]);
    setShowBulkModal(false);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <MarketingViewHeader
        title={t('zalo.followers.title', 'Quản lý Followers')}
        description={
          accountData
            ? t('zalo.followers.description', 'Followers của {{name}}', { name: accountData.name })
            : t('zalo.followers.descriptionDefault', 'Quản lý danh sách followers')
        }

        actions={
          <div className="flex gap-2">
            <Button variant="outline" className="gap-2">
              <Download className="h-4 w-4" />
              {t('zalo.followers.export', 'Export')}
            </Button>
            <Button className="gap-2">
              <UserPlus className="h-4 w-4" />
              {t('zalo.followers.sync', 'Đồng bộ')}
            </Button>
          </div>
        }
      />

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Tổng Followers</span>
              <Users className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">
            {followersData?.meta.totalItems.toLocaleString() || 0}
          </div>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Hoạt động</span>
              <Users className="h-4 w-4 text-green-600" />
            </div>
          }
        >
          <div className="text-2xl font-bold text-green-600">
            {followersData?.items.filter((f: ZaloFollowerDto) => f.status === 'ACTIVE').length || 0}
          </div>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Mới tuần này</span>
              <UserPlus className="h-4 w-4 text-blue-600" />
            </div>
          }
        >
          <div className="text-2xl font-bold text-blue-600">+24</div>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Đã chọn</span>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">
            {selectedFollowers.length}
          </div>
        </Card>
      </div>

      {/* Filters & Actions */}
      <Card
        title="Bộ lọc và thao tác"
      >
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('zalo.followers.searchPlaceholder', 'Tìm kiếm theo tên, số điện thoại...')}
                  value={query.search || ''}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Button variant="outline" className="gap-2">
              <Filter className="h-4 w-4" />
              Bộ lọc nâng cao
            </Button>
          </div>

          {selectedFollowers.length > 0 && (
            <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
              <span className="text-sm font-medium">
                Đã chọn {selectedFollowers.length} followers
              </span>
              <Button size="sm" onClick={() => setShowBulkModal(true)}>
                <TagIcon className="h-4 w-4 mr-1" />
                Thêm tag
              </Button>
              <Button size="sm" variant="outline">
                <MessageCircle className="h-4 w-4 mr-1" />
                Gửi tin nhắn
              </Button>
            </div>
          )}
        </div>
      </Card>

      {/* Followers Table */}
      <Card
        title="Danh sách Followers"
        subtitle={
          followersData?.meta.totalItems
            ? `Tổng cộng ${followersData.meta.totalItems} followers`
            : 'Chưa có followers nào'
        }
      >
        <div className="rounded-md border">
          <div className="rounded-md border">
            <div className="text-center py-8">
              <p className="text-muted-foreground">Followers table will be implemented</p>
            </div>
          </div>

        </div>

        {/* Pagination */}
        {followersData && followersData.meta.totalPages > 1 && (
          <div className="mt-4">
            <Pagination
              currentPage={followersData.meta.currentPage}
              totalPages={followersData.meta.totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </Card>

      {/* Bulk Actions Modal */}
      <Modal
        isOpen={showBulkModal}
        onClose={() => setShowBulkModal(false)}
        title="Thao tác hàng loạt"
        size="md"
      >
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Thực hiện thao tác cho {selectedFollowers.length} followers đã chọn
          </p>

          <div className="space-y-2">
            <Button
              className="w-full justify-start"
              variant="outline"
              onClick={() => handleBulkAddTag('VIP')}
            >
              <TagIcon className="h-4 w-4 mr-2" />
              Thêm tag "VIP"
            </Button>
            <Button
              className="w-full justify-start"
              variant="outline"
              onClick={() => handleBulkAddTag('Khách hàng mới')}
            >
              <TagIcon className="h-4 w-4 mr-2" />
              Thêm tag "Khách hàng mới"
            </Button>
            <Button
              className="w-full justify-start"
              variant="outline"
            >
              <MessageCircle className="h-4 w-4 mr-2" />
              Gửi tin nhắn hàng loạt
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default ZaloFollowersPage;
