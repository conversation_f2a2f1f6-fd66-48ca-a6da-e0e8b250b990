import { Test, TestingModule } from '@nestjs/testing';
import { AdminEmailServerConfigurationService } from '../admin-email-server-configuration.service';
import { AdminEmailServerConfigurationRepository } from '../../../repositories';
import { EncryptionService } from '@shared/services/encryption.service';
import { AdminEmailServerConfigurationEntity } from '@modules/integration/entities/admin_email_server_configurations.entity';
import { CreateEmailServerDto, TestEmailServerDto, UpdateEmailServerDto } from '../../../user/dto';
import { AppException } from '@common/exceptions';
import { INTEGRATION_ERROR_CODES } from '../../../exceptions/integration-error.code';
import { EmailServerQueryDto } from '../../dto';
import * as nodemailer from 'nodemailer';

// Mock nodemailer
jest.mock('nodemailer', () => ({
  createTransport: jest.fn().mockReturnValue({
    verify: jest.fn().mockResolvedValue(true),
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
  }),
}));

describe('AdminEmailServerConfigurationService', () => {
  let service: AdminEmailServerConfigurationService;
  let repository: AdminEmailServerConfigurationRepository;
  let encryptionService: EncryptionService;

  // Mock data
  const mockEmailServer: AdminEmailServerConfigurationEntity = {
    id: 1,
    serverName: 'Test SMTP Server',
    host: 'smtp.test.com',
    port: 587,
    username: '<EMAIL>',
    password: 'encrypted-password',
    useSsl: true,
    additionalSettings: { auth: 'login' },
    createdBy: 1,
    createdAt: Date.now(),
    updatedAt: Date.now(),
  };

  const mockEmailServerList: AdminEmailServerConfigurationEntity[] = [
    mockEmailServer,
    {
      ...mockEmailServer,
      id: 2,
      serverName: 'Another SMTP Server',
      host: 'smtp.another.com',
    },
  ];

  const mockCreateDto: CreateEmailServerDto = {
    serverName: 'New SMTP Server',
    host: 'smtp.new.com',
    port: 587,
    username: '<EMAIL>',
    password: 'new-password',
    useSsl: true,
    additionalSettings: { auth: 'login' },
  };

  const mockUpdateDto: UpdateEmailServerDto = {
    serverName: 'Updated SMTP Server',
    port: 465,
  };

  const mockTestDto: TestEmailServerDto = {
    recipientEmail: '<EMAIL>',
    subject: 'Test Email',
  };

  // Mock repository
  const mockRepository = {
    createQueryBuilder: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    getCount: jest.fn().mockResolvedValue(2),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getMany: jest.fn().mockResolvedValue(mockEmailServerList),
    find: jest.fn().mockResolvedValue(mockEmailServerList),
    findOne: jest.fn().mockResolvedValue(mockEmailServer),
    create: jest.fn().mockReturnValue(mockEmailServer),
    save: jest.fn().mockResolvedValue(mockEmailServer),
    update: jest.fn().mockResolvedValue({ affected: 1 }),
    delete: jest.fn().mockResolvedValue({ affected: 1 }),
  };

  // Mock encryption service
  const mockEncryptionService = {
    encrypt: jest.fn().mockReturnValue('encrypted-password'),
    decrypt: jest.fn().mockReturnValue('decrypted-password'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminEmailServerConfigurationService,
        {
          provide: AdminEmailServerConfigurationRepository,
          useValue: mockRepository,
        },
        {
          provide: EncryptionService,
          useValue: mockEncryptionService,
        },
      ],
    }).compile();

    service = module.get<AdminEmailServerConfigurationService>(AdminEmailServerConfigurationService);
    repository = module.get<AdminEmailServerConfigurationRepository>(AdminEmailServerConfigurationRepository);
    encryptionService = module.get<EncryptionService>(EncryptionService);

    // Reset mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return a paginated list of email servers', async () => {
      const queryDto: EmailServerQueryDto = {
        page: 1,
        limit: 10,
      };

      // Mock queryBuilder methods
      const mockQueryBuilder = {
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(2),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockEmailServerList),
      };
      mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.findAll(queryDto);

      expect(result).toBeDefined();
      expect(result.items).toHaveLength(2);
      expect(result.meta.totalItems).toBe(2);
      expect(repository.createQueryBuilder).toHaveBeenCalledWith('email_server');
      expect(mockQueryBuilder.getMany).toHaveBeenCalled();
    });

    it('should apply search filter if provided', async () => {
      const queryDto: EmailServerQueryDto = {
        page: 1,
        limit: 10,
        search: 'test',
      };

      // Mock queryBuilder methods
      const mockQueryBuilder = {
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(2),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockEmailServerList),
      };
      mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      await service.findAll(queryDto);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(email_server.server_name LIKE :search OR email_server.host LIKE :search)',
        { search: '%test%' }
      );
    });

    it('should apply userId filter if provided', async () => {
      const queryDto: EmailServerQueryDto = {
        page: 1,
        limit: 10,
        userId: 1,
      };

      // Mock queryBuilder methods
      const mockQueryBuilder = {
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(2),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockEmailServerList),
      };
      mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      await service.findAll(queryDto);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('email_server.user_id = :userId', { userId: 1 });
    });

    it('should handle errors and throw AppException', async () => {
      // Mock queryBuilder methods with error
      const mockQueryBuilder = {
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(2),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockRejectedValue(new Error('Database error')),
      };
      mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      await expect(service.findAll({ page: 1, limit: 10 })).rejects.toThrow(AppException);
    });
  });

  describe('findOne', () => {
    it('should return an email server by id', async () => {
      const result = await service.findOne(1);

      expect(result).toBeDefined();
      expect(result.id).toBe(1);
      expect(result.password).toBe('********');
      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: 1 },
        relations: ['user'],
      });
    });

    it('should throw AppException if email server not found', async () => {
      mockRepository.findOne.mockResolvedValueOnce(null);

      await expect(service.findOne(999)).rejects.toThrow(AppException);
      await expect(service.findOne(999)).rejects.toThrow(INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND.message);
    });

    it('should handle errors and throw AppException', async () => {
      mockRepository.findOne.mockRejectedValueOnce(new Error('Database error'));

      await expect(service.findOne(1)).rejects.toThrow(AppException);
    });
  });

  describe('create', () => {
    it('should create a new email server', async () => {
      const result = await service.create(mockCreateDto, 1);

      expect(result).toBeDefined();
      expect(result.serverName).toBe(mockEmailServer.serverName);
      expect(result.password).toBe('********');
      expect(encryptionService.encrypt).toHaveBeenCalledWith(mockCreateDto.password);
      expect(repository.create).toHaveBeenCalled();
      expect(repository.save).toHaveBeenCalled();
    });

    it('should handle errors and throw AppException', async () => {
      mockRepository.save.mockRejectedValueOnce(new Error('Database error'));

      await expect(service.create(mockCreateDto, 1)).rejects.toThrow(AppException);
      await expect(service.create(mockCreateDto, 1)).rejects.toThrow(INTEGRATION_ERROR_CODES.EMAIL_SERVER_CREATE_FAILED.message);
    });
  });

  describe('update', () => {
    it('should update an existing email server', async () => {
      const result = await service.update(1, mockUpdateDto, 1);

      expect(result).toBeDefined();
      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: 1 },
      });
      expect(repository.update).toHaveBeenCalled();
      expect(repository.findOne).toHaveBeenCalledTimes(2);
    });

    it('should encrypt password if provided in update dto', async () => {
      const updateDtoWithPassword = { ...mockUpdateDto, password: 'new-password' };

      await service.update(1, updateDtoWithPassword, 1);

      expect(encryptionService.encrypt).toHaveBeenCalledWith('new-password');
    });

    it('should throw AppException if email server not found', async () => {
      mockRepository.findOne.mockResolvedValueOnce(null);

      await expect(service.update(999, mockUpdateDto, 1)).rejects.toThrow(AppException);
      await expect(service.update(999, mockUpdateDto, 1)).rejects.toThrow(INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND.message);
    });

    it('should handle errors and throw AppException', async () => {
      mockRepository.update.mockRejectedValueOnce(new Error('Database error'));

      await expect(service.update(1, mockUpdateDto, 1)).rejects.toThrow(AppException);
      await expect(service.update(1, mockUpdateDto, 1)).rejects.toThrow(INTEGRATION_ERROR_CODES.EMAIL_SERVER_UPDATE_FAILED.message);
    });
  });

  describe('remove', () => {
    it('should remove an email server', async () => {
      const result = await service.remove(1, 1);

      expect(result).toBeDefined();
      expect(result.message).toBe('Cấu hình máy chủ email đã được xóa');
      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: 1 },
      });
      expect(repository.delete).toHaveBeenCalledWith(1);
    });

    it('should throw AppException if email server not found', async () => {
      mockRepository.findOne.mockResolvedValueOnce(null);

      await expect(service.remove(999, 1)).rejects.toThrow(AppException);
      await expect(service.remove(999, 1)).rejects.toThrow(INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND.message);
    });

    it('should handle errors and throw AppException', async () => {
      mockRepository.delete.mockRejectedValueOnce(new Error('Database error'));

      await expect(service.remove(1, 1)).rejects.toThrow(AppException);
      await expect(service.remove(1, 1)).rejects.toThrow(INTEGRATION_ERROR_CODES.EMAIL_SERVER_DELETE_FAILED.message);
    });
  });

  describe('testConnection', () => {
    it('should test email server connection successfully', async () => {
      const result = await service.testConnection(1, mockTestDto);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBe('Kết nối thành công! Email kiểm tra đã được gửi.');
      expect(nodemailer.createTransport).toHaveBeenCalled();
      expect(encryptionService.decrypt).toHaveBeenCalledWith(mockEmailServer.password);
    });

    it('should handle connection errors', async () => {
      const mockTransport = {
        verify: jest.fn().mockRejectedValueOnce(new Error('Connection error')),
      };
      (nodemailer.createTransport as jest.Mock).mockReturnValueOnce(mockTransport);

      const result = await service.testConnection(1, mockTestDto);

      expect(result).toBeDefined();
      expect(result.success).toBe(false);
      expect(result.message).toBe('Kết nối thất bại!');
      expect(result.details).toBe('Connection error');
    });

    it('should throw AppException if email server not found', async () => {
      mockRepository.findOne.mockResolvedValueOnce(null);

      await expect(service.testConnection(999, mockTestDto)).rejects.toThrow(AppException);
      await expect(service.testConnection(999, mockTestDto)).rejects.toThrow(INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND.message);
    });
  });
});
