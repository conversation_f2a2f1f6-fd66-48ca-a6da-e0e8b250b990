import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import { Plus, Search, Filter } from 'lucide-react';
import { Card } from '@/shared/components/common';
import { Button } from '@/shared/components/common';
import { Input } from '@/shared/components/common';

import { Modal } from '@/shared/components/common';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import { useZaloAccounts } from '../../hooks/zalo/useZaloAccounts';
import { ConnectZaloAccountForm } from '../../components/zalo/ConnectZaloAccountForm';
import { Pagination } from '@/shared/components/common';
import type { ZaloOAAccountQueryDto } from '../../types/zalo.types';

/**
 * Trang quản lý Zalo OA Accounts
 */
export function ZaloAccountsPage() {
  const { t } = useTranslation('marketing');
  const [searchParams, setSearchParams] = useSearchParams();
  const [query, setQuery] = useState<ZaloOAAccountQueryDto>({
    page: 1,
    limit: 10,
    search: '',
  });
  const [showConnectDialog, setShowConnectDialog] = useState(
    searchParams.get('action') === 'connect'
  );

  const { data: accountsData, isLoading } = useZaloAccounts(query);

  const handleSearch = (value: string) => {
    setQuery(prev => ({ ...prev, search: value, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setQuery(prev => ({ ...prev, page }));
  };

  const handleConnectSuccess = () => {
    setShowConnectDialog(false);
    setSearchParams({});
  };

  // Removed unused handler functions - will be implemented when needed

  return (
    <div className="space-y-6">
      {/* Header */}
      <MarketingViewHeader
        title={t('zalo.accounts.title', 'Quản lý Zalo OA')}
        description={t('zalo.accounts.description', 'Kết nối và quản lý các Zalo Official Account')}
        actions={
          <Button onClick={() => setShowConnectDialog(true)} className="gap-2">
            <Plus className="h-4 w-4" />
            {t('zalo.accounts.connectNew', 'Kết nối OA mới')}
          </Button>
        }
      />

      {/* Filters */}
      <Card
        title={t('zalo.accounts.filters.title', 'Bộ lọc')}
      >
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('zalo.accounts.searchPlaceholder', 'Tìm kiếm theo tên OA...')}
                value={query.search || ''}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            {t('zalo.accounts.filters.advanced', 'Bộ lọc nâng cao')}
          </Button>
        </div>
      </Card>

      {/* Accounts Table */}
      <Card
        title={t('zalo.accounts.list.title', 'Danh sách tài khoản')}
        subtitle={
          accountsData?.meta.totalItems
            ? t('zalo.accounts.list.description', 'Tổng cộng {{count}} tài khoản', { count: accountsData.meta.totalItems })
            : t('zalo.accounts.list.noData', 'Chưa có tài khoản nào')
        }
      >
          <div className="rounded-md border">
            <div className="overflow-x-auto">
              {/* Table Header */}
              <div className="grid grid-cols-6 gap-4 p-4 bg-muted/50 border-b font-medium text-sm">
                <div>{t('zalo.accounts.table.name', 'Tên OA')}</div>
                <div>{t('zalo.accounts.table.oaId', 'OA ID')}</div>
                <div>{t('zalo.accounts.table.followers', 'Followers')}</div>
                <div>{t('zalo.accounts.table.status', 'Trạng thái')}</div>
                <div>{t('zalo.accounts.table.lastUpdate', 'Cập nhật cuối')}</div>
                <div className="w-[100px]">{t('zalo.accounts.table.actions', 'Thao tác')}</div>
              </div>

              {/* Table Body */}
              <div>
                {isLoading ? (
                  [...Array(5)].map((_, i) => (
                    <div key={i} className="grid grid-cols-6 gap-4 p-4 border-b">
                      <div className="flex items-center space-x-3">
                        <div className="h-8 w-8 rounded-full bg-muted animate-pulse" />
                        <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                      </div>
                      <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                      <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                      <div className="h-6 w-20 bg-muted animate-pulse rounded" />
                      <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                      <div className="h-8 w-8 bg-muted animate-pulse rounded" />
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Table content will be implemented</p>
                  </div>
                )}
              </div>
            </div>
          </div>

        {/* Pagination */}
        {accountsData && accountsData.meta.totalPages > 1 && (
          <div className="mt-4">
            <Pagination
              currentPage={accountsData.meta.currentPage}
              totalPages={accountsData.meta.totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </Card>

      {/* Connect Account Dialog */}
      <Modal
        isOpen={showConnectDialog}
        onClose={() => setShowConnectDialog(false)}
        title={t('zalo.accounts.connect.title', 'Kết nối Zalo OA')}
        size="md"
      >
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            {t('zalo.accounts.connect.description', 'Nhập thông tin để kết nối Zalo Official Account')}
          </p>
          <ConnectZaloAccountForm onSuccess={handleConnectSuccess} />
        </div>
      </Modal>
    </div>
  );
}

export default ZaloAccountsPage;
