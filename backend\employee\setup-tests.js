// Đặt NODE_ENV là 'test' để sử dụng file .env.test
process.env.NODE_ENV = 'test';

// Đặt các biến môi trường cần thiết cho các bài test
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '5432';
process.env.DB_USERNAME = 'test';
process.env.DB_PASSWORD = 'test';
process.env.DB_DATABASE = 'test_db';

process.env.CF_R2_ACCESS_KEY = 'mock_access_key';
process.env.CF_R2_SECRET_KEY = 'mock_secret_key';
process.env.CF_R2_ENDPOINT = 'https://mock-endpoint.com';
process.env.CF_BUCKET_NAME = 'mock-bucket';

process.env.OPENAI_API_KEY = 'mock_openai_key';

process.env.CDN_URL = 'https://mock-cdn.com';
process.env.CDN_SECRET_KEY = 'mock_cdn_key';

process.env.JWT_SECRET = 'mock_jwt_secret';
process.env.JWT_EXPIRES_IN = '1h';
process.env.JWT_REFRESH_EXPIRES_IN = '7d';
