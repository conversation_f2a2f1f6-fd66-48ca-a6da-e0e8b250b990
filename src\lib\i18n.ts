import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import enTranslation from '../locales/en.json';
import viTranslation from '../locales/vi.json';
import zhTranslation from '../locales/zh.json';

// Import module translations
import businessResources from '../modules/business/locales';
import authResources from '../modules/auth/locales';
import adminAuthResources from '../modules/admin/auth/locales';
import profileResources from '../modules/profile/locales';
import componentsResources from '../modules/components/locales';
import marketingResources from '../modules/marketing/locales';
import integrationResources from '../modules/integration/locales';
import { subscriptionResources } from '../modules/subscription/locales';
import marketplaceResources from '../modules/marketplace/locales';
import employeeResources from '../modules/admin/employee/locales';
import genericPageResources from '../modules/generic-page/locales';
import { rpointAdminResources as rpointAdminResources } from '@/modules/admin/r-point/locales';
import affiliateResources from '@/modules/admin/affiliate/locales';
import userResources from '@/modules/admin/user/locales';
import dataResources from '@/modules/admin/data/locales';
import moduleDataResources from '@/modules/data/locales';
import marketplaceAdminResources from '@/modules/admin/marketplace/locales';
import marketingAdminResources from '@/modules/admin/marketing/locales';
import blogResources from '@/modules/blog/locales';
import modelTrainingResources from '@/modules/model-training/locales';
import adminToolResources from '@/modules/admin/tool/locales';
import toolsResources from '@/modules/tools/locales';
import settingsResources from '@/modules/settings/locales';
import adminEnTranslation from './i18n/locales/en/admin.json';
import adminViTranslation from './i18n/locales/vi/admin.json';
import adminZhTranslation from './i18n/locales/zh/admin.json';
import workflowsEnTranslation from './i18n/locales/en/workflows.json';
import workflowsViTranslation from './i18n/locales/vi/workflows.json';
import workflowsZhTranslation from './i18n/locales/zh/workflows.json';
import integrationsEnTranslation from './i18n/locales/en/integrations.json';
import integrationsViTranslation from './i18n/locales/vi/integrations.json';
import integrationsZhTranslation from './i18n/locales/zh/integrations.json';
import paymentEnTranslation from './i18n/locales/en/payment.json';
import paymentViTranslation from './i18n/locales/vi/payment.json';
import paymentZhTranslation from './i18n/locales/zh/payment.json';
import commonEnTranslation from './i18n/locales/en/common.json';
import commonViTranslation from './i18n/locales/vi/common.json';
import commonZhTranslation from './i18n/locales/zh/common.json';
import paginationEnTranslation from './i18n/locales/en/pagination.json';
import paginationViTranslation from './i18n/locales/vi/pagination.json';
import paginationZhTranslation from './i18n/locales/zh/pagination.json';

export const resources = {
  en: {
    translation: enTranslation,
    business: businessResources.en.business,
    auth: authResources.en.auth,
    adminAuth: adminAuthResources.en.adminAuth,
    adminValidation: adminAuthResources.en.validation,
    profile: profileResources.en.profile,
    validation: profileResources.en.validation,
    components: componentsResources.en.components,
    marketing: marketingResources.en.marketing,
    integration: integrationResources.en.integration,
    subscription: subscriptionResources.en.subscription,
    marketplace: marketplaceResources.en.marketplace,
    employee: employeeResources.en.employee,
    genericPage: genericPageResources.en.genericPage,
    rpointAdmin: rpointAdminResources.en.rpoint,
    affiliate: affiliateResources.en.affiliate,
    user: userResources.en.user,
    data: moduleDataResources.en.data,
    blog: blogResources.en,
    modelTraining: modelTrainingResources.en,
    tools: toolsResources.en.tools,
    settings: settingsResources.en.settings,
    admin: {
      ...adminEnTranslation,
      data: dataResources.en.data,
      marketplace: marketplaceAdminResources.en.marketplace,
      marketing: marketingAdminResources.en.marketingAdmin,
      tool: adminToolResources.en.adminTool,
    },
    workflows: workflowsEnTranslation,
    integrations: integrationsEnTranslation,
    payment: paymentEnTranslation,
    common: commonEnTranslation,
    pagination: paginationEnTranslation,
  },
  vi: {
    translation: viTranslation,
    business: businessResources.vi.business,
    auth: authResources.vi.auth,
    adminAuth: adminAuthResources.vi.adminAuth,
    adminValidation: adminAuthResources.vi.validation,
    profile: profileResources.vi.profile,
    validation: profileResources.vi.validation,
    components: componentsResources.vi.components,
    marketing: marketingResources.vi.marketing,
    integration: integrationResources.vi.integration,
    subscription: subscriptionResources.vi.subscription,
    marketplace: marketplaceResources.vi.marketplace,
    employee: employeeResources.vi.employee,
    genericPage: genericPageResources.vi.genericPage,
    rpointAdmin: rpointAdminResources.vi.rpoint,
    affiliate: affiliateResources.vi.affiliate,
    user: userResources.vi.user,
    data: moduleDataResources.vi.data,
    blog: blogResources.vi,
    modelTraining: modelTrainingResources.vi,
    tools: toolsResources.vi.tools,
    settings: settingsResources.vi.settings,
    admin: {
      ...adminViTranslation,
      data: dataResources.vi.data,
      marketplace: marketplaceAdminResources.vi.marketplace,
      marketing: marketingAdminResources.vi.marketingAdmin,
      tool: adminToolResources.vi.adminTool,
    },
    workflows: workflowsViTranslation,
    integrations: integrationsViTranslation,
    payment: paymentViTranslation,
    common: commonViTranslation,
    pagination: paginationViTranslation,
  },
  zh: {
    translation: zhTranslation,
    business: businessResources.zh.business,
    auth: authResources.zh.auth,
    adminAuth: adminAuthResources.zh.adminAuth,
    adminValidation: adminAuthResources.zh.validation,
    profile: profileResources.zh.profile,
    validation: profileResources.zh.validation,
    components: componentsResources.zh.components,
    marketing: marketingResources.zh.marketing,
    integration: integrationResources.zh.integration,
    subscription: subscriptionResources.zh.subscription,
    marketplace: marketplaceResources.zh.marketplace,
    genericPage: genericPageResources.zh.genericPage,
    employee: employeeResources.zh.employee,
    rpointAdmin: rpointAdminResources.zh.rpoint,
    affiliate: affiliateResources.zh.affiliate,
    user: userResources.zh.user,
    data: moduleDataResources.zh.data,
    blog: blogResources.zh,
    modelTraining: modelTrainingResources.zh,
    tools: toolsResources.zh.tools,
    settings: settingsResources.zh.settings,
    admin: {
      ...adminZhTranslation,
      data: dataResources.zh.data,
      marketplace: marketplaceAdminResources.zh.marketplace,
      marketing: marketingAdminResources.zh.marketingAdmin,
      tool: adminToolResources.zh.adminTool,
    },
    workflows: workflowsZhTranslation,
    integrations: integrationsZhTranslation,
    payment: paymentZhTranslation,
    common: commonZhTranslation,
    pagination: paginationZhTranslation,
  },
};

export const availableLanguages = [
  { code: 'vi', name: 'Tiếng Việt' },
  { code: 'en', name: 'English' },
  { code: 'zh', name: '中文' },
];

i18n.use(initReactI18next).init({
  resources,
  lng: 'vi', // Default language
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false, // React already escapes values
  },
});

export default i18n;
