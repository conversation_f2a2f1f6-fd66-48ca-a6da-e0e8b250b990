import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEmail, IsOptional, IsString, MinLength } from 'class-validator';

/**
 * DTO cho việc cập nhật thông tin nhân viên
 */
export class UpdateEmployeeDto {
  /**
   * Tên đầy đủ của nhân viên
   */
  @ApiProperty({
    description: 'Tên đầy đủ của nhân viên',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên đầy đủ phải là chuỗi' })
  fullName?: string;

  /**
   * Email nhân viên
   */
  @ApiProperty({
    description: 'Email nhân viên',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email?: string;

  /**
   * <PERSON><PERSON> điện thoại nhân viên
   */
  @ApiProperty({
    description: '<PERSON><PERSON> điện thoại nhân viên',
    example: '0912345678',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  phoneNumber?: string;

  /**
   * Mật khẩu nhân viên
   */
  @ApiProperty({
    description: 'Mật khẩu nhân viên',
    example: 'password123',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mật khẩu phải là chuỗi' })
  @MinLength(6, { message: 'Mật khẩu phải có ít nhất 6 ký tự' })
  password?: string;

  /**
   * Địa chỉ nhân viên
   */
  @ApiProperty({
    description: 'Địa chỉ nhân viên',
    example: 'Số 1, Đường ABC, Quận XYZ, Hà Nội',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Địa chỉ phải là chuỗi' })
  address?: string;

  /**
   * Trạng thái hoạt động của tài khoản
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động của tài khoản',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trạng thái phải là boolean' })
  enable?: boolean;

  /**
   * Danh sách ID của các role
   */
  @ApiProperty({
    description: 'Danh sách ID của các role',
    example: [1, 2],
    type: [Number],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Danh sách role phải là mảng' })
  roleIds?: number[];
}
