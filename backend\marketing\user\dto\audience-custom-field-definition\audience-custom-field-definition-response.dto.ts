import { ApiProperty } from '@nestjs/swagger';
import { CustomFieldDataType } from './create-audience-custom-field-definition.dto';

/**
 * DTO cho phản hồi thông tin trường tùy chỉnh
 */
export class AudienceCustomFieldDefinitionResponseDto {
  /**
   * ID của trường tùy chỉnh
   * @example 1
   */
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 1,
  })
  id: number;

  /**
   * Định danh duy nhất cho trường tùy chỉnh
   * @example "customer_address"
   */
  @ApiProperty({
    description: 'Định danh duy nhất cho trường tùy chỉnh',
    example: 'customer_address',
  })
  fieldKey: string;

  /**
   * ID của người dùng mà trường tùy chỉnh này thuộc về
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người dùng mà trường tùy chỉnh này thuộc về',
    example: 1,
  })
  userId: number;

  /**
   * Tên hiển thị thân thiện với người dùng
   * @example "Địa chỉ khách hàng"
   */
  @ApiProperty({
    description: 'Tên hiển thị thân thiện với người dùng',
    example: 'Địa chỉ khách hàng',
  })
  displayName: string;

  /**
   * Kiểu dữ liệu
   * @example "string"
   */
  @ApiProperty({
    description: 'Kiểu dữ liệu',
    enum: CustomFieldDataType,
    example: CustomFieldDataType.STRING,
  })
  dataType: CustomFieldDataType;

  /**
   * Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh
   * @example "Địa chỉ liên hệ của khách hàng"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh',
    example: 'Địa chỉ liên hệ của khách hàng',
    nullable: true,
  })
  description: string | null;
}
