import { Loading } from "@/shared";
import MainLayout from "@/shared/layouts/MainLayout";
import { Suspense, lazy } from "react";
import { RouteObject } from "react-router-dom";

// Lazy load AI pages
const AIAgentsPage = lazy(() => import("../pages/AIAgentsPage"));
const AgentDetailPage = lazy(() => import("../pages/AgentDetailPage"));
const AgentEditPage = lazy(() => import("../pages/AgentEditPage"));
const AgentCategoriesPage = lazy(() => import("../pages/AgentCategoriesPage"));
const AgentCreatePage = lazy(() => import("../pages/AgentCreatePage"));


const agentRoutes: RouteObject[] = [
  {
    path: '/ai-agents',
    element: (
      <MainLayout title="AI Agents">
        <Suspense fallback={<Loading />}>
          <AIAgentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/ai-agents/:id',
    element: (
      <MainLayout title="AI Agent Details">
        <Suspense fallback={<Loading />}>
          <AgentDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/ai-agents/:id/edit',
    element: (
      <MainLayout title="Edit AI Agent">
        <Suspense fallback={<Loading />}>
          <AgentEditPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/ai-agents/categories',
    element: (
      <MainLayout title="AI Agent Categories">
        <Suspense fallback={<Loading />}>
          <AgentCategoriesPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/ai-agents/add',
    element: (
      <MainLayout title="Create AI Agent">
        <Suspense fallback={<Loading />}>
          <AgentCreatePage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default agentRoutes;
