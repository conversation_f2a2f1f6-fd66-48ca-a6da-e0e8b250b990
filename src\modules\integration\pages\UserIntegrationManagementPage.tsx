import React from 'react';
import { useTranslation } from 'react-i18next';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Trang tổng quan quản lý tích hợp cho Admin
 */
const UserIntegrationManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Email Server Card */}
        <ModuleCard
          title={t('admin:integration.email.title', 'Quản lý Email ')}
          description={t(
            'admin:integration.email.description',
            'Quản lý cấu hình m<PERSON> chủ email cho hệ thống gửi email tự động'
          )}
          icon="mail"
          linkTo="/integrations/email"
        />
        {/* Facebook Card */}
        <ModuleCard
          title={t('admin:integration.facebook.title', 'Quản lý Facebook')}
          description={t(
            'admin:integration.facebook.description',
            'Quản lý tích hợp với Facebook'
          )}
          icon="facebook"
          linkTo="/integrations/facebook"
        />
        {/* Website Card */}
        <ModuleCard
          title={t('admin:integration.website.title', 'Quản lý Website')}
          description={t(
            'admin:integration.website.description',
            'Quản lý tích hợp với các trang web'
          )}
          icon="globe"
          linkTo="/integrations/website"
        />
        
      </ResponsiveGrid>
    </div>
  );
};

export default UserIntegrationManagementPage;
