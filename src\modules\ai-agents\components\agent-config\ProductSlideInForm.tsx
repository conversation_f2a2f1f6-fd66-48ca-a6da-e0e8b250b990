import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  Button,
  Card,
  EmptyState,
  Icon,
  Pagination,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Interface cho item Product
 */
interface Product {
  id: string;
  name: string;
  sku: string;
  price: number;
  salePrice?: number;
  imageUrl?: string;
  category?: string;
  stock: number;
  status: 'active' | 'inactive' | 'out_of_stock';
  createdAt: string;
}

/**
 * Props cho component ProductSlideInForm
 */
interface ProductSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi chọn các sản phẩm
   */
  onSelect: (selectedProducts: Product[]) => void;

  /**
   * <PERSON>h sách ID của các sản phẩm đã chọn
   */
  selectedProductIds?: string[];
}

/**
 * Component form trượt để chọn các sản phẩm
 */
const ProductSlideInForm: React.FC<ProductSlideInFormProps> = ({
  isVisible,
  onClose,
  onSelect,
  selectedProductIds = [],
}) => {
  // State cho dữ liệu và UI
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedIds, setSelectedIds] = useState<string[]>(selectedProductIds);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // State cho phân trang
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);

  // State cho sắp xếp và lọc
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'ASC' | 'DESC'>('ASC');
  const [filterCategory, setFilterCategory] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('');

  // Cấu hình cột cho bảng
  const columns: TableColumn<Product>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'product',
      title: 'Sản phẩm',
      dataIndex: 'name',
      width: '40%',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="w-12 h-12 rounded-md bg-gray-100 flex items-center justify-center mr-3 overflow-hidden">
            {record.imageUrl ? (
              <img
                src={record.imageUrl}
                alt={record.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <Icon name="box" size="md" className="text-gray-500" />
            )}
          </div>
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              SKU: {record.sku}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'price',
      title: 'Giá',
      dataIndex: 'price',
      width: '15%',
      render: (_, record) => (
        <div>
          {record.salePrice ? (
            <>
              <Typography variant="body2" className="text-red-500 font-medium">
                {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(record.salePrice)}
              </Typography>
              <Typography variant="caption" className="text-gray-500 line-through">
                {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(record.price)}
              </Typography>
            </>
          ) : (
            <Typography variant="body2" className="font-medium">
              {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(record.price)}
            </Typography>
          )}
        </div>
      ),
    },
    {
      key: 'category',
      title: 'Danh mục',
      dataIndex: 'category',
      width: '15%',
    },
    {
      key: 'stock',
      title: 'Tồn kho',
      dataIndex: 'stock',
      width: '15%',
      render: (_, record) => (
        <Typography
          variant="body2"
          className={
            record.stock > 10
              ? 'text-green-500'
              : record.stock > 0
              ? 'text-yellow-500'
              : 'text-red-500'
          }
        >
          {record.stock}
        </Typography>
      ),
    },
    {
      key: 'status',
      title: 'Trạng thái',
      dataIndex: 'status',
      width: '15%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.status === 'active' ? (
            <span className="text-green-500 text-sm flex items-center">
              <Icon name="check-circle" size="sm" className="mr-1" />
              Đang bán
            </span>
          ) : record.status === 'inactive' ? (
            <span className="text-gray-500 text-sm flex items-center">
              <Icon name="x-circle" size="sm" className="mr-1" />
              Ngừng bán
            </span>
          ) : (
            <span className="text-red-500 text-sm flex items-center">
              <Icon name="alert-circle" size="sm" className="mr-1" />
              Hết hàng
            </span>
          )}
        </div>
      ),
    },
  ];

  // Giả lập dữ liệu sản phẩm - sử dụng useMemo để tránh re-render không cần thiết
  const mockProducts = useMemo<Product[]>(() => [
    { id: 'product-1', name: 'Điện thoại iPhone 13 Pro Max', sku: 'IP13PM-256', price: 30990000, salePrice: 28990000, imageUrl: 'https://via.placeholder.com/150x150', category: 'Điện thoại', stock: 15, status: 'active', createdAt: '2023-05-15' },
    { id: 'product-2', name: 'Laptop MacBook Pro M1', sku: 'MBP-M1-512', price: 35990000, imageUrl: 'https://via.placeholder.com/150x150', category: 'Laptop', stock: 8, status: 'active', createdAt: '2023-06-20' },
    { id: 'product-3', name: 'Tai nghe AirPods Pro', sku: 'APP-2', price: 5990000, salePrice: 4990000, imageUrl: 'https://via.placeholder.com/150x150', category: 'Phụ kiện', stock: 25, status: 'active', createdAt: '2023-07-05' },
    { id: 'product-4', name: 'iPad Air 5', sku: 'IPA5-64', price: 15990000, imageUrl: 'https://via.placeholder.com/150x150', category: 'Máy tính bảng', stock: 0, status: 'out_of_stock', createdAt: '2023-08-10' },
    { id: 'product-5', name: 'Apple Watch Series 7', sku: 'AWS7-41', price: 10990000, salePrice: 9990000, imageUrl: 'https://via.placeholder.com/150x150', category: 'Đồng hồ thông minh', stock: 12, status: 'active', createdAt: '2023-04-25' },
    { id: 'product-6', name: 'Sạc nhanh 20W', sku: 'AC-20W', price: 590000, imageUrl: 'https://via.placeholder.com/150x150', category: 'Phụ kiện', stock: 50, status: 'active', createdAt: '2023-01-15' },
    { id: 'product-7', name: 'Ốp lưng iPhone 13', sku: 'CASE-IP13', price: 390000, imageUrl: 'https://via.placeholder.com/150x150', category: 'Phụ kiện', stock: 30, status: 'active', createdAt: '2023-09-05' },
    { id: 'product-8', name: 'Chuột Magic Mouse', sku: 'MM-3', price: 2490000, imageUrl: 'https://via.placeholder.com/150x150', category: 'Phụ kiện', stock: 5, status: 'active', createdAt: '2023-09-06' },
    { id: 'product-9', name: 'Bàn phím Magic Keyboard', sku: 'MK-2', price: 3290000, imageUrl: 'https://via.placeholder.com/150x150', category: 'Phụ kiện', stock: 0, status: 'out_of_stock', createdAt: '2023-09-07' },
    { id: 'product-10', name: 'iMac M1', sku: 'IMAC-M1-256', price: 32990000, salePrice: 30990000, imageUrl: 'https://via.placeholder.com/150x150', category: 'Máy tính để bàn', stock: 3, status: 'active', createdAt: '2023-10-01' },
    { id: 'product-11', name: 'Mac Mini M1', sku: 'MM-M1-256', price: 18990000, imageUrl: 'https://via.placeholder.com/150x150', category: 'Máy tính để bàn', stock: 7, status: 'active', createdAt: '2023-10-02' },
    { id: 'product-12', name: 'Samsung Galaxy S22 Ultra', sku: 'SGS22U-256', price: 25990000, salePrice: 23990000, imageUrl: 'https://via.placeholder.com/150x150', category: 'Điện thoại', stock: 10, status: 'active', createdAt: '2023-10-03' },
  ], []);

  // Giả lập việc tải dữ liệu
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Giả lập API call
        await new Promise(resolve => setTimeout(resolve, 500));

        // Lọc dữ liệu theo tìm kiếm, danh mục và trạng thái
        let filteredData = [...mockProducts];

        if (search) {
          filteredData = filteredData.filter(product =>
            product.name.toLowerCase().includes(search.toLowerCase()) ||
            product.sku.toLowerCase().includes(search.toLowerCase())
          );
        }

        if (filterCategory) {
          filteredData = filteredData.filter(product => product.category === filterCategory);
        }

        if (filterStatus) {
          filteredData = filteredData.filter(product => product.status === filterStatus);
        }

        // Sắp xếp dữ liệu
        filteredData.sort((a, b) => {
          if (sortBy === 'name') {
            return sortDirection === 'ASC'
              ? a.name.localeCompare(b.name)
              : b.name.localeCompare(a.name);
          } else if (sortBy === 'price') {
            const aPrice = a.salePrice || a.price;
            const bPrice = b.salePrice || b.price;
            return sortDirection === 'ASC' ? aPrice - bPrice : bPrice - aPrice;
          } else if (sortBy === 'stock') {
            return sortDirection === 'ASC' ? a.stock - b.stock : b.stock - a.stock;
          } else if (sortBy === 'createdAt') {
            return sortDirection === 'ASC'
              ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
              : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
          }
          return 0;
        });

        // Phân trang
        const startIndex = (currentPage - 1) * itemsPerPage;
        const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage);

        setProducts(paginatedData);
        setTotalItems(filteredData.length);
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, currentPage, itemsPerPage, sortBy, sortDirection, filterCategory, filterStatus]);

  // Kiểm tra có thay đổi chưa lưu không
  useEffect(() => {
    const hasUnsavedChanges =
      selectedIds.length !== selectedProductIds.length ||
      selectedIds.some(id => !selectedProductIds.includes(id)) ||
      selectedProductIds.some(id => !selectedIds.includes(id));

    setHasChanges(hasUnsavedChanges);
  }, [selectedIds, selectedProductIds]);

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setSearch(term);
    setCurrentPage(1);
  };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = (column: string, direction: 'ASC' | 'DESC') => {
    setSortBy(column);
    setSortDirection(direction);
  };

  // Xử lý lưu
  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      // Giả lập API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Lấy thông tin đầy đủ của các sản phẩm đã chọn
      const selectedProducts = mockProducts.filter(product =>
        selectedIds.includes(product.id)
      );

      onSelect(selectedProducts);
      onClose();
    } catch (error) {
      console.error('Error saving selected products:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    if (hasChanges) {
      const confirmed = window.confirm(
        'Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn đóng form?'
      );
      if (!confirmed) return;
    }

    setSearch('');
    onClose();
  }, [hasChanges, onClose]);

  // Lấy danh sách danh mục duy nhất
  const categories = useMemo(() => {
    const uniqueCategories = [...new Set(mockProducts.map(product => product.category))];
    return uniqueCategories.filter(Boolean) as string[];
  }, [mockProducts]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'sort',
      label: 'Sắp xếp theo',
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-name',
      label: 'Tên',
      onClick: () => handleSortChange('name', sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-price',
      label: 'Giá',
      onClick: () => handleSortChange('price', sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-stock',
      label: 'Tồn kho',
      onClick: () => handleSortChange('stock', sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-date',
      label: 'Ngày tạo',
      onClick: () => handleSortChange('createdAt', sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'divider-1',
      divider: true,
    },
    {
      id: 'filter-category',
      label: 'Danh mục',
      icon: 'folder',
      onClick: () => { },
    },
    {
      id: 'category-all',
      label: 'Tất cả',
      onClick: () => setFilterCategory(''),
    },
    ...categories.map(category => ({
      id: `category-${category.toLowerCase().replace(/\s+/g, '-')}`,
      label: category,
      onClick: () => setFilterCategory(category),
    })),
    {
      id: 'divider-2',
      divider: true,
    },
    {
      id: 'filter-status',
      label: 'Trạng thái',
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'status-all',
      label: 'Tất cả',
      onClick: () => setFilterStatus(''),
    },
    {
      id: 'status-active',
      label: 'Đang bán',
      onClick: () => setFilterStatus('active'),
    },
    {
      id: 'status-inactive',
      label: 'Ngừng bán',
      onClick: () => setFilterStatus('inactive'),
    },
    {
      id: 'status-out-of-stock',
      label: 'Hết hàng',
      onClick: () => setFilterStatus('out_of_stock'),
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-6xl">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">Chọn sản phẩm</Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            Đóng
          </Button>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Bảng dữ liệu */}
        <div className="mb-4 w-full overflow-x-auto">
          <Table
            data={products}
            columns={columns}
            loading={isLoading}
            rowKey="id"
            size="lg"
            hoverable
            bordered={false}
            selectable={true}
            className="w-full table-fixed"
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
          />

          {/* Hiển thị khi không có dữ liệu */}
          {!isLoading && products.length === 0 && (
            <EmptyState
              icon="search"
              title="Không có kết quả"
              description="Không tìm thấy sản phẩm nào phù hợp với tìm kiếm của bạn."
              className="py-8"
            />
          )}
        </div>

        {/* Phân trang */}
        <div className="mb-4">
          <Pagination
            currentPage={currentPage}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPageOptions={[5, 10, 20, 50]}
            showItemsPerPageSelector={true}
            showPageInfo={true}
          />
        </div>

        {/* Nút lưu */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            className="mr-2"
            disabled={isSubmitting}
          >
            Hủy
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            isLoading={isSubmitting}
            disabled={isLoading || !hasChanges}
          >
            Lưu
          </Button>
        </div>
      </Card>
    </SlideInForm>
  );
};

export default ProductSlideInForm;
