import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  IconCard,
  Tooltip,
  Typography,
  Icon,
  Loading,
  Alert,
  Button
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useQuery } from '@tanstack/react-query';
import { MarketplaceApiService, ApiPurchaseHistoryItem } from '../services/marketplace-api.service';
import { PRODUCT_QUERY_KEYS } from '../constants/product-query-keys';
import { formatPrice } from '../utils/price-formatter';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Trang quản lý sản phẩm đã mua
 */
const PurchasedProductsPage: React.FC = () => {
  const { t } = useTranslation(['marketplace', 'common']);

  // State cho filter
  const [searchTerm, setSearchTerm] = useState('');
  // const [filter] = useState('all'); // setFilter unused for now
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  // Lấy lịch sử mua hàng từ API
  const {
    data: purchaseHistoryData,
    isLoading,
    error,
    refetch
  } = useQuery<PaginatedResult<ApiPurchaseHistoryItem>>({
    queryKey: [...PRODUCT_QUERY_KEYS.PURCHASE_HISTORY, { page, limit }],
    queryFn: () => MarketplaceApiService.getPurchaseHistory({ page, limit }),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Log dữ liệu khi có thay đổi
  React.useEffect(() => {
    if (purchaseHistoryData) {
      console.log('🎉 [PurchasedProductsPage] Data received:', purchaseHistoryData);
    }
    if (error) {
      console.error('💥 [PurchasedProductsPage] Query error:', error);
    }
  }, [purchaseHistoryData, error]);

  // Columns cho bảng
  const columns: TableColumn<ApiPurchaseHistoryItem>[] = [
    { key: 'orderLineId', title: 'ID', dataIndex: 'orderLineId', width: '8%' },
    {
      key: 'productName',
      title: t('marketplace:purchasedProducts.table.product', 'Sản phẩm'),
      dataIndex: 'productName',
      width: '25%',
      render: (_: unknown, record: ApiPurchaseHistoryItem) => (
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 rounded overflow-hidden bg-gray-100">
            <div className="w-full h-full flex items-center justify-center">
              <Icon name="image" size="sm" className="text-gray-400" />
            </div>
          </div>
          <div>
            <div className="font-medium">{record.productName || t('marketplace:purchasedProducts.noName', 'Không có tên')}</div>
            <div className="text-sm text-gray-500">ID: {record.productId || 'N/A'}</div>
          </div>
        </div>
      )
    },
    {
      key: 'sellerName',
      title: t('marketplace:purchasedProducts.table.seller', 'Người bán'),
      dataIndex: 'sellerName',
      width: '15%',
      render: (_: unknown, record: ApiPurchaseHistoryItem) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-2">
            <Icon name="user" size="sm" className="text-gray-500" />
          </div>
          <span>{record.sellerName || 'Không rõ'}</span>
        </div>
      )
    },
    {
      key: 'createdAt',
      title: 'Ngày mua',
      dataIndex: 'createdAt',
      width: '12%',
      render: (value: unknown) => {
        if (!value) return '-';
        try {
          return new Date(value as string).toLocaleDateString('vi-VN');
        } catch {
          console.warn('Invalid date value:', value);
          return '-';
        }
      }
    },
    {
      key: 'quantity',
      title: 'Số lượng',
      dataIndex: 'quantity',
      width: '8%',
      render: (value: unknown) => (
        <span className="font-medium">{String(value || 0)}</span>
      )
    },
    {
      key: 'discountedPrice',
      title: 'Giá mua',
      dataIndex: 'discountedPrice',
      width: '12%',
      render: (value: unknown) => (
        <div className="flex items-center">
          <span className="mr-1">{formatPrice((value as number) || 0)}</span>
          <Icon name="rpoint" size="sm" className="text-red-600" />
        </div>
      )
    },
    {
      key: 'totalPrice',
      title: 'Tổng tiền',
      dataIndex: 'totalPrice',
      width: '12%',
      render: (_: unknown, record: ApiPurchaseHistoryItem) => {
        const quantity = record.quantity || 0;
        const price = record.discountedPrice || 0;
        const total = quantity * price;
        return (
          <div className="flex items-center font-medium text-green-600">
            <span className="mr-1">{formatPrice(total)}</span>
            <Icon name="rpoint" size="sm" className="text-red-600" />
          </div>
        );
      }
    },
    {
      key: 'actions',
      title: 'Thao tác',
      width: '10%',
      render: (_: unknown, record: ApiPurchaseHistoryItem) => (
        <div className="flex space-x-2">
          <Tooltip content="Xem chi tiết" position="top">
            <IconCard
              icon="eye"
              variant="default"
              size="sm"
              onClick={() => console.log('View product', record.productId)}
            />
          </Tooltip>
          <Tooltip content="Tải xuống" position="top">
            <IconCard
              icon="download"
              variant="default"
              size="sm"
              onClick={() => console.log('Download product', record.productId)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  // Lọc dữ liệu
  const filteredData = useMemo(() => {
    const purchaseItems = purchaseHistoryData?.items || [];

    // Kiểm tra và log dữ liệu để debug
    console.log('Purchase history data:', purchaseHistoryData);
    console.log('Purchase items:', purchaseItems);

    // Lọc và validate dữ liệu
    return purchaseItems
      .filter((item: ApiPurchaseHistoryItem) => {
        // Kiểm tra item có đầy đủ dữ liệu cần thiết không
        if (!item || typeof item !== 'object') {
          console.warn('Invalid purchase item:', item);
          return false;
        }

        // Kiểm tra orderLineId (rowKey) có tồn tại không
        if (item.orderLineId == null) {
          console.warn('Purchase item missing orderLineId:', item);
          return false;
        }

        const matchesSearch =
          (item.productName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
          (item.sellerName || '').toLowerCase().includes(searchTerm.toLowerCase());
        // Note: API không có status field cho purchase history, nên chỉ filter theo search
        return matchesSearch;
      });
  }, [purchaseHistoryData, searchTerm]);

  // Hiển thị loading
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading />
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <Alert
        type="error"
        title="Lỗi tải dữ liệu"
        message={error.message}
        action={
          <Button variant="outline" onClick={() => refetch()}>
            Thử lại
          </Button>
        }
      />
    );
  }

  return (
    <div>
      <Typography variant="h1" className="mb-6">
        {t('purchasedProducts.title', 'Lịch sử mua hàng')}
      </Typography>

      <MenuIconBar
        onSearch={setSearchTerm}
        items={[
          {
            id: 'all',
            label: t('common.all', 'Tất cả'),
            icon: 'list',
            onClick: () => {}, // No filter functionality for now
          },
        ]}
      />

      <Card className="overflow-hidden">
        {filteredData.length === 0 ? (
          <div className="text-center py-12">
            <Icon name="shopping-bag" size="xl" className="text-gray-400 mb-4" />
            <Typography variant="h6" color="muted">
              Chưa có sản phẩm nào được mua
            </Typography>
            <Typography variant="body2" color="muted" className="mt-2">
              Hãy khám phá marketplace để tìm sản phẩm phù hợp
            </Typography>
          </div>
        ) : (
          <Table
            columns={columns}
            data={filteredData}
            rowKey="orderLineId"
            pagination={{
              current: page,
              pageSize: limit,
              total: purchaseHistoryData?.meta?.totalItems || 0,
              onChange: setPage
            }}
          />
        )}
      </Card>
    </div>
  );
};

export default PurchasedProductsPage;
