import { Test, TestingModule } from '@nestjs/testing';
import { EmployeePasswordService } from '../services/employee-password.service';
import { EmployeeRepository } from '../repositories/employee.repository';
import { BadRequestException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';

jest.mock('bcrypt');

describe('EmployeePasswordService', () => {
  let service: EmployeePasswordService;
  let employeeRepository: jest.Mocked<EmployeeRepository>;

  beforeEach(async () => {
    // Create mock for EmployeeRepository
    const employeeRepoMock = {
      changePassword: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmployeePasswordService,
        { provide: EmployeeRepository, useValue: employeeRepoMock },
      ],
    }).compile();

    service = module.get<EmployeePasswordService>(EmployeePasswordService);
    employeeRepository = module.get(EmployeeRepository) as jest.Mocked<EmployeeRepository>;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validatePasswordStrength', () => {
    it('should not throw an exception for a strong password', () => {
      // Arrange
      const strongPassword = 'StrongPassword123!';
      // Mock checkPasswordStrength to return a high score
      jest.spyOn(service, 'checkPasswordStrength').mockReturnValue({ score: 5, feedback: 'Mật khẩu mạnh' });

      // Act & Assert
      expect(() => service.validatePasswordStrength(strongPassword)).not.toThrow();
    });

    it('should throw BadRequestException for a weak password', () => {
      // Arrange
      const weakPassword = 'weak';
      // Mock checkPasswordStrength to return a low score
      jest.spyOn(service, 'checkPasswordStrength').mockReturnValue({ score: 1, feedback: 'Mật khẩu yếu' });

      // Act & Assert
      expect(() => service.validatePasswordStrength(weakPassword)).toThrow(BadRequestException);
    });

    it('should throw BadRequestException for a password without uppercase letters', () => {
      // Arrange
      const passwordWithoutUppercase = 'password123!';
      // Mock checkPasswordStrength to return a low score
      jest.spyOn(service, 'checkPasswordStrength').mockReturnValue({ score: 2, feedback: 'Mật khẩu trung bình' });

      // Act & Assert
      expect(() => service.validatePasswordStrength(passwordWithoutUppercase)).toThrow(BadRequestException);
    });

    it('should throw BadRequestException for a password without numbers', () => {
      // Arrange
      const passwordWithoutNumbers = 'Password!';
      // Mock checkPasswordStrength to return a low score
      jest.spyOn(service, 'checkPasswordStrength').mockReturnValue({ score: 2, feedback: 'Mật khẩu trung bình' });

      // Act & Assert
      expect(() => service.validatePasswordStrength(passwordWithoutNumbers)).toThrow(BadRequestException);
    });

    it('should throw BadRequestException for a password without special characters', () => {
      // Arrange
      const passwordWithoutSpecialChars = 'Password123';
      // Mock checkPasswordStrength to return a low score
      jest.spyOn(service, 'checkPasswordStrength').mockReturnValue({ score: 2, feedback: 'Mật khẩu trung bình' });

      // Act & Assert
      expect(() => service.validatePasswordStrength(passwordWithoutSpecialChars)).toThrow(BadRequestException);
    });
  });

  describe('hashPassword', () => {
    it('should hash password correctly', async () => {
      // Arrange
      const password = 'Password123!';
      const hashedPassword = 'hashedPassword';
      const salt: number = 10;
      jest.spyOn(bcrypt, 'genSalt').mockResolvedValue(salt as never);
      (bcrypt.hash as jest.Mock).mockResolvedValue(hashedPassword);

      // Act
      const result = await service.hashPassword(password);

      // Assert
      expect(bcrypt.hash).toHaveBeenCalledWith(password, salt);
      expect(result).toEqual(hashedPassword);
    });
  });
});
