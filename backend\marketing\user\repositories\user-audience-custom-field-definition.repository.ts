import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeleteResult, FindManyOptions, FindOneOptions, FindOptionsWhere, Repository } from 'typeorm';
import { UserAudienceCustomFieldDefinition } from '../entities/user-audience-custom-field-definition.entity';

/**
 * Repository cho UserAudienceCustomFieldDefinition
 */
@Injectable()
export class UserAudienceCustomFieldDefinitionRepository {
  constructor(
    @InjectRepository(UserAudienceCustomFieldDefinition)
    private readonly repository: Repository<UserAudienceCustomFieldDefinition>,
  ) {}

  /**
   * Tìm kiếm nhiều trường tùy chỉnh
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách trường tùy chỉnh
   */
  async find(options?: FindManyOptions<UserAudienceCustomFieldDefinition>): Promise<UserAudienceCustomFieldDefinition[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một trường tùy chỉnh
   * @param options Tùy chọn tìm kiếm
   * @returns Trường tùy chỉnh hoặc null
   */
  async findOne(options?: FindOneOptions<UserAudienceCustomFieldDefinition>): Promise<UserAudienceCustomFieldDefinition | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Đếm số lượng trường tùy chỉnh
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng trường tùy chỉnh
   */
  async count(options?: FindManyOptions<UserAudienceCustomFieldDefinition>): Promise<number> {
    return this.repository.count(options);
  }

  /**
   * Xóa trường tùy chỉnh theo điều kiện
   * @param criteria Điều kiện xóa
   * @returns Kết quả xóa
   */
  async delete(criteria: FindOptionsWhere<UserAudienceCustomFieldDefinition>): Promise<DeleteResult> {
    return this.repository.delete(criteria);
  }

  /**
   * Xóa trường tùy chỉnh
   * @param customField Trường tùy chỉnh cần xóa
   * @returns Trường tùy chỉnh đã xóa
   */
  async remove(customField: UserAudienceCustomFieldDefinition): Promise<UserAudienceCustomFieldDefinition>;
  async remove(customField: UserAudienceCustomFieldDefinition[]): Promise<UserAudienceCustomFieldDefinition[]>;
  async remove(customField: UserAudienceCustomFieldDefinition | UserAudienceCustomFieldDefinition[]): Promise<UserAudienceCustomFieldDefinition | UserAudienceCustomFieldDefinition[]> {
    return this.repository.remove(customField as any);
  }

  /**
   * Tạo mới trường tùy chỉnh
   * @param data Dữ liệu trường tùy chỉnh
   * @returns Trường tùy chỉnh đã tạo
   */
  async create(data: Partial<UserAudienceCustomFieldDefinition>): Promise<UserAudienceCustomFieldDefinition> {
    const customField = this.repository.create(data);
    return this.repository.save(customField);
  }

  /**
   * Lưu trường tùy chỉnh
   * @param data Dữ liệu trường tùy chỉnh
   * @returns Trường tùy chỉnh đã lưu
   */
  async save(data: Partial<UserAudienceCustomFieldDefinition>): Promise<UserAudienceCustomFieldDefinition> {
    return this.repository.save(data);
  }
}
