import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ProductDetail } from '@/modules/marketplace/types/product.types';

/**
 * Đ<PERSON>nh nghĩa kiểu dữ liệu cho CartItem
 */
export interface CartItem {
  id: number; // Thay đổi từ string sang number để phù hợp với ProductDetail
  name: string;
  thumbnail: string;
  price: number;
  quantity: number;
  selected: boolean;
  function?: string;
}

/**
 * Trạng thái giỏ hàng
 */
export interface CartState {
  items: CartItem[];
  selectedItems: number[]; // Thay đổi từ string[] sang number[]
  isCheckingOut: boolean;
  soldCounts: Record<number, number>; // Thay đổi key từ string sang number
  discount: number;
}

/**
 * Trạng thái ban đầu
 */
const initialState: CartState = {
  items: [],
  selectedItems: [],
  isCheckingOut: false,
  soldCounts: {},
  discount: 8000, // Fixed discount value for demo purposes
};

/**
 * Cart slice
 */
const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    /**
     * Thêm sản phẩm vào giỏ hàng
     */
    addToCart: (
      state,
      action: PayloadAction<{
        product: ProductDetail;
        quantity: number;
      }>
    ) => {
      const { product, quantity } = action.payload;
      const existingItemIndex = state.items.findIndex(item => item.id === product.id);

      if (existingItemIndex !== -1) {
        // Cập nhật số lượng nếu sản phẩm đã tồn tại
        state.items[existingItemIndex].quantity += quantity;
      } else {
        // Thêm sản phẩm mới nếu chưa tồn tại
        const newItem: CartItem = {
          id: product.id,
          name: product.name,
          thumbnail: product.thumbnail || '', // Đảm bảo không undefined
          price: product.price || product.discountedPrice, // Fallback nếu price undefined
          quantity: quantity,
          selected: true,
        };
        state.items.push(newItem);
      }

      // Thêm sản phẩm vào danh sách đã chọn
      if (!state.selectedItems.includes(product.id)) {
        state.selectedItems.push(product.id);
      }
    },

    /**
     * Xóa sản phẩm khỏi giỏ hàng
     */
    removeFromCart: (state, action: PayloadAction<number>) => {
      const itemId = action.payload;
      state.items = state.items.filter(item => item.id !== itemId);
      state.selectedItems = state.selectedItems.filter(id => id !== itemId);
    },

    /**
     * Cập nhật số lượng sản phẩm
     */
    updateQuantity: (
      state,
      action: PayloadAction<{
        itemId: number;
        quantity: number;
      }>
    ) => {
      const { itemId, quantity } = action.payload;

      if (quantity <= 0) {
        // Xóa sản phẩm nếu số lượng <= 0
        state.items = state.items.filter(item => item.id !== itemId);
        state.selectedItems = state.selectedItems.filter(id => id !== itemId);
      } else {
        // Cập nhật số lượng
        const itemIndex = state.items.findIndex(item => item.id === itemId);
        if (itemIndex !== -1) {
          state.items[itemIndex].quantity = quantity;
        }
      }
    },

    /**
     * Xóa tất cả sản phẩm trong giỏ hàng
     */
    clearCart: state => {
      state.items = [];
      state.selectedItems = [];
    },

    /**
     * Chọn/bỏ chọn sản phẩm
     */
    selectItem: (
      state,
      action: PayloadAction<{
        itemId: number;
        selected: boolean;
      }>
    ) => {
      const { itemId, selected } = action.payload;

      if (selected) {
        if (!state.selectedItems.includes(itemId)) {
          state.selectedItems.push(itemId);
        }
      } else {
        state.selectedItems = state.selectedItems.filter(id => id !== itemId);
      }
    },

    /**
     * Chọn/bỏ chọn tất cả sản phẩm
     */
    selectAll: (state, action: PayloadAction<boolean>) => {
      const selected = action.payload;

      if (selected) {
        state.selectedItems = state.items.map(item => item.id);
      } else {
        state.selectedItems = [];
      }
    },

    /**
     * Cập nhật trạng thái thanh toán
     */
    setCheckingOut: (state, action: PayloadAction<boolean>) => {
      state.isCheckingOut = action.payload;
    },

    /**
     * Cập nhật số lượng đã bán
     */
    updateSoldCount: (state, action: PayloadAction<number>) => {
      const productId = action.payload;
      const currentCount = state.soldCounts[productId] || 0;
      state.soldCounts[productId] = currentCount + 1;
    },

    /**
     * Xóa các sản phẩm đã chọn sau khi thanh toán
     */
    removeSelectedItems: state => {
      state.items = state.items.filter(item => !state.selectedItems.includes(item.id));
      state.selectedItems = [];
    },
  },
});

// Export actions
export const {
  addToCart,
  removeFromCart,
  updateQuantity,
  clearCart,
  selectItem,
  selectAll,
  setCheckingOut,
  updateSoldCount,
  removeSelectedItems,
} = cartSlice.actions;

// Export reducer
export default cartSlice.reducer;
