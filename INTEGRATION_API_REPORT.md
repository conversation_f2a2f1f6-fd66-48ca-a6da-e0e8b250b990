# Báo cáo phát triển API Integration: Facebook & Website

## <PERSON><PERSON> tả
Phân tích và tạo các interface, API functions, và React Query hooks cho Facebook và Website integration dựa trên backend controllers và DTOs có sẵn.

## Phân tích Backend Structure

### Facebook Integration
**Controllers**: `facebook-page-user.controller.ts`
**Services**: `facebook-page-user.service.ts`
**DTOs**: 
- `facebook-page-response.dto.ts`
- `facebook-page-query.dto.ts`
- `create-url-auth.dto.ts`

### Website Integration  
**Controllers**: `user-website-user.controller.ts`
**Services**: `user-website-user.service.ts`
**DTOs**:
- `website-response.dto.ts`
- `create-website.dto.ts`
- `website-query.dto.ts`

## Các file đã tạo

### 1. Types & Interfaces

#### `src/modules/integration/types/facebook.types.ts`
- **FacebookPageDto**: Interface cho Facebook Page response
- **FacebookPageQueryDto**: Query parameters cho danh sách pages
- **FacebookAuthResponseDto**: Response cho auth URL
- **FacebookCallbackDto**: Callback data từ Facebook
- **ConnectAgentToFacebookPageDto**: Kết nối agent với page
- **DisconnectAgentFromFacebookPageDto**: Ngắt kết nối agent
- **Enums**: FacebookPageSortBy, SortOrder

#### `src/modules/integration/types/website.types.ts`
- **WebsiteDto**: Interface cho Website response
- **WebsiteQueryDto**: Query parameters cho danh sách websites
- **CreateWebsiteDto**: Tạo website mới
- **UpdateWebsiteDto**: Cập nhật website
- **ConnectAgentToWebsiteDto**: Kết nối agent với website
- **VerifyWebsiteDto**: Xác minh website
- **WebsiteStatsDto**: Thống kê websites
- **WebsiteAnalyticsDto**: Analytics cho website
- **Enums**: WebsiteSortBy, SortDirection

### 2. API Functions

#### `src/modules/integration/api/facebook.api.ts`
```typescript
// Core CRUD operations
- getFacebookPages(params?: FacebookPageQueryDto)
- getFacebookPageDetail(pageId: string)
- deleteFacebookPage(pageId: string)
- deleteManyFacebookPages(data: DeleteFacebookPagesDto)

// Authentication
- createFacebookAuthUrl(params: CreateFacebookUrlAuthDto)
- handleFacebookCallback(data: FacebookCallbackDto)

// Agent Management
- connectAgentToFacebookPage(data: ConnectAgentToFacebookPageDto)
- disconnectAgentFromFacebookPage(data: DisconnectAgentFromFacebookPageDto)

// Additional Features
- updateFacebookPageStatus(pageId: string, isActive: boolean)
- syncFacebookPages(personalId: string)
- deleteFacebookPersonal(personalId: string)
```

#### `src/modules/integration/api/website.api.ts`
```typescript
// Core CRUD operations
- getWebsites(params?: WebsiteQueryDto)
- createWebsite(data: CreateWebsiteDto)
- getWebsiteDetail(websiteId: string)
- updateWebsite(websiteId: string, data: UpdateWebsiteDto)
- deleteWebsite(websiteId: string)
- deleteManyWebsites(websiteIds: string[])

// Agent Management
- connectAgentToWebsite(data: ConnectAgentToWebsiteDto)
- disconnectAgentFromWebsite(data: DisconnectAgentFromWebsiteDto)

// Verification
- verifyWebsite(data: VerifyWebsiteDto)
- getWebsiteVerificationCode(websiteId: string)
- checkWebsiteVerification(websiteId: string)

// Analytics & Stats
- getWebsiteStats()
- getWebsiteAnalytics(websiteId: string, startDate: Date, endDate: Date)
```

### 3. React Query Hooks

#### `src/modules/integration/hooks/useFacebook.ts`
```typescript
// Query Hooks
- useGetFacebookPages(params?, options?)
- useGetFacebookPageDetail(pageId, options?)
- useCreateFacebookAuthUrl(params, options?)

// Mutation Hooks
- useHandleFacebookCallback()
- useDeleteFacebookPage()
- useDeleteManyFacebookPages()
- useDeleteFacebookPersonal()
- useConnectAgentToFacebookPage()
- useDisconnectAgentFromFacebookPage()
- useUpdateFacebookPageStatus()
- useSyncFacebookPages()
```

#### `src/modules/integration/hooks/useWebsite.ts`
```typescript
// Query Hooks
- useGetWebsites(params?, options?)
- useGetWebsiteDetail(websiteId, options?)
- useGetWebsiteVerificationCode(websiteId, options?)
- useCheckWebsiteVerification(websiteId, options?)
- useGetWebsiteStats(options?)
- useGetWebsiteAnalytics(websiteId, startDate, endDate, options?)

// Mutation Hooks
- useCreateWebsite()
- useUpdateWebsite()
- useDeleteWebsite()
- useDeleteManyWebsites()
- useConnectAgentToWebsite()
- useDisconnectAgentFromWebsite()
- useVerifyWebsite()
```

## Tính năng chính

### Facebook Integration
1. **Authentication Flow**: Tạo auth URL và xử lý callback
2. **Page Management**: CRUD operations cho Facebook Pages
3. **Agent Connection**: Kết nối/ngắt kết nối Agent với Pages
4. **Bulk Operations**: Xóa nhiều pages cùng lúc
5. **Status Management**: Bật/tắt trạng thái pages
6. **Sync**: Đồng bộ pages từ Facebook

### Website Integration
1. **Website Management**: CRUD operations cho websites
2. **Verification**: Xác minh ownership của website
3. **Agent Connection**: Kết nối/ngắt kết nối Agent với websites
4. **Analytics**: Thống kê và phân tích website
5. **Bulk Operations**: Xóa nhiều websites cùng lúc
6. **Status Tracking**: Theo dõi trạng thái xác minh

## React Query Features

### Caching Strategy
- **Stale Time**: 5 phút cho data thường, 10 phút cho verification codes
- **Auto Invalidation**: Tự động invalidate cache khi có mutations
- **Background Refetch**: Tự động refetch data khi cần

### Query Keys Structure
```typescript
// Facebook
FACEBOOK_PAGES: 'facebook-pages'
FACEBOOK_PAGE_DETAIL: 'facebook-page-detail'
FACEBOOK_AUTH_URL: 'facebook-auth-url'

// Website
WEBSITES: 'websites'
WEBSITE_DETAIL: 'website-detail'
WEBSITE_VERIFICATION_CODE: 'website-verification-code'
WEBSITE_VERIFICATION_STATUS: 'website-verification-status'
WEBSITE_STATS: 'website-stats'
WEBSITE_ANALYTICS: 'website-analytics'
```

### Error Handling
- Tất cả hooks đều hỗ trợ error handling
- Automatic retry cho failed requests
- Loading states cho UI feedback

## API Endpoints Mapping

### Facebook
```
GET    /integration/facebook/pages              - Danh sách pages
GET    /integration/facebook/pages/:id          - Chi tiết page
DELETE /integration/facebook/pages/:id          - Xóa page
DELETE /integration/facebook/pages              - Xóa nhiều pages
GET    /integration/facebook/auth/url           - Tạo auth URL
POST   /integration/facebook/auth/callback      - Xử lý callback
POST   /integration/facebook/pages/connect-agent    - Kết nối agent
POST   /integration/facebook/pages/disconnect-agent - Ngắt kết nối agent
PATCH  /integration/facebook/pages/:id/status  - Cập nhật trạng thái
POST   /integration/facebook/personals/:id/sync - Đồng bộ pages
```

### Website
```
GET    /integration/website                     - Danh sách websites
POST   /integration/website                     - Tạo website
GET    /integration/website/:id                 - Chi tiết website
PATCH  /integration/website/:id                 - Cập nhật website
DELETE /integration/website/:id                 - Xóa website
DELETE /integration/website/bulk                - Xóa nhiều websites
POST   /integration/website/connect-agent       - Kết nối agent
POST   /integration/website/disconnect-agent    - Ngắt kết nối agent
POST   /integration/website/verify              - Xác minh website
GET    /integration/website/:id/verification-code - Lấy mã xác minh
GET    /integration/website/:id/verification-status - Trạng thái xác minh
GET    /integration/website/stats               - Thống kê
GET    /integration/website/:id/analytics       - Analytics
```

## Type Safety
- ✅ Tất cả interfaces đều strongly typed
- ✅ Enum cho sort fields và directions
- ✅ Optional parameters được handle đúng
- ✅ Generic types cho API responses
- ✅ Proper error typing

## Performance Optimizations
- ✅ Memoized query keys
- ✅ Selective cache invalidation
- ✅ Background refetch
- ✅ Stale-while-revalidate pattern
- ✅ Optimistic updates support

## Usage Examples

### Facebook Integration
```typescript
// Lấy danh sách Facebook Pages
const { data, isLoading, error } = useGetFacebookPages({
  page: 1,
  limit: 10,
  search: 'my page'
});

// Kết nối Agent với Page
const connectMutation = useConnectAgentToFacebookPage();
connectMutation.mutate({
  agentId: 'agent-123',
  facebookPageId: 'page-456'
});
```

### Website Integration
```typescript
// Tạo website mới
const createMutation = useCreateWebsite();
createMutation.mutate({
  websiteName: 'My Website',
  host: 'https://example.com'
});

// Xác minh website
const verifyMutation = useVerifyWebsite();
verifyMutation.mutate({
  websiteId: 'website-123',
  verificationCode: 'abc123'
});
```

## Build Status
- ✅ TypeScript compilation: PASS
- ✅ ESLint (new files): PASS
- ⚠️ ESLint (existing backend files): 53 errors (không ảnh hưởng đến frontend)

## Kết luận
Đã hoàn thành việc tạo đầy đủ API layer cho Facebook và Website integration với:
- ✅ Strongly typed interfaces
- ✅ Comprehensive API functions
- ✅ React Query hooks với caching strategy
- ✅ Error handling và loading states
- ✅ Performance optimizations
- ✅ Consistent naming conventions

Các API và hooks này sẵn sàng để sử dụng trong các components UI cho Facebook và Website integration features.
