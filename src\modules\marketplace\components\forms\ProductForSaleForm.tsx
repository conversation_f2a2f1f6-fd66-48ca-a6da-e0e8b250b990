import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, FormItem, Input, Button, Select, Icon, Textarea } from '@/shared/components/common';
import { SubmitHandler, useFormContext } from 'react-hook-form';
import { ProductStatus, ProductCategory } from '../../types/product-for-sale.types';

// Định nghĩa type cho form values
export interface ProductForSaleFormValues {
  name: string;
  description?: string;
  image?: string;
  price?: number;
  category: ProductCategory;
  detail?: string;
  userManual?: string;
}

/**
 * Component con để handle textarea với form context
 */
interface FormTextareaProps {
  name: string;
  label: string;
  placeholder: string;
  originalValue: string;
  onChangeDetected: (hasChanged: boolean) => void;
}

const FormTextarea: React.FC<FormTextareaProps> = ({
  name,
  label,
  placeholder,
  originalValue,
  onChangeDetected
}) => {
  const { setValue } = useFormContext<ProductForSaleFormValues>();
  const [displayValue, setDisplayValue] = useState<string>(''); // Giá trị hiển thị trong textarea
  const [originalContent, setOriginalContent] = useState<string>(''); // Nội dung gốc để so sánh
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  // Fetch nội dung từ CDN URL chỉ một lần khi component mount
  useEffect(() => {
    if (isInitialized) return;

    const fetchContentFromUrl = async (url: string) => {
      if (!url) {
        console.log(`❌ [${name}] Empty URL provided`);
        return '';
      }

      console.log(`🔍 [${name}] ==================== FETCHING ====================`);
      console.log(`🔍 [${name}] Input URL:`, url);

      // Làm sạch URL - giữ nguyên query parameters
      const cleanUrl = url.trim();
      console.log(`🔍 [${name}] Clean URL:`, cleanUrl);

      // FORCE FETCH - bỏ qua kiểm tra domain để test
      // if (!cleanUrl.includes('cdn.redai.vn')) {
      //   console.log(`⚠️ [${name}] URL không chứa cdn.redai.vn:`, cleanUrl);
      //   return '';
      // }

      try {
        setIsLoading(true);
        console.log(`🔍 [${name}] Starting fetch request...`);

        // Thêm timeout 10 giây
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        const response = await fetch(cleanUrl, {
          signal: controller.signal,
          method: 'GET',
          headers: {
            'Accept': 'text/plain, text/html, */*',
          },
        });

        clearTimeout(timeoutId);

        console.log(`🔍 [${name}] Response status:`, response.status);
        console.log(`🔍 [${name}] Response headers:`, Object.fromEntries(response.headers.entries()));

        if (response.ok) {
          const content = await response.text();
          console.log(`✅ [${name}] ✅ FETCH SUCCESS!`);
          console.log(`✅ [${name}] Content length:`, content.length);
          console.log(`✅ [${name}] Content preview:`, content.substring(0, 200) + '...');
          return content;
        } else {
          console.error(`❌ [${name}] ❌ HTTP ERROR:`, response.status, response.statusText);
          const errorText = await response.text();
          console.error(`❌ [${name}] Error response:`, errorText.substring(0, 200));
          return '';
        }
      } catch (error) {
        console.error(`❌ [${name}] ❌ FETCH EXCEPTION:`, error);
        if (error instanceof Error) {
          console.error(`❌ [${name}] Error name:`, error.name);
          console.error(`❌ [${name}] Error message:`, error.message);
        }
        return '';
      } finally {
        setIsLoading(false);
        console.log(`🔍 [${name}] ==================== FETCH END ====================`);
      }
    };

    const initializeContent = async () => {
      let content = '';

      console.log(`🔍 [${name}] ==================== INITIALIZING ====================`);
      console.log(`🔍 [${name}] originalValue:`, originalValue);
      console.log(`🔍 [${name}] originalValue type:`, typeof originalValue);
      console.log(`🔍 [${name}] originalValue length:`, originalValue?.length);
      console.log(`🔍 [${name}] Is URL?:`, originalValue?.startsWith?.('https://cdn.redai.vn/'));
      console.log(`🔍 [${name}] Contains cdn.redai.vn?:`, originalValue?.includes?.('cdn.redai.vn'));

      // Kiểm tra nếu originalValue là URL CDN - LOGIC MẠNH HƠN
      const isUrl = originalValue && originalValue.length > 10 && (
        originalValue.startsWith('https://cdn.redai.vn/') ||
        originalValue.includes('cdn.redai.vn') ||
        (originalValue.startsWith('https://') && originalValue.includes('expires=')) // Presigned URL
      );

      console.log(`🔍 [${name}] isUrl decision:`, isUrl);

      if (isUrl) {
        console.log(`🔍 [${name}] ✅ DETECTED URL - FETCHING CONTENT...`);
        content = await fetchContentFromUrl(originalValue);

        // Nếu fetch thất bại, fallback về originalValue
        if (!content || content.trim() === '') {
          console.log(`⚠️ [${name}] ❌ FETCH FAILED - using originalValue as fallback`);
          content = originalValue || '';
        } else {
          console.log(`✅ [${name}] ✅ FETCH SUCCESS - content length:`, content.length);
        }
      } else {
        // Nếu không phải URL, sử dụng trực tiếp
        console.log(`🔍 [${name}] ❌ NOT URL - using originalValue directly`);
        content = originalValue || '';
      }

      console.log(`✅ [${name}] Final content:`, content.substring(0, 100) + '...');

      setOriginalContent(content);
      setDisplayValue(content);
      setValue(name as keyof ProductForSaleFormValues, content as string);

      // Reset change detection khi khởi tạo
      console.log(`🔄 [${name}] RESET change detection to FALSE on initialization`);
      onChangeDetected(false);
      setIsInitialized(true);
    };

    initializeContent();
  }, [originalValue, name, setValue, isInitialized, onChangeDetected]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setDisplayValue(newValue); // Cập nhật giá trị hiển thị
    setValue(name as keyof ProductForSaleFormValues, newValue as string); // Cập nhật form state

    // Check if changed from original content (not URL)
    const hasChanged = newValue.trim() !== originalContent.trim();
    console.log(`📝 [${name}] CHANGE DETECTION:`, {
      hasChanged,
      originalLength: originalContent.length,
      newLength: newValue.length,
      originalPreview: originalContent.substring(0, 50) + '...',
      newPreview: newValue.substring(0, 50) + '...',
      originalTrimmed: originalContent.trim().substring(0, 30),
      newTrimmed: newValue.trim().substring(0, 30)
    });

    onChangeDetected(hasChanged);
  };

  return (
    <FormItem name={name} label={label}>
      <Textarea
        rows={4}
        placeholder={isLoading ? 'Đang tải nội dung...' : placeholder}
        fullWidth
        value={displayValue} // Sử dụng displayValue thay vì currentValue từ form
        onChange={handleChange}
        disabled={isLoading}
      />
    </FormItem>
  );
};

interface ProductForSaleFormProps {
  onSubmit: (values: ProductForSaleFormValues & {
    imageFiles?: File[]; // Thay đổi để hỗ trợ nhiều ảnh
    oldImageKeys?: string[]; // Keys của ảnh cũ (để reference)
    deletedImageKeys?: string[]; // Keys của ảnh bị xóa (để gửi DELETE operations)
    hasImageChanged?: boolean; // Flag để biết ảnh có thay đổi không
    uploadedImageUrls?: string[]; // URLs của ảnh đã upload thành công
    hasDetailChanged?: boolean; // Flag để biết detail có thay đổi không
    hasUserManualChanged?: boolean; // Flag để biết userManual có thay đổi không
  }, submitForApproval?: boolean) => void;
  onCancel: () => void;
  initialValues?: Partial<ProductForSaleFormValues & { oldImageKeys?: string[]; images?: string[]; detail?: string; userManual?: string }>;
  isSubmitting?: boolean;
}

/**
 * Form thêm/sửa sản phẩm đăng bán
 */
const ProductForSaleForm: React.FC<ProductForSaleFormProps> = ({
  onSubmit,
  onCancel,
  initialValues,
  isSubmitting = false,
}) => {
  const { t } = useTranslation('marketplace');

  // Tạo schema với translation

  // State cho image upload - hỗ trợ nhiều ảnh
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>(initialValues?.images || []);
  const [hasImageChanged, setHasImageChanged] = useState<boolean>(false);
  const [uploadedImageUrls, setUploadedImageUrls] = useState<string[]>([]);
  const [deletedImageKeys, setDeletedImageKeys] = useState<string[]>([]); // Track ảnh bị xóa

  // Track mapping giữa preview và key để tránh lỗi index
  const [imageKeyMapping, setImageKeyMapping] = useState<Map<string, string>>(new Map());

  // Track thay đổi detail và userManual
  const [hasDetailChanged, setHasDetailChanged] = useState<boolean>(false);
  const [hasUserManualChanged, setHasUserManualChanged] = useState<boolean>(false);
  const originalDetail = initialValues?.detail || '';
  const originalUserManual = initialValues?.userManual || '';

  // Debug state changes
  useEffect(() => {
    console.log(`🔍 [MAIN] hasDetailChanged changed to:`, hasDetailChanged);
  }, [hasDetailChanged]);

  useEffect(() => {
    console.log(`🔍 [MAIN] hasUserManualChanged changed to:`, hasUserManualChanged);
  }, [hasUserManualChanged]);

  // Khởi tạo mapping khi component mount
  useEffect(() => {
    if (initialValues?.images && initialValues?.oldImageKeys) {
      const mapping = new Map<string, string>();
      initialValues.images.forEach((imageUrl: string, index: number) => {
        const key = initialValues.oldImageKeys?.[index];
        if (key) {
          mapping.set(imageUrl, key);
        }
      });
      setImageKeyMapping(mapping);
      console.log('🔍 [INIT] Image key mapping:', mapping);
    }
  }, [initialValues?.images, initialValues?.oldImageKeys]);

  // Xử lý khi thêm ảnh mới
  const handleAddImage = (file: File, dataUrl: string) => {
    setImageFiles(prev => [...prev, file]);
    setImagePreviews(prev => [...prev, dataUrl]);
    setHasImageChanged(true);
  };

  // Xử lý khi xóa ảnh
  const handleRemoveImage = (index: number) => {
    console.log('🔍 [REMOVE IMAGE] Starting removal for index:', index);
    console.log('🔍 [REMOVE IMAGE] Current imagePreviews:', imagePreviews);
    console.log('🔍 [REMOVE IMAGE] Current imageFiles:', imageFiles);
    console.log('🔍 [REMOVE IMAGE] Initial images length:', initialValues?.images?.length || 0);
    console.log('🔍 [REMOVE IMAGE] oldImageKeys:', initialValues?.oldImageKeys);

    // Lấy URL của ảnh đang xóa
    const imageUrl = imagePreviews[index];
    console.log('🔍 [REMOVE IMAGE] Image URL to remove:', imageUrl);

    // Kiểm tra xem có phải ảnh cũ không bằng cách tìm trong mapping
    const correspondingKey = imageKeyMapping.get(imageUrl);
    if (correspondingKey) {
      console.log('🔍 [REMOVE IMAGE] Removing OLD image with key:', correspondingKey);
      setDeletedImageKeys(prev => {
        const newDeleted = [...prev, correspondingKey];
        console.log('🗑️ [REMOVE IMAGE] Updated deletedImageKeys:', newDeleted);
        return newDeleted;
      });
      // Xóa khỏi mapping
      setImageKeyMapping(prev => {
        const newMapping = new Map(prev);
        newMapping.delete(imageUrl);
        return newMapping;
      });
    } else {
      console.log('🔍 [REMOVE IMAGE] Removing NEW image (no key found)');
    }

    setImageFiles(prev => {
      const newFiles = prev.filter((_, i) => i !== index);
      console.log('🔍 [REMOVE IMAGE] Updated imageFiles:', newFiles);
      return newFiles;
    });
    setImagePreviews(prev => {
      const newPreviews = prev.filter((_, i) => i !== index);
      console.log('🔍 [REMOVE IMAGE] Updated imagePreviews:', newPreviews);
      return newPreviews;
    });
    setUploadedImageUrls(prev => prev.filter((_, i) => i !== index));
    setHasImageChanged(true);
  };



  // Xử lý khi submit form (lưu draft)
  const handleFormSubmit: SubmitHandler<ProductForSaleFormValues> = values => {
    // Validation: Kiểm tra có ảnh không
    if (imageFiles.length === 0 && imagePreviews.length === 0) {
      alert(t('productsForSale.form.validation.imageRequired', 'Vui lòng chọn ít nhất một ảnh sản phẩm'));
      return;
    }

    onSubmit({
      ...values,
      imageFiles, // Danh sách file ảnh mới
      oldImageKeys: initialValues?.oldImageKeys, // Keys của ảnh cũ (để reference)
      deletedImageKeys, // Keys của ảnh bị xóa (để gửi DELETE operations)
      hasImageChanged, // Flag để biết ảnh có thay đổi không
      uploadedImageUrls, // URLs của ảnh đã upload
      hasDetailChanged, // Flag để biết detail có thay đổi không
      hasUserManualChanged, // Flag để biết userManual có thay đổi không
      status: ProductStatus.ACTIVE,
    } as ProductForSaleFormValues & {
      imageFiles?: File[];
      oldImageKeys?: string[];
      deletedImageKeys?: string[];
      hasImageChanged?: boolean;
      uploadedImageUrls?: string[];
      hasDetailChanged?: boolean;
      hasUserManualChanged?: boolean;
    }, false);
  };

  // Xử lý khi submit form (gửi duyệt)
  const handleSubmitForApproval: SubmitHandler<ProductForSaleFormValues> = values => {
    // Validation: Kiểm tra có ảnh không
    if (imageFiles.length === 0 && imagePreviews.length === 0) {
      alert(t('productsForSale.form.validation.imageRequired', 'Vui lòng chọn ít nhất một ảnh sản phẩm'));
      return;
    }

    onSubmit({
      ...values,
      imageFiles, // Danh sách file ảnh mới
      oldImageKeys: initialValues?.oldImageKeys, // Keys của ảnh cũ (để reference)
      deletedImageKeys, // Keys của ảnh bị xóa (để gửi DELETE operations)
      hasImageChanged, // Flag để biết ảnh có thay đổi không
      uploadedImageUrls, // URLs của ảnh đã upload
      hasDetailChanged, // Flag để biết detail có thay đổi không
      hasUserManualChanged, // Flag để biết userManual có thay đổi không
      status: ProductStatus.ACTIVE,
    } as ProductForSaleFormValues & {
      imageFiles?: File[];
      oldImageKeys?: string[];
      deletedImageKeys?: string[];
      hasImageChanged?: boolean;
      uploadedImageUrls?: string[];
      hasDetailChanged?: boolean;
      hasUserManualChanged?: boolean;
    }, true);
  };

  // Giả lập URL ảnh ngẫu nhiên
  const getRandomImageUrl = () => {
    return `/images/products/product-${Math.floor(Math.random() * 10) + 1}.jpg`;
  };

  return (
    <div className="p-6">
      <h2 className="text-xl font-semibold mb-6">
        {initialValues?.name
          ? t('productsForSale.editProduct', 'Chỉnh sửa sản phẩm')
          : t('productsForSale.addProduct', 'Thêm sản phẩm mới')}
      </h2>

      <Form
        onSubmit={handleFormSubmit as unknown as SubmitHandler<Record<string, unknown>>}
        defaultValues={{
          ...initialValues,
          image: initialValues?.image || getRandomImageUrl(),
          detail: initialValues?.detail || '',
          userManual: initialValues?.userManual || '',
        }}
        className="space-y-6"
      >
        <FormItem name="name" label={t('productsForSale.form.name', 'Tên sản phẩm')} required>
          <Input
            placeholder={t('productsForSale.form.namePlaceholder', 'Nhập tên sản phẩm')}
            fullWidth
          />
        </FormItem>

        <FormItem name="description" label={t('productsForSale.form.description', 'Mô tả sản phẩm')}>
          <Input
            placeholder={t('productsForSale.form.descriptionPlaceholder', 'Nhập mô tả sản phẩm')}
            fullWidth
          />
        </FormItem>

        <FormItem name="image" label={t('productsForSale.form.image', 'Ảnh sản phẩm')} required>
          <div className="space-y-4">
            {/* Hiển thị ảnh hiện có */}
            {imagePreviews.length > 0 && (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {imagePreviews.map((preview, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={preview}
                      alt={`Product ${index + 1}`}
                      className="w-full h-32 object-cover rounded-lg border"
                    />
                    <button
                      type="button"
                      onClick={() => handleRemoveImage(index)}
                      className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Icon name="x" size="xs" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Upload ảnh mới */}
            <div>
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => {
                  const files = Array.from(e.target.files || []);
                  files.forEach(file => {
                    const reader = new FileReader();
                    reader.onload = (event) => {
                      if (event.target?.result) {
                        handleAddImage(file, event.target.result as string);
                      }
                    };
                    reader.readAsDataURL(file);
                  });
                }}
                className="hidden"
                id="image-upload"
              />
              <label
                htmlFor="image-upload"
                className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50"
              >
                <Icon name="upload" size="lg" className="text-gray-400 mb-2" />
                <span className="text-sm text-gray-500">
                  {t('productsForSale.form.imagePlaceholder', 'Kéo thả hoặc click để tải lên ảnh sản phẩm')}
                </span>
                <span className="text-xs text-gray-400 mt-1">
                  {t('productsForSale.form.imageSupport', 'Hỗ trợ nhiều ảnh, định dạng: JPG, PNG')}
                </span>
              </label>
            </div>

            {/* Thông tin file */}
            {imageFiles.length > 0 && (
              <div className="text-sm text-gray-500">
                <p>{t('productsForSale.form.selectedImages', 'Đã chọn {{count}} ảnh mới', { count: imageFiles.length })}</p>
              </div>
            )}
          </div>
        </FormItem>

        <FormItem name="price" label={t('productsForSale.form.price', 'Giá bán (rpoint)')}>
          <Input
            type="number"
            placeholder={t('productsForSale.form.pricePlaceholder', 'Nhập giá bán (để trống nếu không thay đổi)')}
            min={0}
            step={1000}
            leftIcon={<Icon name="dollar-sign" size="sm" />}
            fullWidth
          />
        </FormItem>

        <FormItem name="category" label={t('productsForSale.form.category', 'Thể loại')} required>
          <Select
            placeholder={t('productsForSale.form.categoryPlaceholder', 'Chọn thể loại')}
            options={[
              {
                value: ProductCategory.AGENT,
                label: t('productsForSale.categories.agent', 'AI Agent'),
              },
              {
                value: ProductCategory.KNOWLEDGE_FILE,
                label: t('productsForSale.categories.knowledgeFile', 'Knowledge File'),
              },
              {
                value: ProductCategory.FUNCTION,
                label: t('productsForSale.categories.function', 'Function')
              },
              {
                value: ProductCategory.FINETUNE,
                label: t('productsForSale.categories.finetune', 'Fine-tuned Model'),
              },
              {
                value: ProductCategory.STRATEGY,
                label: t('productsForSale.categories.strategy', 'Strategy'),
              },
            ]}
            fullWidth
          />
        </FormItem>

        {/* Thêm input cho detail và userManual */}
        {initialValues?.name && (
          <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-700">Thông tin bổ sung</h3>

            <FormTextarea
              name="detail"
              label="Thông tin chi tiết sản phẩm"
              placeholder="Nhập thông tin chi tiết sản phẩm (nếu có thay đổi)"
              originalValue={originalDetail}
              onChangeDetected={setHasDetailChanged}
            />

            <FormTextarea
              name="userManual"
              label="Hướng dẫn sử dụng"
              placeholder="Nhập hướng dẫn sử dụng sản phẩm (nếu có thay đổi)"
              originalValue={originalUserManual}
              onChangeDetected={setHasUserManualChanged}
            />
          </div>
        )}

        <div className="flex justify-end space-x-4 mt-8">
          <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button variant="outline" type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? t('common.saving', 'Đang lưu...')
              : t('common.saveDraft', 'Lưu nháp')
            }
          </Button>
          {initialValues?.name && (
            <Button
              variant="primary"
              type="button"
              disabled={isSubmitting}
              onClick={() => {
                // Get form values and submit for approval
                const form = document.querySelector('form');
                if (form) {
                  const formData = new FormData(form);
                  const values = Object.fromEntries(formData.entries()) as unknown as ProductForSaleFormValues;
                  handleSubmitForApproval(values);
                }
              }}
            >
              {isSubmitting ? t('common.submitting', 'Đang gửi...') : t('common.submitForApproval', 'Gửi duyệt')}
            </Button>
          )}
        </div>
      </Form>
    </div>
  );
};

export default ProductForSaleForm;
