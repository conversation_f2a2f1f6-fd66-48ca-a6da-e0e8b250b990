import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { AdminEmailServerConfigurationService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { CurrentEmployee } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { EmailServerQueryDto } from '../dto';
import { CreateEmailServerDto, TestEmailServerDto, UpdateEmailServerDto } from '../../user/dto';
import { AdminEmailServerConfigurationEntity } from '@modules/integration/entities/admin_email_server_configurations.entity';

@ApiTags(SWAGGER_API_TAGS.INTEGRATION_ADMIN)
@Controller('admin/integration/email-server')
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtEmployeeGuard)
export class AdminEmailServerConfigurationController {
  constructor(
    private readonly adminEmailServerConfigurationService: AdminEmailServerConfigurationService,
  ) {}

  /**
   * Lấy danh sách cấu hình máy chủ email
   * @param queryDto Tham số truy vấn
   * @param employee Thông tin nhân viên hiện tại
   * @returns Danh sách cấu hình máy chủ email
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách cấu hình máy chủ email' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Số trang hiện tại (bắt đầu từ 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Số lượng bản ghi trên mỗi trang',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Tìm kiếm theo tên máy chủ hoặc host',
  })
  @ApiQuery({
    name: 'userId',
    required: false,
    type: Number,
    description: 'Lọc theo ID người dùng',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách cấu hình máy chủ email',
    type: ApiResponseDto
  })
  async findAll(
    @Query() queryDto: EmailServerQueryDto,
    @CurrentEmployee() employee: JwtPayload
  ): Promise<ApiResponseDto<PaginatedResult<AdminEmailServerConfigurationEntity>>> {
    const emailServers = await this.adminEmailServerConfigurationService.findAll(queryDto);
    return ApiResponseDto.success(emailServers, 'Lấy danh sách cấu hình máy chủ email thành công');
  }

  /**
   * Lấy thông tin chi tiết của một cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param employee Thông tin nhân viên hiện tại
   * @returns Thông tin chi tiết của cấu hình máy chủ email
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết của một cấu hình máy chủ email' })
  @ApiParam({ name: 'id', description: 'ID của cấu hình máy chủ email', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết của cấu hình máy chủ email',
    type: ApiResponseDto
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy cấu hình máy chủ email' })
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentEmployee() employee: JwtPayload
  ): Promise<ApiResponseDto<AdminEmailServerConfigurationEntity>> {
    const emailServer = await this.adminEmailServerConfigurationService.findOne(id);
    return ApiResponseDto.success(emailServer, 'Lấy thông tin chi tiết cấu hình máy chủ email thành công');
  }

  /**
   * Tạo mới cấu hình máy chủ email
   * @param createEmailServerDto Thông tin cấu hình máy chủ email cần tạo
   * @param employee Thông tin nhân viên hiện tại
   * @returns Cấu hình máy chủ email đã được tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới cấu hình máy chủ email' })
  @ApiBody({ type: CreateEmailServerDto })
  @ApiResponse({
    status: 201,
    description: 'Cấu hình máy chủ email đã được tạo',
    type: ApiResponseDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  async create(
    @Body() createEmailServerDto: CreateEmailServerDto,
    @CurrentEmployee() employee: JwtPayload
  ): Promise<ApiResponseDto<AdminEmailServerConfigurationEntity>> {
    const emailServer = await this.adminEmailServerConfigurationService.create(createEmailServerDto, employee.id);
    return ApiResponseDto.success(emailServer, 'Tạo mới cấu hình máy chủ email thành công');
  }

  /**
   * Cập nhật thông tin cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param updateEmailServerDto Thông tin cần cập nhật
   * @param employee Thông tin nhân viên hiện tại
   * @returns Cấu hình máy chủ email đã được cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật thông tin cấu hình máy chủ email' })
  @ApiParam({ name: 'id', description: 'ID của cấu hình máy chủ email', type: Number })
  @ApiBody({ type: UpdateEmailServerDto })
  @ApiResponse({
    status: 200,
    description: 'Cấu hình máy chủ email đã được cập nhật',
    type: ApiResponseDto
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy cấu hình máy chủ email' })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateEmailServerDto: UpdateEmailServerDto,
    @CurrentEmployee() employee: JwtPayload
  ): Promise<ApiResponseDto<AdminEmailServerConfigurationEntity>> {
    const emailServer = await this.adminEmailServerConfigurationService.update(id, updateEmailServerDto, employee.id);
    return ApiResponseDto.success(emailServer, 'Cập nhật cấu hình máy chủ email thành công');
  }

  /**
   * Xóa cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param employee Thông tin nhân viên hiện tại
   * @returns Thông báo kết quả
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa cấu hình máy chủ email' })
  @ApiParam({ name: 'id', description: 'ID của cấu hình máy chủ email', type: Number })
  @ApiResponse({ status: 200, description: 'Thông báo kết quả xóa', type: ApiResponseDto })
  @ApiResponse({ status: 404, description: 'Không tìm thấy cấu hình máy chủ email' })
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentEmployee() employee: JwtPayload
  ): Promise<ApiResponseDto<{ message: string }>> {
    const result = await this.adminEmailServerConfigurationService.remove(id, employee.id);
    return ApiResponseDto.success(result, 'Xóa cấu hình máy chủ email thành công');
  }

  /**
   * Kiểm tra kết nối máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param testEmailServerDto Thông tin kiểm tra
   * @param employee Thông tin nhân viên hiện tại
   * @returns Kết quả kiểm tra
   */
  @Post(':id/test')
  @ApiOperation({ summary: 'Kiểm tra kết nối máy chủ email' })
  @ApiParam({ name: 'id', description: 'ID của cấu hình máy chủ email', type: Number })
  @ApiBody({ type: TestEmailServerDto })
  @ApiResponse({
    status: 200,
    description: 'Kết quả kiểm tra kết nối',
    type: ApiResponseDto
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy cấu hình máy chủ email' })
  async testConnection(
    @Param('id', ParseIntPipe) id: number,
    @Body() testEmailServerDto: TestEmailServerDto,
    @CurrentEmployee() employee: JwtPayload
  ): Promise<ApiResponseDto<{ success: boolean; message: string; details?: any }>> {
    const result = await this.adminEmailServerConfigurationService.testConnection(id, testEmailServerDto);
    return ApiResponseDto.success(result, 'Kiểm tra kết nối máy chủ email thành công');
  }
}
