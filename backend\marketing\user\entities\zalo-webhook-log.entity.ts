import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng zalo_webhook_logs trong cơ sở dữ liệu
 * Lưu trữ lịch sử các sự kiện webhook từ Zalo
 */
@Entity('zalo_webhook_logs')
export class ZaloWebhookLog {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của Official Account
   */
  @Column({ name: 'oa_id', length: 50 })
  oaId: string;

  /**
   * Tên sự kiện
   */
  @Column({ name: 'event_name', length: 50 })
  eventName: string;

  /**
   * ID của sự kiện
   */
  @Column({ name: 'event_id', length: 50 })
  eventId: string;

  /**
   * Dữ liệu của sự kiện (JSON)
   */
  @Column({ name: 'data', type: 'jsonb' })
  data: any;

  /**
   * Đã xử lý chưa
   */
  @Column({ name: 'processed', default: false })
  processed: boolean;

  /**
   * Thời điểm x<PERSON>y ra sự kiện (Unix timestamp)
   */
  @Column({ name: 'timestamp', type: 'bigint' })
  timestamp: number;

  /**
   * Thời điểm tạo bản ghi (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;
}
