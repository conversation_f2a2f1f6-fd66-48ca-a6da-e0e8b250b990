import { Loading } from '@/shared/components';
import MainLayout from '@/shared/layouts/MainLayout';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';

// Import AI pages
const AIAgentsPage = lazy(() => import('@/modules/ai-agents/pages/AIAgentsPage'));
const AgentDetailPage = lazy(() => import('@/modules/ai-agents/pages/AgentDetailPage'));
const AgentEditPage = lazy(() => import('@/modules/ai-agents/pages/AgentEditPage'));

/**
 * AI module routes
 */
const aiRoutes: RouteObject[] = [
  {
    path: '/ai-agents',
    element: (
      <MainLayout title="AI Agents">
        <Suspense fallback={<Loading />}>
          <AIAgentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  // Tạm thời ẩn trang categories theo yêu cầu
  // {
  //   path: '/ai-agents/categories',
  //   element: (
  //     <MainLayout title="AI Agent Categories">
  //       <Suspense fallback={<Loading />}>
  //         <AgentCategoriesPage />
  //       </Suspense>
  //     </MainLayout>
  //   ),
  // },
  {
    path: '/ai-agents/:id',
    element: (
      <MainLayout title="AI Agent Details">
        <Suspense fallback={<Loading />}>
          <AgentDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/ai-agents/:id/edit',
    element: (
      <MainLayout title="Edit AI Agent">
        <Suspense fallback={<Loading />}>
          <AgentEditPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default aiRoutes;
