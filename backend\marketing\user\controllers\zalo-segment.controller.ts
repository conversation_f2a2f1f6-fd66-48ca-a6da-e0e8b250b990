import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, getSchemaPath } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ZaloService } from '../services/zalo.service';
import { ZaloSegment, ZaloFollower } from '../entities';
import {
  CreateZaloSegmentDto,
  UpdateZaloSegmentDto,
  ZaloSegmentQueryDto,
  ZaloSegmentResponseDto,
  FollowerQueryDto,
  FollowerResponseDto,
} from '../dto/zalo';

/**
 * Controller xử lý API liên quan đến phân đoạn người dùng Zalo
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_SEGMENT)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/:oaId/segments')
export class ZaloSegmentController {
  constructor(private readonly zaloService: ZaloService) {}

  /**
   * Lấy danh sách phân đoạn Zalo
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách phân đoạn Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách phân đoạn Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZaloSegmentResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getSegments(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query() queryDto: ZaloSegmentQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloSegment>>> {
    const result = await this.zaloService.getZaloSegments(user.id, oaId, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách phân đoạn Zalo thành công');
  }

  /**
   * Lấy thông tin chi tiết phân đoạn Zalo
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết phân đoạn Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết phân đoạn Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloSegmentResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy phân đoạn Zalo' })
  async getSegmentDetail(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ZaloSegment>> {
    const result = await this.zaloService.getZaloSegmentDetail(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết phân đoạn Zalo thành công');
  }

  /**
   * Tạo phân đoạn Zalo mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo phân đoạn Zalo mới' })
  @ApiResponse({
    status: 201,
    description: 'Tạo phân đoạn Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloSegmentResponseDto) }
          }
        }
      ]
    }
  })
  async createSegment(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() createDto: CreateZaloSegmentDto,
  ): Promise<ApiResponseDto<ZaloSegment>> {
    const result = await this.zaloService.createZaloSegment(user.id, oaId, createDto);
    return ApiResponseDto.success(result, 'Tạo phân đoạn Zalo thành công');
  }

  /**
   * Cập nhật phân đoạn Zalo
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật phân đoạn Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật phân đoạn Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloSegmentResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy phân đoạn Zalo' })
  async updateSegment(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateZaloSegmentDto,
  ): Promise<ApiResponseDto<ZaloSegment>> {
    const result = await this.zaloService.updateZaloSegment(user.id, oaId, id, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật phân đoạn Zalo thành công');
  }

  /**
   * Xóa phân đoạn Zalo
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa phân đoạn Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Xóa phân đoạn Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { type: 'boolean' }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy phân đoạn Zalo' })
  async deleteSegment(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.zaloService.deleteZaloSegment(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Xóa phân đoạn Zalo thành công');
  }

  /**
   * Lấy danh sách người theo dõi thuộc phân đoạn
   */
  @Get(':id/followers')
  @ApiOperation({ summary: 'Lấy danh sách người theo dõi thuộc phân đoạn' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách người theo dõi thuộc phân đoạn thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(FollowerResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy phân đoạn Zalo' })
  async getSegmentFollowers(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
    @Query() queryDto: FollowerQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloFollower>>> {
    const result = await this.zaloService.getZaloSegmentFollowers(user.id, oaId, id, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách người theo dõi thuộc phân đoạn thành công');
  }

  /**
   * Cập nhật phân đoạn Zalo
   */
  @Post(':id/refresh')
  @ApiOperation({ summary: 'Cập nhật phân đoạn Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật phân đoạn Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloSegmentResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy phân đoạn Zalo' })
  async refreshSegment(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ZaloSegment>> {
    const result = await this.zaloService.refreshZaloSegment(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Cập nhật phân đoạn Zalo thành công');
  }
}
