import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeController } from '../controller/employee.controller';
import { EmployeeService } from '../services/employee.service';
import { EmployeeAuthService } from '../services/employee-auth.service';
import { CreateEmployeeDto } from '../dto/create-employee.dto';
import { EmployeeAvatarUploadDto, UpdateEmployeeAvatarDto } from '../dto/employee-avatar-upload.dto';
import { ChangeEmployeePasswordDto } from '../dto/change-employee-password.dto';
import { AssignEmployeeRoleDto } from '../dto/assign-employee-role.dto';
import { EmployeeLoginDto } from '../dto/employee-login.dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { Employee } from '../entities/employee.entity';
import { TimeIntervalEnum } from '@/shared/utils';
import { FileSizeEnum, ImageTypeEnum } from '../dto/employee-avatar-upload.dto';
import { mockConfigModule } from './mocks/config.mock';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { JwtUtilService } from '@/modules/auth/guards/jwt.util';
import { RedisService } from '@/shared/services/redis.service';

describe('EmployeeController', () => {
  let controller: EmployeeController;
  let employeeService: jest.Mocked<EmployeeService>;
  let employeeAuthService: jest.Mocked<EmployeeAuthService>;

  const mockEmployee: Employee = {
    id: 1,
    fullName: 'Test Employee',
    email: '<EMAIL>',
    phoneNumber: '0987654321',
    password: 'hashedPassword',
    address: 'Test Address',
    createdAt: Date.now(),
    updatedAt: Date.now(),
    enable: true,
    avatar: 'avatar-url',
    roles: []
  };

  const mockLoginResponse = {
    accessToken: 'access-token',
    refreshToken: 'refresh-token',
    expiresIn: 3600,
    refreshExpiresIn: 86400,
    employee: {
      id: 1,
      email: '<EMAIL>',
      fullName: 'Test Employee',
      avatar: 'avatar-url',
      roles: [{ id: 1, name: 'Admin', code: 'admin' }],
      permissions: ['users:read', 'users:write'],
    },
  };

  beforeEach(async () => {
    // Create mock implementations
    const employeeServiceMock = {
      createEmployee: jest.fn(),
      createAvatarUploadUrl: jest.fn(),
      updateAvatar: jest.fn(),
      changePassword: jest.fn(),
      assignRoles: jest.fn(),
      getEmployeeRoles: jest.fn(),
    };

    const employeeAuthServiceMock = {
      login: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [EmployeeController],
      providers: [
        { provide: EmployeeService, useValue: employeeServiceMock },
        { provide: EmployeeAuthService, useValue: employeeAuthServiceMock },
        mockConfigModule,
        { provide: JwtUtilService, useValue: { verifyToken: jest.fn() } },
        { provide: RedisService, useValue: { get: jest.fn(), set: jest.fn() } },
      ],
    })
    .overrideGuard(JwtEmployeeGuard)
    .useValue({ canActivate: () => true })
    .compile();

    controller = module.get<EmployeeController>(EmployeeController);
    employeeService = module.get(EmployeeService) as jest.Mocked<EmployeeService>;
    employeeAuthService = module.get(EmployeeAuthService) as jest.Mocked<EmployeeAuthService>;
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('login', () => {
    it('should login employee successfully', async () => {
      // Arrange
      const loginDto: EmployeeLoginDto = {
        email: '<EMAIL>',
        password: 'password123'
      };

      employeeAuthService.login.mockResolvedValue(mockLoginResponse);

      // Act
      const result = await controller.login(loginDto);

      // Assert
      expect(employeeAuthService.login).toHaveBeenCalledWith(loginDto);
      expect(result).toEqual(ApiResponseDto.success(mockLoginResponse, 'Đăng nhập thành công'));
    });
  });

  describe('createEmployee', () => {
    it('should create employee successfully', async () => {
      // Arrange
      const createEmployeeDto: CreateEmployeeDto = {
        fullName: 'New Employee',
        email: '<EMAIL>',
        phoneNumber: '0123456789',
        password: 'Password123!',
        address: 'New Address',
        roleIds: [1, 2]
      };

      const mockResult = {
        id: 1,
        fullName: 'New Employee',
        email: '<EMAIL>',
        phoneNumber: '0123456789',
        address: 'New Address',
        createdAt: 1682506092000,
        updatedAt: 1682506092000,
        enable: true
      };

      employeeService.createEmployee.mockResolvedValue(mockResult);

      // Act
      const result = await controller.createEmployee(createEmployeeDto);

      // Assert
      expect(employeeService.createEmployee).toHaveBeenCalledWith(createEmployeeDto);
      expect(result).toEqual(ApiResponseDto.success(mockResult, 'Tạo nhân viên thành công'));
    });

    it('should create employee with avatar upload URL', async () => {
      // Arrange
      const createEmployeeDto: CreateEmployeeDto = {
        fullName: 'New Employee',
        email: '<EMAIL>',
        phoneNumber: '0123456789',
        password: 'Password123!',
        address: 'New Address',
        roleIds: [1, 2],
        avatarImageType: 'image/jpeg',
        avatarMaxSize: 2097152
      };

      const mockResult = {
        id: 1,
        fullName: 'New Employee',
        email: '<EMAIL>',
        phoneNumber: '0123456789',
        address: 'New Address',
        createdAt: 1682506092000,
        updatedAt: 1682506092000,
        enable: true,
        avatarUploadUrl: 'presigned-url',
        avatarKey: 'employee-avatars/1/avatar-123.jpg',
        avatarUrlExpiresAt: 1682506392000
      };

      employeeService.createEmployee.mockResolvedValue(mockResult);

      // Act
      const result = await controller.createEmployee(createEmployeeDto);

      // Assert
      expect(employeeService.createEmployee).toHaveBeenCalledWith(createEmployeeDto);
      expect(result).toEqual(ApiResponseDto.success(mockResult, 'Tạo nhân viên thành công'));
    });
  });

  describe('createAvatarUploadUrl', () => {
    it('should create avatar upload URL successfully', async () => {
      // Arrange
      const employeeId = 1;
      const avatarUploadDto: EmployeeAvatarUploadDto = {
        imageType: ImageTypeEnum.JPEG,
        maxSize: FileSizeEnum.TWO_MB
      };

      const mockUploadUrlResponse = {
        uploadUrl: 'presigned-url',
        avatarKey: 'avatar-key',
        expiresIn: TimeIntervalEnum.FIVE_MINUTES
      };

      employeeService.createAvatarUploadUrl.mockResolvedValue(mockUploadUrlResponse);

      // Act
      const result = await controller.createAvatarUploadUrl(employeeId, avatarUploadDto);

      // Assert
      expect(employeeService.createAvatarUploadUrl).toHaveBeenCalledWith(employeeId, avatarUploadDto);
      expect(result).toEqual(ApiResponseDto.success(mockUploadUrlResponse, 'Tạo URL tải lên avatar thành công'));
    });
  });

  describe('updateAvatar', () => {
    it('should update avatar successfully', async () => {
      // Arrange
      const employeeId = 1;
      const updateAvatarDto: UpdateEmployeeAvatarDto = {
        avatarKey: 'new-avatar-key'
      };

      const updatedEmployee = { ...mockEmployee, avatar: 'new-avatar-key' };
      employeeService.updateAvatar.mockResolvedValue(updatedEmployee);

      // Act
      const result = await controller.updateAvatar(employeeId, updateAvatarDto);

      // Assert
      expect(employeeService.updateAvatar).toHaveBeenCalledWith(employeeId, updateAvatarDto);
      expect(result).toEqual(ApiResponseDto.success(updatedEmployee, 'Cập nhật avatar thành công'));
    });
  });

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      // Arrange
      const employeeId = 1;
      const changePasswordDto: ChangeEmployeePasswordDto = {
        newPassword: 'NewPassword123!'
      };

      const changePasswordResponse = { message: 'Đổi mật khẩu thành công' };
      employeeService.changePassword.mockResolvedValue(changePasswordResponse);

      // Act
      const result = await controller.changePassword(employeeId, changePasswordDto);

      // Assert
      expect(employeeService.changePassword).toHaveBeenCalledWith(employeeId, changePasswordDto);
      expect(result).toEqual(ApiResponseDto.success(changePasswordResponse, 'Đổi mật khẩu thành công'));
    });
  });

  describe('assignRoles', () => {
    it('should assign roles successfully', async () => {
      // Arrange
      const employeeId = 1;
      const assignRoleDto: AssignEmployeeRoleDto = {
        roleIds: [1, 2, 3]
      };

      employeeService.assignRoles.mockResolvedValue(mockEmployee);

      // Act
      const result = await controller.assignRoles(employeeId, assignRoleDto);

      // Assert
      expect(employeeService.assignRoles).toHaveBeenCalledWith(employeeId, assignRoleDto);
      expect(result).toEqual(ApiResponseDto.success(mockEmployee, 'Gán vai trò thành công'));
    });
  });

  describe('getEmployeeRoles', () => {
    it('should get employee roles successfully', async () => {
      // Arrange
      const employeeId = 1;
      const mockRoles = [
        { id: 1, name: 'Admin', description: 'Administrator', permissions: [] },
        { id: 2, name: 'Editor', description: 'Content Editor', permissions: [] }
      ];

      employeeService.getEmployeeRoles.mockResolvedValue(mockRoles);

      // Act
      const result = await controller.getEmployeeRoles(employeeId);

      // Assert
      expect(employeeService.getEmployeeRoles).toHaveBeenCalledWith(employeeId);
      expect(result).toEqual(ApiResponseDto.success(mockRoles, 'Lấy danh sách vai trò thành công'));
    });
  });
});
