import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin quyền trong phản hồi
 */
export class PermissionResponseDto {
  @ApiProperty({
    description: 'ID của quyền',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Module của quyền',
    example: 'users'
  })
  module: string;

  @ApiProperty({
    description: 'Hành động của quyền',
    example: 'read'
  })
  action: string;

  @ApiProperty({
    description: 'Mô tả của quyền',
    example: 'Xem danh sách người dùng'
  })
  description: string;
}

/**
 * DTO cho thông tin vai trò trong phản hồi
 */
export class EmployeeRoleResponseDto {
  @ApiProperty({
    description: 'ID của vai trò',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên vai trò',
    example: 'Admin'
  })
  name: string;

  @ApiProperty({
    description: 'Mã vai trò',
    example: 'admin'
  })
  code: string;

  @ApiProperty({
    description: 'Mô tả vai trò',
    example: 'Quyền quản trị viên'
  })
  description: string;

  @ApiProperty({
    description: 'Danh sách quyền của vai trò',
    type: [PermissionResponseDto]
  })
  permissions: PermissionResponseDto[];
}
