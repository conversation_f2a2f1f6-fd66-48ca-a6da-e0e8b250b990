import { IsNotEmpty, IsOptional, IsString, Length, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc tạo tag mới
 */
export class CreateTagDto {
  /**
   * Tên tag
   * @example "Khách hàng VIP"
   */
  @ApiProperty({
    description: 'Tên tag',
    example: 'Khách hàng VIP',
  })
  @IsNotEmpty({ message: 'Tên tag không được để trống' })
  @IsString({ message: 'Tên tag phải là chuỗi' })
  @Length(1, 255, { message: 'Tên tag phải từ 1 đến 255 ký tự' })
  name: string;

  /**
   * Mã màu của tag (định dạng HEX)
   * @example "#FF5733"
   */
  @ApiProperty({
    description: 'Mã màu của tag (định dạng HEX)',
    example: '#FF5733',
  })
  @IsOptional()
  @IsString({ message: '<PERSON><PERSON> màu phải là chuỗi' })
  @Matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {
    message: 'Mã màu phải có định dạng HEX hợp lệ (ví dụ: #FF5733)',
  })
  color?: string;
}
