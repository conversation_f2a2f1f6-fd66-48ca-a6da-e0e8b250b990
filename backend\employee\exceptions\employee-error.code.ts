import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến module Employee (15000-15099)
 */
export const EMPLOYEE_ERROR_CODES = {
  // ===== NHÂN VIÊN (15000-15019) =====
  /**
   * Lỗi khi không tìm thấy nhân viên
   */
  EMPLOYEE_NOT_FOUND: new ErrorCode(
    15000,
    'Không tìm thấy nhân viên',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi email đã tồn tại
   */
  EMAIL_ALREADY_EXISTS: new ErrorCode(
    15001,
    'Email đã được sử dụng',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi tạo nhân viên thất bại
   */
  EMPLOYEE_CREATION_FAILED: new ErrorCode(
    15002,
    'Tạ<PERSON> nhân viên thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật nhân viên thất bại
   */
  EMPLOYEE_UPDATE_FAILED: new ErrorCode(
    15003,
    'Cập nhật nhân viên thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa nhân viên thất bại
   */
  EMPLOYEE_DELETE_FAILED: new ErrorCode(
    15004,
    'Xóa nhân viên thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi nhân viên bị vô hiệu hóa
   */
  EMPLOYEE_DISABLED: new ErrorCode(
    15005,
    'Tài khoản nhân viên đã bị vô hiệu hóa',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi mật khẩu không đủ mạnh
   */
  PASSWORD_TOO_WEAK: new ErrorCode(
    15006,
    'Mật khẩu không đủ mạnh',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi đổi mật khẩu thất bại
   */
  PASSWORD_CHANGE_FAILED: new ErrorCode(
    15007,
    'Đổi mật khẩu thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== VAI TRÒ VÀ QUYỀN HẠN (15020-15039) =====
  /**
   * Lỗi khi không tìm thấy vai trò
   */
  ROLE_NOT_FOUND: new ErrorCode(
    15020,
    'Không tìm thấy vai trò',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tạo vai trò thất bại
   */
  ROLE_CREATION_FAILED: new ErrorCode(
    15021,
    'Tạo vai trò thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật vai trò thất bại
   */
  ROLE_UPDATE_FAILED: new ErrorCode(
    15022,
    'Cập nhật vai trò thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa vai trò thất bại
   */
  ROLE_DELETE_FAILED: new ErrorCode(
    15023,
    'Xóa vai trò thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi gán vai trò thất bại
   */
  ROLE_ASSIGNMENT_FAILED: new ErrorCode(
    15024,
    'Gán vai trò thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== AVATAR (15040-15059) =====
  /**
   * Lỗi khi tạo URL tải lên avatar thất bại
   */
  AVATAR_URL_CREATION_FAILED: new ErrorCode(
    15040,
    'Tạo URL tải lên avatar thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật avatar thất bại
   */
  AVATAR_UPDATE_FAILED: new ErrorCode(
    15041,
    'Cập nhật avatar thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi loại hình ảnh không hợp lệ
   */
  INVALID_IMAGE_TYPE: new ErrorCode(
    15042,
    'Loại hình ảnh không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi số điện thoại đã tồn tại
   */
  PHONE_NUMBER_ALREADY_EXISTS: new ErrorCode(
    15043,
    'Số điện thoại đã được sử dụng',
    HttpStatus.CONFLICT,
  ),
};
