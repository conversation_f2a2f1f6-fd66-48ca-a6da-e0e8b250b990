import { Injectable, NotFoundException } from '@nestjs/common';
import { EmployeeRoleRepository, PermissionRepository } from '@modules/employee/repositories';
import { EmployeeRole, Permission } from '@modules/employee/entities';
import { AssignRolePermissionsDto } from '../dto/assign-role-permissions.dto';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý logic liên quan đến role của nhân viên
 */
@Injectable()
export class EmployeeRoleService {
  constructor(
    private readonly employeeRoleRepository: EmployeeRoleRepository,
    private readonly permissionRepository: PermissionRepository,
  ) {}

  /**
   * L<PERSON>y tất cả vai trò
   * @returns Danh sách tất cả vai trò
   */
  async getAllRoles(): Promise<EmployeeRole[]> {
    return this.employeeRoleRepository.findAll();
  }

  /**
   * Lấy vai trò theo ID
   * @param id ID của vai trò
   * @returns Vai trò
   */
  async getRoleById(id: number): Promise<EmployeeRole> {
    return this.employeeRoleRepository.findById(id);
  }

  /**
   * Lấy tất cả quyền
   * @returns Danh sách tất cả quyền
   */
  async getAllPermissions(): Promise<Permission[]> {
    return this.permissionRepository.findAll();
  }

  /**
   * Lấy tất cả quyền của vai trò
   * @param roleId ID của vai trò
   * @returns Danh sách các quyền của vai trò
   */
  async getRolePermissions(roleId: number): Promise<Permission[]> {
    return this.employeeRoleRepository.findPermissionsByRoleId(roleId);
  }

  /**
   * Gán quyền cho vai trò
   * @param roleId ID của vai trò
   * @param dto DTO chứa danh sách ID của các quyền
   * @returns Vai trò đã được cập nhật với các quyền mới
   */
  @Transactional()
  async assignPermissionsToRole(roleId: number, dto: AssignRolePermissionsDto): Promise<EmployeeRole> {
    // Kiểm tra vai trò có tồn tại không
    const role = await this.employeeRoleRepository.findById(roleId);
    if (!role) {
      throw new NotFoundException(`Vai trò với ID "${roleId}" không tồn tại`);
    }

    // Gán quyền cho vai trò
    return this.employeeRoleRepository.assignPermissionsToRole(roleId, dto.permissionIds);
  }
}