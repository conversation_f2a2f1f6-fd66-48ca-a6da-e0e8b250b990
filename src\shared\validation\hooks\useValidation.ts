import { useState, useCallback, useRef, useEffect } from 'react';
import {
  ValidationRule,
  ValidationResult,
  ValidationContext,
  ValidationState,
  UseValidationOptions,
  ValidationError,
  ValidationWarning,
  ValidationInfo
} from '../types';
// import { defaultMessageProvider } from '../messages';

/**
 * Enhanced validation hook with comprehensive features
 */
export const useValidation = (
  rules: ValidationRule[] = [],
  options: UseValidationOptions = {}
) => {
  const {
    debounceMs = 300,
    stopOnFirstError = false,
  } = options;

  // Validation state
  const [validationState, setValidationState] = useState<ValidationState>({
    isValidating: false,
    isValid: true,
    errors: {},
    warnings: {},
    infos: {},
    touchedFields: new Set(),
    validatedFields: new Set(),
  });

  // Refs for managing async operations
  const debounceTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());
  // const _validationPromises = useRef<Map<string, Promise<ValidationResult>>>(new Map());

  /**
   * Clear debounce timeout for a field
   */
  const clearDebounceTimeout = useCallback((field: string) => {
    const timeout = debounceTimeouts.current.get(field);
    if (timeout) {
      clearTimeout(timeout);
      debounceTimeouts.current.delete(field);
    }
  }, []);

  /**
   * Run validation rules for a field
   */
  const runValidation = useCallback(async (
    field: string,
    value: unknown,
    formValues: Record<string, unknown> = {},
    immediate = false
  ): Promise<ValidationResult> => {
    const context: ValidationContext = {
      field,
      formValues,
      meta: { immediate },
    };

    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const infos: ValidationInfo[] = [];

    // Filter rules for this field
    const fieldRules = rules.filter(rule => {
      if (typeof rule.enabled === 'function') {
        return rule.enabled(context);
      }
      return rule.enabled !== false;
    });

    // Run synchronous validations first
    for (const rule of fieldRules) {
      try {
        const result = await Promise.resolve(rule.validator(value, context));

        errors.push(...result.errors);
        warnings.push(...result.warnings);
        infos.push(...result.infos);

        // Stop on first error if configured
        if (stopOnFirstError && result.errors.length > 0) {
          break;
        }
      } catch (error) {
        console.error(`Validation rule "${rule.name}" failed:`, error);
        errors.push({
          field,
          message: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          code: 'VALIDATION_ERROR',
          severity: 'error',
        });
      }
    }

    const isValid = errors.length === 0;

    return {
      isValid,
      errors,
      warnings,
      infos,
    };
  }, [rules, stopOnFirstError]);

  /**
   * Validate a single field
   */
  const validateField = useCallback(async (
    field: string,
    value: unknown,
    formValues: Record<string, unknown> = {},
    immediate = false
  ): Promise<ValidationResult> => {
    // Clear existing debounce timeout
    clearDebounceTimeout(field);

    // Set validating state
    setValidationState(prev => ({
      ...prev,
      isValidating: true,
    }));

    const performValidation = async (): Promise<ValidationResult> => {
      try {
        const result = await runValidation(field, value, formValues, immediate);

        // Update validation state
        setValidationState(prev => {
          const newErrors = { ...prev.errors };
          const newWarnings = { ...prev.warnings };
          const newInfos = { ...prev.infos };
          const newTouchedFields = new Set(prev.touchedFields);
          const newValidatedFields = new Set(prev.validatedFields);

          // Update field-specific results
          if (result.errors.length > 0) {
            newErrors[field] = result.errors;
          } else {
            delete newErrors[field];
          }

          if (result.warnings.length > 0) {
            newWarnings[field] = result.warnings;
          } else {
            delete newWarnings[field];
          }

          if (result.infos.length > 0) {
            newInfos[field] = result.infos;
          } else {
            delete newInfos[field];
          }

          newTouchedFields.add(field);
          newValidatedFields.add(field);

          // Calculate overall validity
          const hasErrors = Object.keys(newErrors).length > 0;

          return {
            isValidating: false,
            isValid: !hasErrors,
            errors: newErrors,
            warnings: newWarnings,
            infos: newInfos,
            touchedFields: newTouchedFields,
            validatedFields: newValidatedFields,
          };
        });

        return result;
      } catch (error) {
        console.error(`Field validation failed for "${field}":`, error);

        const errorResult: ValidationResult = {
          isValid: false,
          errors: [{
            field,
            message: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            code: 'VALIDATION_FAILED',
            severity: 'error',
          }],
          warnings: [],
          infos: [],
        };

        setValidationState(prev => ({
          ...prev,
          isValidating: false,
          errors: {
            ...prev.errors,
            [field]: errorResult.errors,
          },
        }));

        return errorResult;
      }
    };

    if (immediate || debounceMs <= 0) {
      return performValidation();
    } else {
      // Debounced validation
      return new Promise((resolve) => {
        const timeout = setTimeout(async () => {
          const result = await performValidation();
          resolve(result);
        }, debounceMs);

        debounceTimeouts.current.set(field, timeout);
      });
    }
  }, [runValidation, clearDebounceTimeout, debounceMs]);

  /**
   * Validate multiple fields
   */
  const validateFields = useCallback(async (
    fieldsToValidate: Record<string, unknown>,
    formValues: Record<string, unknown> = {},
    immediate = false
  ): Promise<Record<string, ValidationResult>> => {
    const results: Record<string, ValidationResult> = {};

    // Run validations in parallel
    const validationPromises = Object.entries(fieldsToValidate).map(async ([field, value]) => {
      const result = await validateField(field, value, formValues, immediate);
      results[field] = result;
      return { field, result };
    });

    await Promise.all(validationPromises);

    return results;
  }, [validateField]);

  /**
   * Validate entire form
   */
  const validateForm = useCallback(async (
    formValues: Record<string, unknown>,
    immediate = true
  ): Promise<ValidationResult> => {
    const results = await validateFields(formValues, formValues, immediate);

    const allErrors: ValidationError[] = [];
    const allWarnings: ValidationWarning[] = [];
    const allInfos: ValidationInfo[] = [];

    Object.values(results).forEach(result => {
      allErrors.push(...result.errors);
      allWarnings.push(...result.warnings);
      allInfos.push(...result.infos);
    });

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings,
      infos: allInfos,
    };
  }, [validateFields]);

  /**
   * Clear validation for a field
   */
  const clearFieldValidation = useCallback((field: string) => {
    clearDebounceTimeout(field);

    setValidationState(prev => {
      const newErrors = { ...prev.errors };
      const newWarnings = { ...prev.warnings };
      const newInfos = { ...prev.infos };
      const newValidatedFields = new Set(prev.validatedFields);

      delete newErrors[field];
      delete newWarnings[field];
      delete newInfos[field];
      newValidatedFields.delete(field);

      return {
        ...prev,
        errors: newErrors,
        warnings: newWarnings,
        infos: newInfos,
        validatedFields: newValidatedFields,
        isValid: Object.keys(newErrors).length === 0,
      };
    });
  }, [clearDebounceTimeout]);

  /**
   * Clear all validation
   */
  const clearValidation = useCallback(() => {
    // Clear all debounce timeouts
    debounceTimeouts.current.forEach(timeout => clearTimeout(timeout));
    debounceTimeouts.current.clear();

    setValidationState({
      isValidating: false,
      isValid: true,
      errors: {},
      warnings: {},
      infos: {},
      touchedFields: new Set(),
      validatedFields: new Set(),
    });
  }, []);

  /**
   * Mark field as touched
   */
  const touchField = useCallback((field: string) => {
    setValidationState(prev => ({
      ...prev,
      touchedFields: new Set([...prev.touchedFields, field]),
    }));
  }, []);

  /**
   * Get validation result for a specific field
   */
  const getFieldValidation = useCallback((field: string) => {
    return {
      errors: validationState.errors[field] || [],
      warnings: validationState.warnings[field] || [],
      infos: validationState.infos[field] || [],
      isValid: !validationState.errors[field] || validationState.errors[field].length === 0,
      isTouched: validationState.touchedFields.has(field),
      isValidated: validationState.validatedFields.has(field),
    };
  }, [validationState]);

  /**
   * Check if form has any errors
   */
  const hasErrors = useCallback(() => {
    return Object.keys(validationState.errors).length > 0;
  }, [validationState.errors]);

  /**
   * Check if form has any warnings
   */
  const hasWarnings = useCallback(() => {
    return Object.keys(validationState.warnings).length > 0;
  }, [validationState.warnings]);

  /**
   * Get all errors as flat array
   */
  const getAllErrors = useCallback(() => {
    return Object.values(validationState.errors).flat();
  }, [validationState.errors]);

  /**
   * Get all warnings as flat array
   */
  const getAllWarnings = useCallback(() => {
    return Object.values(validationState.warnings).flat();
  }, [validationState.warnings]);

  // Cleanup on unmount
  useEffect(() => {
    const timeouts = debounceTimeouts.current;
    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
      timeouts.clear();
    };
  }, []);

  return {
    // State
    validationState,
    isValidating: validationState.isValidating,
    isValid: validationState.isValid,

    // Validation methods
    validateField,
    validateFields,
    validateForm,

    // Field management
    clearFieldValidation,
    clearValidation,
    touchField,
    getFieldValidation,

    // Utility methods
    hasErrors,
    hasWarnings,
    getAllErrors,
    getAllWarnings,
  };
};
