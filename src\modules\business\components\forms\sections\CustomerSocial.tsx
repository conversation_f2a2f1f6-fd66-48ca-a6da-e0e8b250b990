import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, Icon, Button, Input, FormItem } from '@/shared/components/common';
import { CustomerDetailData } from './types';

interface CustomerSocialProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị thông tin mạng xã hội của khách hàng
 */
const CustomerSocial: React.FC<CustomerSocialProps> = ({ customer }) => {
  const { t } = useTranslation('business');
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    facebook: customer.socialProfiles?.facebook || '',
    twitter: customer.socialProfiles?.twitter || '',
    linkedin: customer.socialProfiles?.linkedin || '',
    zalo: customer.socialProfiles?.zalo || '',
    website: customer.socialProfiles?.website || '',
  });

  // Social platform configurations
  const socialPlatforms = [
    {
      key: 'facebook',
      name: 'Facebook',
      icon: 'facebook' as const,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      placeholder: 'https://facebook.com/username',
    },
    {
      key: 'twitter',
      name: 'Twitter',
      icon: 'twitter' as const,
      color: 'text-blue-400',
      bgColor: 'bg-blue-50',
      placeholder: 'https://twitter.com/username',
    },
    {
      key: 'linkedin',
      name: 'LinkedIn',
      icon: 'linkedin' as const,
      color: 'text-blue-700',
      bgColor: 'bg-blue-50',
      placeholder: 'https://linkedin.com/in/username',
    },
    {
      key: 'zalo',
      name: 'Zalo',
      icon: 'message-circle' as const,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      placeholder: 'Zalo ID hoặc số điện thoại',
    },
    {
      key: 'website',
      name: 'Website',
      icon: 'globe' as const,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      placeholder: 'https://website.com',
    },
  ];

  // Handle input change
  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle save
  const handleSave = () => {
    // TODO: Implement save logic
    console.log('Save social profiles:', formData);
    setIsEditing(false);
  };

  // Handle cancel
  const handleCancel = () => {
    setFormData({
      facebook: customer.socialProfiles?.facebook || '',
      twitter: customer.socialProfiles?.twitter || '',
      linkedin: customer.socialProfiles?.linkedin || '',
      zalo: customer.socialProfiles?.zalo || '',
      website: customer.socialProfiles?.website || '',
    });
    setIsEditing(false);
  };

  // Handle social link click
  const handleSocialClick = (url: string) => {
    if (url) {
      window.open(url.startsWith('http') ? url : `https://${url}`, '_blank');
    }
  };

  // Check if customer has any social profiles
  const hasSocialProfiles = customer.socialProfiles &&
    Object.values(customer.socialProfiles).some(profile => profile);

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="text-foreground">
            {t('customer.detail.social')}
          </Typography>
          <div className="flex items-center space-x-2">
            {hasSocialProfiles && !isEditing && (
              <Typography variant="body2" className="text-muted">
                {Object.values(customer.socialProfiles || {}).filter(Boolean).length} {t('customer.detail.platforms')}
              </Typography>
            )}
            {!isEditing ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                <Icon name="edit" size="sm" className="mr-1" />
                {t('common.edit')}
              </Button>
            ) : (
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                >
                  {t('common.cancel')}
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleSave}
                >
                  {t('common.save')}
                </Button>
              </div>
            )}
          </div>
        </div>
      }
      defaultOpen={false}
    >
      {isEditing ? (
        <div className="space-y-4">
          <Typography variant="body2" className="text-muted mb-4">
            {t('customer.social.editDescription')}
          </Typography>

          <div className="grid grid-cols-1 gap-4">
            {socialPlatforms.map((platform) => (
              <FormItem
                key={platform.key}
                label={platform.name}
                className="mb-0"
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full ${platform.bgColor}`}>
                    <Icon name={platform.icon} size="sm" className={platform.color} />
                  </div>
                  <div className="flex-1">
                    <Input
                      value={formData[platform.key as keyof typeof formData]}
                      onChange={(e) => handleInputChange(platform.key, e.target.value)}
                      placeholder={platform.placeholder}
                      className="w-full"
                    />
                  </div>
                </div>
              </FormItem>
            ))}
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {hasSocialProfiles ? (
            <div className="grid grid-cols-1 gap-4">
              {socialPlatforms.map((platform) => {
                const profileUrl = customer.socialProfiles?.[platform.key as keyof typeof customer.socialProfiles];

                if (!profileUrl) return null;

                return (
                  <div
                    key={platform.key}
                    className="flex items-center justify-between p-4 rounded-lg border hover:bg-muted/20 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-full ${platform.bgColor}`}>
                        <Icon name={platform.icon} size="md" className={platform.color} />
                      </div>
                      <div>
                        <Typography variant="body1" className="text-foreground font-medium">
                          {platform.name}
                        </Typography>
                        <Typography variant="body2" className="text-muted truncate max-w-64">
                          {profileUrl}
                        </Typography>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSocialClick(profileUrl)}
                      className="ml-2"
                    >
                      <Icon name="external-link" size="sm" />
                    </Button>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="mb-4">
                <Icon name="users" size="lg" className="text-muted mx-auto" />
              </div>
              <Typography variant="body1" className="text-muted mb-2">
                {t('customer.detail.noSocialProfiles')}
              </Typography>
              <Typography variant="body2" className="text-muted mb-4">
                {t('customer.detail.noSocialProfilesDesc')}
              </Typography>
              <Button
                variant="outline"
                onClick={() => setIsEditing(true)}
              >
                <Icon name="plus" size="sm" className="mr-1" />
                {t('customer.social.addProfiles')}
              </Button>
            </div>
          )}
        </div>
      )}
    </CollapsibleCard>
  );
};

export default CustomerSocial;
