import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, Container, Icon, Input, Loading } from '@/shared/components/common';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal/ConfirmDeleteModal';
import Pagination from '@/shared/components/common/Pagination/Pagination';
import React, { useEffect, useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { CreateTypeForm, TypeAgent, TypeAgentGrid } from '../components/agent-add';
import { CustomAgentFormData } from '../components/agent-add/CustomTypeAgentCard';
import { ConvertConfig, IntegrationConfig, IntegrationConfigData, ModelConfig, MultiAgentConfig, ProfileConfig, ResponseConfig, StrategyConfig } from '../components/agent-config';
import { useGetTypeAgentsWithService } from '../hooks/useAgentService';
import { useGetTypeAgentDetail } from '../hooks/useTypeAgent';
import {
    AgentConfigData,
    ConvertData,
    GetTypeAgentsQueryDto,
    ModelConfigData,
    MultiAgentConfigData,
    ProfileData,
    ResponseData,
    SortDirection,
    StrategyData,
    TypeAgentSortBy,
    TypeProviderEnum
} from '../types';
import { mapTypeAgentsFromApi } from '../utils/api-mappers';
import { t } from 'i18next';




/**
 * Trang tạo Agent mới
 */
const AgentCreatePage: React.FC = () => {
    // State cho phân trang và filter
    const [page, setPage] = useState<number>(1);
    const [limit, setLimit] = useState<number>(7);
    const [search, setSearch] = useState<string>('');
    const [isSystem, setIsSystem] = useState<boolean | undefined>(undefined); // Mặc định lấy tất cả
    const [sortBy, setSortBy] = useState<TypeAgentSortBy>(TypeAgentSortBy.CREATED_AT);
    const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);
    const [totalItems, setTotalItems] = useState<number>(0);
    const navigate = useNavigate();

    // State để kiểm soát hiển thị màn hình cấu hình
    const [showConfigScreen, setShowConfigScreen] = useState<boolean>(false);

    // State để lưu dữ liệu cấu hình agent
    const [agentData, setAgentData] = useState<AgentConfigData & {
        // Các cờ để xác định component nào sẽ được hiển thị
        hasProfile?: boolean;
        hasModel?: boolean;
        hasIntegrations?: boolean;
        hasStrategy?: boolean;
        hasConvert?: boolean;
        hasResponse?: boolean;
        hasMultiAgent?: boolean;
    }>({
        name: 'Tên agent',
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
        // Mặc định hiển thị tất cả các component
        hasProfile: true,
        hasModel: true,
        hasIntegrations: true,
        hasStrategy: true,
        hasConvert: true,
        hasResponse: true,
        hasMultiAgent: false,
        profile: {
            birthDate: '01/01/2000',
            gender: 'Nam',
            language: 'Tiếng Việt',
            education: 'Đại học',
            country: 'Việt Nam',
            position: 'Sales Assistant',
            skills: ['Tư vấn', 'Hỗ trợ khách hàng'],
            personality: 'Thân thiện, nhiệt tình, chuyên nghiệp',
        },
        modelConfig: {
            provider: TypeProviderEnum.OPENAI,
            modelId: 'gpt-4',
            vectorStore: 'pinecone',
            maxTokens: 1229,
            temperature: 1,
            topP: 1,
            topK: 100
        },
        integrations: {
            integrations: [
            ]
        },
        strategy: {
            strategyId: 'strategy1',
            steps: {
                step1: '',
                step2: '',
                step3: '',
                step4: ''
            }
        },
        response: {
            media: [],
            urls: [],
            products: []
        },
        convert: {
            fields: [
                {
                    id: 'field-1',
                    name: 'email',
                    description: 'Lấy tất cả email của người dùng',
                    enabled: true,
                    type: 'email',
                    required: true
                },
                {
                    id: 'field-2',
                    name: 'phone',
                    description: 'Lấy số điện thoại của người dùng',
                    enabled: true,
                    type: 'phone',
                    required: true
                },
                {
                    id: 'field-3',
                    name: 'name',
                    description: 'Lấy họ tên đầy đủ của người dùng',
                    enabled: true,
                    type: 'name',
                    required: true
                }
            ]
        },
        multiAgent: {
            agents: []
        }
    });

    // State để lưu loại agent được chọn
    const [selectedAgentType, setSelectedAgentType] = useState<number | null>(null);

    // State để hiển thị form tạo custom agent
    const [showCustomForm, setShowCustomForm] = useState<boolean>(false);

    // State để quản lý edit mode cho custom agent
    const [customAgentEditData, setCustomAgentEditData] = useState<TypeAgent | null>(null);
    const [customAgentMode, setCustomAgentMode] = useState<'create' | 'edit'>('create');

    // State để quản lý việc hiển thị CreateTypeForm cho view/edit mode
    const [showTypeAgentForm, setShowTypeAgentForm] = useState<boolean>(false);
    const [typeAgentFormMode, setTypeAgentFormMode] = useState<'create' | 'view' | 'edit'>('create');
    const [selectedTypeAgentId, setSelectedTypeAgentId] = useState<number | null>(null);
    const [typeAgentFormData, setTypeAgentFormData] = useState<CustomAgentFormData | null>(null);

    // State để quản lý delete confirmation modal
    const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
    const [deleteAgentData, setDeleteAgentData] = useState<TypeAgent | null>(null);

    // State để lưu danh sách agent từ API
    const [agentTypes, setAgentTypes] = useState<TypeAgent[]>([]);

    // Debug agentTypes
    useEffect(() => {
        console.log('AgentTypes State Updated:', agentTypes);
        console.log('AgentTypes Length:', agentTypes.length);
    }, [agentTypes]);

    // Tạo query params cho API
    const queryParams: GetTypeAgentsQueryDto = useMemo(() => ({
        page,
        limit,
        search: search || undefined,
        sortBy,
        sortDirection,
        isSystem,
    }), [page, limit, search, sortBy, sortDirection, isSystem]);

    // Debug log để kiểm tra filter
    useEffect(() => {
        console.log('Filter State Changed:', {
            isSystem,
            sortBy,
            sortDirection,
            search,
            page,
            limit
        });
        console.log('Query Params:', queryParams);
    }, [isSystem, sortBy, sortDirection, search, page, limit, queryParams]);

    // Gọi API để lấy danh sách type agents
    const {
        data: typeAgentsResponse,
        isLoading: isLoadingTypeAgents,
        error: typeAgentsError,
        refetch: refetchTypeAgents
    } = useGetTypeAgentsWithService(queryParams);

    // Gọi API để lấy chi tiết type agent khi cần
    const {
        data: typeAgentDetailResponse,
        isLoading: isLoadingTypeAgentDetail,
    } = useGetTypeAgentDetail(selectedTypeAgentId || undefined);

    // Cập nhật state khi có dữ liệu từ API
    useEffect(() => {
        console.log('API Response:', typeAgentsResponse);
        if (typeAgentsResponse?.result?.items) {
            console.log('Raw API Items:', typeAgentsResponse.result.items);
            // Chuyển đổi dữ liệu từ API sang format frontend
            const mappedAgents = mapTypeAgentsFromApi(typeAgentsResponse.result.items);
            console.log('Mapped Agents:', mappedAgents);
            setAgentTypes(mappedAgents);
            setTotalItems(typeAgentsResponse.result.meta.totalItems);
        }
    }, [typeAgentsResponse]);

    // Cập nhật form data khi có chi tiết type agent từ API
    useEffect(() => {
        if (typeAgentDetailResponse?.result && selectedTypeAgentId) {
            const detail = typeAgentDetailResponse.result;
            setTypeAgentFormData({
                name: detail.name,
                description: detail.description || '',
                config: {
                    hasProfile: detail.config.hasProfile ?? true,
                    hasOutput: detail.config.hasOutput ?? true,
                    hasConversion: detail.config.hasConversion ?? false,
                    hasResources: detail.config.hasResources ?? true,
                    hasStrategy: detail.config.hasStrategy ?? false,
                    hasMultiAgent: detail.config.hasMultiAgent ?? false
                },
                groupToolIds: detail.groupTools?.map(gt => gt.id) || []
            });
        }
    }, [typeAgentDetailResponse, selectedTypeAgentId]);

    // Xử lý khi thay đổi trang
    const handlePageChange = (newPage: number) => {
        setPage(newPage);
    };

    // Xử lý khi thay đổi số lượng hiển thị trên một trang
    const handleLimitChange = (newValue: number) => {
        setLimit(newValue);
        setPage(1); // Reset về trang 1 khi thay đổi limit
    };

    // Xử lý khi tìm kiếm
    const handleSearch = (value: string) => {
        setSearch(value);
        setPage(1); // Reset về trang 1 khi tìm kiếm
    };

    // Tạo filter items theo cách của BlogListPage
    const filterItems = [
        {
            id: 'all-agents',
            label: isSystem === undefined ? '✓ Tất cả loại' : 'Tất cả loại',
            icon: 'users',
            onClick: () => {
                setIsSystem(undefined);
                setPage(1);
            }
        },
        {
            id: 'system-agents',
            label: isSystem === true ? '✓ System Agents' : 'System Agents',
            icon: 'shield',
            onClick: () => {
                setIsSystem(true);
                setPage(1);
            }
        },
        {
            id: 'user-agents',
            label: isSystem === false ? '✓ User Agents' : 'User Agents',
            icon: 'user',
            onClick: () => {
                setIsSystem(false);
                setPage(1);
            }
        },
        {
            id: 'divider-1',
            divider: true
        },
        {
            id: 'sort-name',
            label: sortBy === TypeAgentSortBy.NAME ? '✓ Sắp xếp theo tên' : 'Sắp xếp theo tên',
            icon: 'sort-alpha',
            onClick: () => {
                setSortBy(TypeAgentSortBy.NAME);
                setPage(1);
            }
        },
        {
            id: 'sort-date',
            label: sortBy === TypeAgentSortBy.CREATED_AT ? '✓ Sắp xếp theo ngày' : 'Sắp xếp theo ngày',
            icon: 'calendar',
            onClick: () => {
                setSortBy(TypeAgentSortBy.CREATED_AT);
                setPage(1);
            }
        },
        {
            id: 'divider-2',
            divider: true
        },
        {
            id: 'order-asc',
            label: sortDirection === SortDirection.ASC ? '✓ Tăng dần' : 'Tăng dần',
            icon: 'arrow-up',
            onClick: () => {
                setSortDirection(SortDirection.ASC);
                setPage(1);
            }
        },
        {
            id: 'order-desc',
            label: sortDirection === SortDirection.DESC ? '✓ Giảm dần' : 'Giảm dần',
            icon: 'arrow-down',
            onClick: () => {
                setSortDirection(SortDirection.DESC);
                setPage(1);
            }
        },
        {
            id: 'divider-3',
            divider: true
        },
        {
            id: 'reset-filters',
            label: 'Đặt lại bộ lọc',
            icon: 'refresh-cw',
            onClick: () => {
                setIsSystem(undefined);
                setSortBy(TypeAgentSortBy.CREATED_AT);
                setSortDirection(SortDirection.DESC);
                setPage(1);
            }
        }
    ];

    // Xử lý khi chọn loại agent
    const handleAgentTypeSelect = (agentId: number) => {
        setSelectedAgentType(agentId);

        // Cập nhật thông tin agent dựa trên loại agent đã chọn
        const selectedAgent = agentTypes.find(agent => agent.id === agentId);
        if (selectedAgent) {
            setAgentData(prev => ({
                ...prev,
                // Cập nhật các cờ hiển thị component dựa trên config
                hasProfile: selectedAgent.config.hasProfile,
                hasModel: true, // Luôn có model config
                hasIntegrations: selectedAgent.config.hasResources,
                hasStrategy: selectedAgent.config.hasStrategy,
                hasConvert: selectedAgent.config.hasConversion,
                hasResponse: selectedAgent.config.hasOutput,
                hasMultiAgent: selectedAgent.config.hasMultiAgent,
                profile: {
                    ...prev.profile,
                    name: selectedAgent.name,
                    position: `${selectedAgent.name.replace(' Agent', '')} AI`,
                    personality: selectedAgent.id === 1 ? 'Thân thiện, nhiệt tình, chuyên nghiệp' :
                        selectedAgent.id === 2 ? 'Tỉ mỉ, logic, chính xác' :
                            selectedAgent.id === 3 ? 'Thân thiện, hài hước, gần gũi' :
                                selectedAgent.id === 4 ? 'Tò mò, phân tích, chi tiết' :
                                    'Chuyên nghiệp, hiệu quả, chính xác'
                }
            }));
        }

        // Hiển thị màn hình cấu hình
        setShowConfigScreen(true);
    };

    // Xử lý khi chọn custom agent
    const handleCustomAgentClick = () => {
        setSelectedAgentType(null);
        setShowCustomForm(true);

        // Đặt tất cả các cờ hiển thị component thành true
        setAgentData(prev => ({
            ...prev,
            hasProfile: true,
            hasModel: true,
            hasIntegrations: true,
            hasStrategy: true,
            hasConvert: true,
            hasResponse: true,
            hasMultiAgent: true
        }));
    };

    // Xử lý khi lưu custom agent
    const handleSaveCustomAgent = (data: CustomAgentFormData) => {
        // Tạm thời thêm vào state local để demo
        const newAgent: TypeAgent = {
            id: Date.now(), // Sử dụng timestamp làm ID tạm
            name: data.name,
            description: data.description,
            createdAt: Date.now(), // Timestamp number
            icon: "custom", // Icon mặc định cho custom agent
            isSystem: false, // Đánh dấu là custom agent (không phải system)
            // Config từ form
            config: data.config,
        };

        // Thêm agent mới vào danh sách (tạm thời)
        setAgentTypes([newAgent, ...agentTypes]);

        // Đóng form
        setShowCustomForm(false);
        setCustomAgentEditData(null);
        setCustomAgentMode('create');
    };

    // Xử lý khi hủy tạo custom agent
    const handleCancelCustomAgent = () => {
        setShowCustomForm(false);
        setCustomAgentEditData(null);
        setCustomAgentMode('create');
    };

    // Xử lý khi xem chi tiết type agent
    const handleViewAgent = (agentId: number) => {
        setSelectedTypeAgentId(agentId);
        setTypeAgentFormMode('view');
        setShowTypeAgentForm(true);
    };

    // Xử lý khi xóa type agent
    const handleDeleteAgent = (agentId: number) => {
        const agent = agentTypes.find(a => a.id === agentId);
        if (agent) {
            setDeleteAgentData(agent);
            setShowDeleteModal(true);
        }
    };

    // Xử lý xác nhận xóa type agent
    const handleConfirmDelete = () => {
        if (deleteAgentData) {
            // Xóa khỏi state local (trong thực tế sẽ gọi API)
            setAgentTypes(prev => prev.filter(agent => agent.id !== deleteAgentData.id));
            setShowDeleteModal(false);
            setDeleteAgentData(null);
        }
    };

    // Xử lý hủy xóa
    const handleCancelDelete = () => {
        setShowDeleteModal(false);
        setDeleteAgentData(null);
    };

    // Xử lý đóng CreateTypeForm
    const handleCloseTypeAgentForm = () => {
        setShowTypeAgentForm(false);
        setSelectedTypeAgentId(null);
        setTypeAgentFormData(null);
        setTypeAgentFormMode('create');
    };

    // Xử lý khi cập nhật profile
    const handleProfileUpdate = (profileData: ProfileData) => {
        setAgentData(prev => ({
            ...prev,
            profile: profileData
        }));
    };

    // Xử lý khi cập nhật cấu hình model
    const handleModelConfigUpdate = (modelConfigData: ModelConfigData) => {
        setAgentData(prev => ({
            ...prev,
            modelConfig: modelConfigData
        }));
    };

    // Xử lý khi cập nhật tích hợp
    const handleIntegrationsUpdate = (integrationsData: IntegrationConfigData) => {
        setAgentData(prev => ({
            ...prev,
            integrations: integrationsData
        }));
    };

    // Xử lý khi cập nhật chiến lược
    const handleStrategyUpdate = (strategyData: StrategyData) => {
        setAgentData(prev => ({
            ...prev,
            strategy: strategyData
        }));
    };

    // Xử lý khi cập nhật tài nguyên phản hồi
    const handleResponseUpdate = (responseData: ResponseData) => {
        setAgentData(prev => ({
            ...prev,
            response: responseData
        }));
    };

    // Xử lý khi cập nhật dữ liệu chuyển đổi
    const handleConvertUpdate = (convertData: ConvertData) => {
        setAgentData(prev => ({
            ...prev,
            convert: convertData
        }));
    };

    // Xử lý khi cập nhật multi-agent
    const handleMultiAgentUpdate = (multiAgentData: MultiAgentConfigData) => {
        setAgentData(prev => ({
            ...prev,
            multiAgent: multiAgentData
        }));
    };

    // Xử lý khi lưu cấu hình agent
    const handleSaveAgentConfig = () => {
        // Trong thực tế, đây sẽ là một API call
        console.log('Lưu cấu hình agent:', agentData);

        // Hiển thị thông báo thành công
        alert('Đã lưu cấu hình agent thành công!');

        // Quay lại màn hình chọn loại agent
        setShowConfigScreen(false);
    };

    // Xử lý khi quay lại màn chọn loại agent
    const handleBackToTypeSelection = () => {
        // Quay lại màn hình chọn loại agent
        setShowConfigScreen(false);
        setSelectedAgentType(null);
    };

    // Tham chiếu đến input file ẩn
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Xử lý khi nhấp vào overlay để tải lên avatar
    const handleAvatarUpload = () => {
        // Kích hoạt click vào input file ẩn
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    // Xử lý khi người dùng đã chọn file ảnh
    const handleAvatarFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            // Tạo URL tạm thời cho ảnh đã chọn
            const imageUrl = URL.createObjectURL(file);

            // Cập nhật avatar trong state
            setAgentData(prev => ({
                ...prev,
                avatar: imageUrl
            }));
        }
    };

    // Xử lý khi thay đổi input (tên agent)
    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = event.target;

        // Cập nhật giá trị trong state
        setAgentData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    return (
        <>
            {/* Hiển thị form Type Agent nếu được kích hoạt */}
            {showTypeAgentForm ? (
                <Container>
                    <div className="py-4 sm:py-6">
                        {/* Header với nút quay lại */}
                        <div className="flex items-center gap-2 mb-6">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleCloseTypeAgentForm}
                                className="flex items-center space-x-2"
                            >
                                <Icon name="arrow-left" size="sm" />
                                <span>Quay lại</span>
                            </Button>
                        </div>

                        {/* Form component */}
                        <div className="max-w-4xl mx-auto">
                            {isLoadingTypeAgentDetail ? (
                                <div className="flex justify-center items-center py-12">
                                    <div className="text-gray-500">Đang tải dữ liệu...</div>
                                </div>
                            ) : (
                                <CreateTypeForm
                                    onSave={handleSaveCustomAgent}
                                    onSuccess={handleCloseTypeAgentForm}
                                    initialData={typeAgentFormData || undefined}
                                    mode={typeAgentFormMode}
                                    typeAgentId={selectedTypeAgentId || undefined}
                                />
                            )}
                        </div>
                    </div>
                </Container>
            ) : !showConfigScreen ? (
                // Màn hình chọn loại agent
                <Container>
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-4 sm:mb-6">
                        <div className="flex items-center gap-2">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.history.back()}
                                className="flex items-center space-x-2"
                            >
                                <Icon name="arrow-left" size="sm" />
                                <span>{t('common.back', 'Quay lại')}</span>
                            </Button>
                            <h1 className="text-xl sm:text-2xl font-bold">
                                {`Chọn loại Agent `}
                            </h1>
                        </div>
                    </div>

                    {/* Form tạo custom agent - hiển thị ở phía trên */}
                    {showCustomForm && (
                        <Container>
                            <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600">
                                <div className="flex items-center justify-between mb-4">
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                        Tạo Agent tùy chỉnh
                                    </h3>
                                    <Button
                                        variant="ghost"
                                        onClick={handleCancelCustomAgent}
                                        className="p-1"
                                    >
                                        <Icon name="x" size="sm" />
                                    </Button>
                                </div>
                                <CreateTypeForm
                                    onSave={handleSaveCustomAgent}
                                    onSuccess={handleCancelCustomAgent}
                                    mode="create"
                                />
                            </div>
                        </Container>
                    )}
                    <Container>
                        <div className="py-4 sm:py-6">
                            <div className="p-3 sm:p-4">
                                <p className="text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 text-sm sm:text-base">
                                    Chọn loại agent phù hợp với nhu cầu của bạn. Mỗi loại agent có những khả năng và đặc điểm khác nhau.
                                </p>

                                {/* Phần tìm kiếm và lọc */}
                                <MenuIconBar
                                    onSearch={handleSearch}
                                    items={filterItems}
                                    showDateFilter={false}
                                    showColumnFilter={false}
                                />

                                {/* Danh sách agent */}
                                <div className="overflow-x-auto">
                                    {isLoadingTypeAgents ? (
                                        <div className="flex justify-center items-center py-12">
                                            <Loading size="lg" />
                                        </div>
                                    ) : typeAgentsError ? (
                                        <div className="text-center py-12">
                                            <p className="text-red-500 mb-4">
                                                Có lỗi xảy ra khi tải danh sách Type Agent
                                            </p>
                                            <Button
                                                variant="outline"
                                                onClick={() => refetchTypeAgents()}
                                            >
                                                Thử lại
                                            </Button>
                                        </div>
                                    ) : (
                                        <TypeAgentGrid
                                            agents={agentTypes}
                                            selectedAgentId={selectedAgentType}
                                            onSelectAgent={handleAgentTypeSelect}
                                            onCustomAgentClick={handleCustomAgentClick}
                                            onViewAgent={handleViewAgent}
                                            onDeleteAgent={handleDeleteAgent}
                                            showActions={true}
                                            customAgentEditData={customAgentEditData}
                                            customAgentMode={customAgentMode}
                                            onSaveCustomAgent={handleSaveCustomAgent}
                                        />
                                    )}
                                </div>

                                {/* Phân trang - chỉ hiển thị khi có dữ liệu */}
                                {!isLoadingTypeAgents && !typeAgentsError && agentTypes.length > 0 && (
                                    <div className="mt-6 flex justify-end">
                                        <Pagination
                                            currentPage={page}
                                            borderless={true}
                                            totalItems={totalItems}
                                            itemsPerPage={limit}
                                            onPageChange={handlePageChange}
                                            onItemsPerPageChange={handleLimitChange}
                                            itemsPerPageOptions={[7, 15, 23, 79]}
                                            showItemsPerPageSelector={true}
                                            showPageInfo={true}
                                            variant="compact"
                                        />
                                    </div>
                                )}
                            </div>
                        </div>
                    </Container>
                </Container>
            ) : (
                // Màn hình cấu hình agent
                <Container>
                    <div className="py-4 sm:py-6">
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-4 sm:gap-0">
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={handleBackToTypeSelection}
                                    className="flex items-center space-x-2"
                                >
                                    <Icon name="arrow-left" size="sm" />
                                    <span>{t('common.back', 'Quay lại')}</span>
                                </Button>
                                <h1 className="text-xl sm:text-2xl font-bold">
                                    {`Cấu hình `}
                                </h1>
                            </div>
                            <div className="flex space-x-2 sm:space-x-4 w-full sm:w-auto">
                                <Button
                                    variant="secondary"
                                    onClick={() => navigate('/ai-agents')}
                                    className="flex-1 sm:flex-none"
                                >
                                    Hủy
                                </Button>
                                <Button
                                    variant="primary"
                                    onClick={handleSaveAgentConfig}
                                    className="flex-1 sm:flex-none"
                                >
                                    Lưu
                                </Button>
                            </div>
                        </div>
                        <div className="mb-4 sm:mb-6 p-3 sm:p-4 rounded-lg">
                            {/* Container flex để căn giữa theo chiều dọc */}
                            <div className="flex flex-col items-center justify-center">
                                {/* Avatar */}
                                <div className="relative w-36 h-36 rounded-full overflow-hidden bg-gradient-to-br from-red-500 to-orange-300 flex items-center justify-center border-4 border-white group cursor-pointer mb-4">
                                    {agentData.avatar ? (
                                        <img
                                            src={agentData.avatar}
                                            alt="Avatar"
                                            className="w-full h-full object-cover"
                                        />
                                    ) : (
                                        <div className="text-white">
                                            <Icon name="user" size="xl" />
                                        </div>
                                    )}

                                    <div
                                        className="absolute bottom-0 left-0 right-0 h-1/4 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center"
                                        onClick={handleAvatarUpload}
                                    >
                                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                            <Icon name="upload" size="md" className="text-white" />
                                        </div>
                                    </div>

                                    <input
                                        ref={fileInputRef}
                                        type="file"
                                        accept="image/*"
                                        className="hidden"
                                        onChange={handleAvatarFileChange}
                                    />
                                </div>

                                {/* Input tên - căn giữa dưới avatar */}
                                <div className="w-full max-w-xs">
                                    <Input
                                        id="name"
                                        name="name"
                                        value={agentData.name}
                                        onChange={handleInputChange}
                                        placeholder="Nhập tên agent"
                                        className="w-full text-center"
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="space-y-4 sm:space-y-6">

                            {/* Model Config - Hiển thị nếu hasModel = true */}
                            {agentData.hasModel && (
                                <ModelConfig
                                    // initialData={agentData.modelConfig}
                                    onSave={handleModelConfigUpdate}
                                />
                            )}

                            {/* Profile Config - Hiển thị nếu hasProfile = true */}
                            {agentData.hasProfile && (
                                <ProfileConfig
                                    initialData={agentData.profile}
                                    onSave={handleProfileUpdate}
                                />
                            )}

                            {/* Integration Config - Hiển thị nếu hasIntegrations = true */}
                            {agentData.hasIntegrations && (
                                <IntegrationConfig
                                    initialData={agentData.integrations}
                                    onSave={handleIntegrationsUpdate}
                                />
                            )}

                            {/* Strategy Config - Hiển thị nếu hasStrategy = true */}
                            {agentData.hasStrategy && (
                                <StrategyConfig
                                    initialData={agentData.strategy}
                                    onSave={handleStrategyUpdate}
                                />
                            )}

                            {/* Convert Config - Hiển thị nếu hasConvert = true */}
                            {agentData.hasConvert && (
                                <ConvertConfig
                                    initialData={agentData.convert}
                                    onSave={handleConvertUpdate}
                                />
                            )}

                            {/* Response Config - Hiển thị nếu hasResponse = true */}
                            {agentData.hasResponse && (
                                <ResponseConfig
                                    initialData={agentData.response}
                                    onSave={handleResponseUpdate}
                                />
                            )}

                            {/* Multi Agent Config - Hiển thị nếu hasMultiAgent = true */}
                            {agentData.hasMultiAgent && (
                                <MultiAgentConfig
                                    initialData={agentData.multiAgent}
                                    onSave={handleMultiAgentUpdate}
                                    availableAgents={agentTypes}
                                />
                            )}
                        </div>
                    </div>
                </Container>
            )}



            {/* Delete Confirmation Modal */}
            <ConfirmDeleteModal
                isOpen={showDeleteModal}
                onClose={handleCancelDelete}
                onConfirm={handleConfirmDelete}
                title="Xác nhận xóa Type Agent"
                message="Bạn có chắc chắn muốn xóa type agent này?"
                itemName={deleteAgentData?.name}
                isSubmitting={false}
            />
        </>
    );
};

export default AgentCreatePage;
