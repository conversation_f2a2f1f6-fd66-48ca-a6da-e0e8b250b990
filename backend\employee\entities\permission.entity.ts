import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';

/**
 * Entity đại diện cho bảng permissions trong cơ sở dữ liệu
 */
@Entity('permissions')
@Unique('permissions_pk', ['action', 'module'])
export class Permission {
  /**
   * ID của permission
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Hành động
   */
  @Column({ name: 'action', length: 255, comment: 'Hành động' })
  action: string;

  /**
   * <PERSON><PERSON> tả quyền
   */
  @Column({ name: 'description', type: 'text', comment: '<PERSON>ô tả quyền' })
  description: string;

  /**
   * Module
   */
  @Column({ name: 'module', length: 50, comment: 'Module' })
  module: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', comment: 'Thời gian tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
