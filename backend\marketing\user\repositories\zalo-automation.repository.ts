import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ZaloAutomation } from '../entities';
import { ZaloAutomationStatus, ZaloAutomationTriggerType } from '../dto/zalo';

/**
 * Repository cho tự động hóa Zalo
 */
@Injectable()
export class ZaloAutomationRepository {
  constructor(
    @InjectRepository(ZaloAutomation)
    private readonly repository: Repository<ZaloAutomation>,
  ) {}

  /**
   * Tìm tự động hóa theo ID
   * @param id ID của tự động hóa
   * @returns Tự động hóa
   */
  async findById(id: number): Promise<ZaloAutomation | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm tự động hóa theo ID và ID người dùng
   * @param id ID của tự động hóa
   * @param userId ID của người dùng
   * @returns Tự động hóa
   */
  async findByIdAndUserId(id: number, userId: number): Promise<ZaloAutomation | null> {
    return this.repository.findOne({ where: { id, userId } });
  }

  /**
   * Tìm tự động hóa theo ID, ID người dùng và ID Official Account
   * @param id ID của tự động hóa
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Tự động hóa
   */
  async findByIdAndUserIdAndOaId(id: number, userId: number, oaId: string): Promise<ZaloAutomation | null> {
    return this.repository.findOne({ where: { id, userId, oaId } });
  }

  /**
   * Tìm danh sách tự động hóa theo ID người dùng và ID Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Danh sách tự động hóa
   */
  async findByUserIdAndOaId(userId: number, oaId: string): Promise<ZaloAutomation[]> {
    return this.repository.find({ where: { userId, oaId } });
  }

  /**
   * Tìm danh sách tự động hóa theo ID Official Account, loại sự kiện kích hoạt và trạng thái
   * @param oaId ID của Official Account
   * @param triggerType Loại sự kiện kích hoạt
   * @param status Trạng thái
   * @returns Danh sách tự động hóa
   */
  async findByOaIdAndTriggerTypeAndStatus(
    oaId: string,
    triggerType: ZaloAutomationTriggerType,
    status: ZaloAutomationStatus = ZaloAutomationStatus.ACTIVE,
  ): Promise<ZaloAutomation[]> {
    return this.repository.createQueryBuilder('automation')
      .where('automation.oaId = :oaId', { oaId })
      .andWhere('automation.status = :status', { status })
      .andWhere("automation.trigger->>'type' = :triggerType", { triggerType })
      .getMany();
  }

  /**
   * Tìm danh sách tự động hóa với phân trang
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách tự động hóa và tổng số tự động hóa
   */
  async findWithPagination(options: any): Promise<[ZaloAutomation[], number]> {
    const { where, skip, take, order } = options;
    return this.repository.findAndCount({
      where,
      skip,
      take,
      order,
    });
  }

  /**
   * Tạo tự động hóa mới
   * @param data Dữ liệu tự động hóa
   * @returns Tự động hóa đã tạo
   */
  async create(data: Partial<ZaloAutomation>): Promise<ZaloAutomation> {
    const automation = this.repository.create(data);
    return this.repository.save(automation);
  }

  /**
   * Cập nhật tự động hóa
   * @param id ID của tự động hóa
   * @param data Dữ liệu cập nhật
   * @returns Tự động hóa đã cập nhật
   */
  async update(id: number, data: Partial<ZaloAutomation>): Promise<ZaloAutomation | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa tự động hóa
   * @param id ID của tự động hóa
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Cập nhật trạng thái tự động hóa
   * @param id ID của tự động hóa
   * @param status Trạng thái mới
   * @returns Tự động hóa đã cập nhật
   */
  async updateStatus(id: number, status: ZaloAutomationStatus): Promise<ZaloAutomation | null> {
    await this.repository.update(id, { status });
    return this.findById(id);
  }

  /**
   * Tăng số lần kích hoạt tự động hóa
   * @param id ID của tự động hóa
   * @returns Tự động hóa đã cập nhật
   */
  async incrementTriggerCount(id: number): Promise<ZaloAutomation | null> {
    const automation = await this.findById(id);
    if (!automation) {
      return null;
    }

    await this.repository.update(id, {
      triggerCount: automation.triggerCount + 1,
      updatedAt: Date.now(),
    });

    return this.findById(id);
  }
}
