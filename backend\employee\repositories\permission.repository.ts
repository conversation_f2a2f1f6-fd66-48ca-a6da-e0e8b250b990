import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Permission } from '@modules/employee/entities';

/**
 * Repository cho Permission
 */
@Injectable()
export class PermissionRepository {
  constructor(
    @InjectRepository(Permission)
    private readonly repository: Repository<Permission>,
  ) {}

  /**
   * L<PERSON>y tất cả quyền
   * @returns Danh sách tất cả quyền
   */
  async findAll(): Promise<Permission[]> {
    return this.repository.find();
  }

  /**
   * Tìm quyền theo ID
   * @param id ID của quyền
   * @returns Quyền
   */
  async findById(id: number): Promise<Permission> {
    const permission = await this.repository.findOne({
      where: { id },
    });

    if (!permission) {
      throw new NotFoundException(`Quyền với ID "${id}" không tồn tại`);
    }

    return permission;
  }

  /**
   * Tìm nhiều quyền theo danh sách ID
   * @param ids Danh sách ID của các quyền
   * @returns Danh sách các quyền
   */
  async findByIds(ids: number[]): Promise<Permission[]> {
    return this.repository.find({
      where: { id: In(ids) },
    });
  }

  /**
   * Tìm quyền theo module và action
   * @param module Tên module
   * @param action Tên action
   * @returns Quyền tìm thấy hoặc null
   */
  async findByModuleAndAction(module: string, action: string): Promise<Permission | null> {
    return this.repository.findOne({
      where: { module, action },
    });
  }

  /**
   * Tạo quyền mới
   * @param data Thông tin quyền mới
   * @returns Quyền đã được tạo
   */
  async create(data: Partial<Permission>): Promise<Permission> {
    const permission = this.repository.create({
      ...data,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return this.repository.save(permission);
  }

  /**
   * Xóa quyền
   * @param id ID của quyền
   */
  async delete(id: number): Promise<void> {
    await this.repository.delete(id);
  }
}
