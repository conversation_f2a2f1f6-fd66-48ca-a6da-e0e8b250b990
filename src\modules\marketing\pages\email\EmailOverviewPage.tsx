
import { useTranslation } from 'react-i18next';
import { Plus, Mail, FileText, Send, Eye, MousePointer } from 'lucide-react';
import { Card } from '@/shared/components/common';
import { Button } from '@/shared/components/common';
import { Badge } from '@/shared/components/common';
import { Skeleton } from '@/shared/components/common';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import { useEmailTemplates } from '../../hooks/email/useEmailTemplates';
import { useNavigate } from 'react-router-dom';
import type { EmailTemplateDto } from '../../types/email.types';

/**
 * Trang tổng quan Email Marketing
 */
export function EmailOverviewPage() {
  const { t } = useTranslation('marketing');
  const navigate = useNavigate();

  const { data: templatesData, isLoading } = useEmailTemplates({ limit: 5 });

  const handleCreateTemplate = () => {
    navigate('/marketing/email/templates?action=create');
  };

  const handleCreateCampaign = () => {
    navigate('/marketing/email/campaigns?action=create');
  };

  const handleViewTemplate = (templateId: string) => {
    navigate(`/marketing/email/templates/${templateId}`);
  };

  // Mock data cho demo
  const mockStats = {
    totalTemplates: templatesData?.meta.totalItems || 0,
    totalCampaigns: 24,
    emailsSentThisMonth: 15420,
    averageOpenRate: 28.5,
    averageClickRate: 4.2,
    recentCampaigns: [
      {
        id: '1',
        name: 'Newsletter tháng 1',
        status: 'sent',
        sentAt: new Date('2025-01-20'),
        recipients: 1250,
        openRate: 32.4,
        clickRate: 5.1,
      },
      {
        id: '2',
        name: 'Khuyến mãi cuối năm',
        status: 'sending',
        sentAt: new Date('2025-01-22'),
        recipients: 2100,
        openRate: 0,
        clickRate: 0,
      },
      {
        id: '3',
        name: 'Chào mừng khách hàng mới',
        status: 'scheduled',
        sentAt: new Date('2025-01-25'),
        recipients: 450,
        openRate: 0,
        clickRate: 0,
      },
    ],
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'sent':
        return <Badge variant="success">Đã gửi</Badge>;
      case 'sending':
        return <Badge variant="warning">Đang gửi</Badge>;
      case 'scheduled':
        return <Badge variant="info">Đã lên lịch</Badge>;
      case 'draft':
        return <Badge variant="warning">Bản nháp</Badge>;
      default:
        return <Badge variant="primary">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <MarketingViewHeader
        title={t('email.overview.title', 'Email Marketing')}
        description={t('email.overview.description', 'Tạo và quản lý chiến dịch email marketing hiệu quả')}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCreateTemplate} className="gap-2">
              <FileText className="h-4 w-4" />
              {t('email.overview.createTemplate', 'Tạo Template')}
            </Button>
            <Button onClick={handleCreateCampaign} className="gap-2">
              <Plus className="h-4 w-4" />
              {t('email.overview.createCampaign', 'Tạo Chiến dịch')}
            </Button>
          </div>
        }
      />

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {t('email.overview.stats.totalTemplates', 'Tổng Templates')}
              </span>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">
            {isLoading ? <Skeleton className="h-8 w-16" /> : mockStats.totalTemplates}
          </div>
          <p className="text-xs text-muted-foreground">
            {t('email.overview.stats.activeTemplates', '+3 template mới')}
          </p>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {t('email.overview.stats.emailsSent', 'Email đã gửi')}
              </span>
              <Send className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">
            {mockStats.emailsSentThisMonth.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            {t('email.overview.stats.thisMonth', 'Tháng này')}
          </p>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {t('email.overview.stats.openRate', 'Tỷ lệ mở')}
              </span>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">
            {mockStats.averageOpenRate}%
          </div>
          <p className="text-xs text-muted-foreground">
            {t('email.overview.stats.averageOpenRate', 'Trung bình 30 ngày')}
          </p>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {t('email.overview.stats.clickRate', 'Tỷ lệ click')}
              </span>
              <MousePointer className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">
            {mockStats.averageClickRate}%
          </div>
          <p className="text-xs text-muted-foreground">
            {t('email.overview.stats.averageClickRate', 'Trung bình 30 ngày')}
          </p>
        </Card>
      </div>

      {/* Recent Templates */}
      <Card
        title={t('email.overview.recentTemplates', 'Templates gần đây')}
        subtitle={t('email.overview.recentTemplatesDescription', 'Các email template được tạo hoặc cập nhật gần đây')}
      >
        {isLoading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[150px]" />
                </div>
              </div>
            ))}
          </div>
        ) : templatesData?.items.length === 0 ? (
          <div className="text-center py-8">
            <div className="mx-auto h-12 w-12 text-muted-foreground mb-4">
              <FileText className="h-full w-full" />
            </div>
            <h3 className="text-lg font-medium mb-2">
              {t('email.overview.noTemplates', 'Chưa có template nào')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t('email.overview.noTemplatesDescription', 'Tạo template đầu tiên để bắt đầu gửi email')}
            </p>
            <Button onClick={handleCreateTemplate}>
              {t('email.overview.createFirstTemplate', 'Tạo template đầu tiên')}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {templatesData?.items.map((template: EmailTemplateDto) => (
              <div
                key={template.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                onClick={() => handleViewTemplate(template.id)}
              >
                <div className="flex items-center space-x-4">
                  <div className="h-12 w-12 rounded bg-gradient-to-r from-orange-500 to-red-600 flex items-center justify-center text-white font-semibold">
                    <Mail className="h-6 w-6" />
                  </div>
                  <div>
                    <h4 className="font-medium">{template.name}</h4>
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <span>{template.subject}</span>
                      <Badge variant="info" className="text-xs">
                        {template.type}
                      </Badge>
                      <Badge
                        variant={template.status === 'ACTIVE' ? 'success' : 'warning'}
                        className="text-xs"
                      >
                        {template.status === 'ACTIVE' ? 'Hoạt động' : 'Bản nháp'}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="text-sm text-muted-foreground">
                  {new Date(template.updatedAt).toLocaleDateString('vi-VN')}
                </div>
              </div>
            ))}

            {templatesData && templatesData.meta.totalItems > templatesData.items.length && (
              <div className="text-center pt-4">
                <Button
                  variant="outline"
                  onClick={() => navigate('/marketing/email/templates')}
                >
                  {t('email.overview.viewAllTemplates', 'Xem tất cả templates')}
                </Button>
              </div>
            )}
          </div>
        )}
      </Card>

      {/* Recent Campaigns */}
      <Card
        title={t('email.overview.recentCampaigns', 'Chiến dịch gần đây')}
        subtitle={t('email.overview.recentCampaignsDescription', 'Tình trạng các chiến dịch email gần đây')}
      >
        <div className="space-y-4">
          {mockStats.recentCampaigns.map((campaign) => (
            <div
              key={campaign.id}
              className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-center space-x-4">
                <div className="h-10 w-10 rounded bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white">
                  <Send className="h-5 w-5" />
                </div>
                <div>
                  <h4 className="font-medium">{campaign.name}</h4>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <span>{campaign.recipients.toLocaleString()} người nhận</span>
                    {getStatusBadge(campaign.status)}
                  </div>
                </div>
              </div>

              <div className="text-right">
                <div className="text-sm font-medium">
                  {campaign.status === 'sent' && (
                    <>
                      <span className="text-green-600">{campaign.openRate}% mở</span>
                      <span className="text-blue-600 ml-2">{campaign.clickRate}% click</span>
                    </>
                  )}
                </div>
                <div className="text-xs text-muted-foreground">
                  {campaign.sentAt.toLocaleDateString('vi-VN')}
                </div>
              </div>
            </div>
          ))}

          <div className="text-center pt-4">
            <Button
              variant="outline"
              onClick={() => navigate('/marketing/email/campaigns')}
            >
              {t('email.overview.viewAllCampaigns', 'Xem tất cả chiến dịch')}
            </Button>
          </div>
        </div>
      </Card>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card
          title="Quản lý Templates"
          subtitle="Tạo và chỉnh sửa email templates"
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => navigate('/marketing/email/templates')}
          hoverable
        >
          <div className="mt-4">
            <Button variant="outline" size="sm">
              Xem chi tiết
            </Button>
          </div>
        </Card>

        <Card
          title="Email Automation"
          subtitle="Thiết lập chuỗi email tự động"
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => navigate('/marketing/email/automation')}
          hoverable
        >
          <div className="mt-4">
            <Button variant="outline" size="sm">
              Xem chi tiết
            </Button>
          </div>
        </Card>

        <Card
          title="Analytics"
          subtitle="Báo cáo hiệu quả email marketing"
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => navigate('/marketing/email/analytics')}
          hoverable
        >
          <div className="mt-4">
            <Button variant="outline" size="sm">
              Xem chi tiết
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
}

export default EmailOverviewPage;
