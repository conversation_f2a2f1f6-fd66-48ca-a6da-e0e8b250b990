import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thống kê hiệu suất của một campaign
 */
export class CampaignPerformanceDto {
  @ApiProperty({
    description: 'ID của campaign',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên campaign',
    example: 'Chiến dịch khuyến mãi mùa hè'
  })
  name: string;

  @ApiProperty({
    description: 'Tổng số người nhận',
    example: 100
  })
  totalRecipients: number;

  @ApiProperty({
    description: 'Số lượng đã gửi',
    example: 100
  })
  sent: number;

  @ApiProperty({
    description: 'Số lượng đã nhận',
    example: 95
  })
  delivered: number;

  @ApiProperty({
    description: 'Số lượng đã mở (chỉ áp dụng cho email)',
    example: 50
  })
  opened: number;

  @ApiProperty({
    description: 'Số lượng đã nhấp (chỉ áp dụng cho email)',
    example: 20
  })
  clicked: number;

  @ApiProperty({
    description: 'Tỷ lệ mở (%)',
    example: 50
  })
  openRate: number;

  @ApiProperty({
    description: 'Tỷ lệ nhấp (%)',
    example: 20
  })
  clickRate: number;

  @ApiProperty({
    description: 'Thời gian chạy campaign (Unix timestamp)',
    example: 1619171200
  })
  runAt: number;
}

/**
 * DTO cho thống kê hiệu suất campaign
 */
export class CampaignPerformanceStatisticsDto {
  @ApiProperty({
    description: 'Danh sách hiệu suất campaign',
    type: [CampaignPerformanceDto]
  })
  campaigns: CampaignPerformanceDto[];

  @ApiProperty({
    description: 'Tỷ lệ mở trung bình (%)',
    example: 45.5
  })
  averageOpenRate: number;

  @ApiProperty({
    description: 'Tỷ lệ nhấp trung bình (%)',
    example: 18.3
  })
  averageClickRate: number;

  @ApiProperty({
    description: 'Thời gian cập nhật thống kê (Unix timestamp)',
    example: 1619171200
  })
  updatedAt: number;
}
