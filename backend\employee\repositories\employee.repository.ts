import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, FindOptionsWhere } from 'typeorm';
import { Employee } from '@modules/employee/entities';
import { CreateEmployeeDto } from '../dto/create-employee.dto';
import { AppException, ErrorCode } from '@/common';
import { EmployeeQueryDto } from '../dto/employee-query.dto';
import { PaginatedResult } from '@common/response/api-response-dto';

/**
 * Repository cho Employee
 */
@Injectable()
export class EmployeeRepository {
  private readonly logger = new Logger(EmployeeRepository.name);

  constructor(
    @InjectRepository(Employee)
    private readonly repository: Repository<Employee>,
  ) {}

  /**
   * Tạo nhân viên mới
   * @param createEmployeeDto Thông tin nhân viên mới
   * @param hashedPassword Mật khẩu đã được mã hóa
   * @returns Nhân viên đã được tạo
   */
  async createEmployee(createEmployeeDto: CreateEmployeeDto, hashedPassword: string): Promise<Employee> {
    // Tạo nhân viên mới
    const employee = this.repository.create({
      ...createEmployeeDto,
      password: hashedPassword,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Lưu vào cơ sở dữ liệu
    return this.repository.save(employee);
  }

  /**
   * Tìm nhân viên theo ID
   * @param id ID của nhân viên
   * @param selectFields Các trường cần lấy, mặc định lấy tất cả
   * @returns Nhân viên
   */
  async findById(id: number, selectFields?: string[]): Promise<Employee> {
    const findOptions: any = {
      where: { id }
    };

    // Nếu có chỉ định các trường cần lấy
    if (selectFields && selectFields.length > 0) {
      findOptions.select = selectFields;
    }

    const employee = await this.repository.findOne(findOptions);

    if (!employee) {
      throw new AppException(ErrorCode.EMPLOYEE_NOT_FOUND, `Nhân viên với ID "${id}" không tồn tại`);
    }

    return employee;
  }

  /**
   * Tìm nhân viên theo email
   * @param email Email của nhân viên
   * @returns Nhân viên hoặc null nếu không tìm thấy
   */
  async findByEmail(email: string): Promise<Employee | null> {
    return this.repository.findOne({
      where: { email },
    });
  }

  /**
   * Tìm nhân viên theo số điện thoại
   * @param phoneNumber Số điện thoại của nhân viên
   * @returns Nhân viên hoặc null nếu không tìm thấy
   */
  async findByPhoneNumber(phoneNumber: string): Promise<Employee | null> {
    return this.repository.findOne({
      where: { phoneNumber },
    });
  }

  /**
   * Cập nhật avatar cho nhân viên
   * @param id ID của nhân viên
   * @param avatarKey Khóa của avatar trên S3
   * @returns Nhân viên đã được cập nhật
   */
  async updateAvatar(id: number, avatarKey: string): Promise<Employee> {
    const employee = await this.findById(id);

    // Cập nhật avatar và thời gian cập nhật
    employee.avatar = avatarKey;
    employee.updatedAt = Date.now();

    // Lưu thay đổi vào cơ sở dữ liệu
    return this.repository.save(employee);
  }

  /**
   * Đổi mật khẩu cho nhân viên
   * @param id ID của nhân viên
   * @param hashedPassword Mật khẩu mới đã được mã hóa
   * @returns Nhân viên đã được cập nhật
   */
  async changePassword(id: number, hashedPassword: string): Promise<Employee> {
    const employee = await this.findById(id);

    // Cập nhật mật khẩu mới và thời gian cập nhật
    employee.password = hashedPassword;
    employee.updatedAt = Date.now();

    // Lưu thay đổi vào cơ sở dữ liệu
    return this.repository.save(employee);
  }

  /**
   * Tìm kiếm danh sách nhân viên với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách nhân viên với phân trang
   */
  async findAll(queryDto: EmployeeQueryDto): Promise<PaginatedResult<Employee>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
        enable,
        email,
        phoneNumber,
      } = queryDto;

      // Tính toán offset
      const offset = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: FindOptionsWhere<Employee> = {};

      // Thêm điều kiện tìm kiếm theo từ khóa
      if (search) {
        where.fullName = Like(`%${search}%`);
      }

      // Thêm điều kiện lọc theo trạng thái
      if (enable !== undefined) {
        where.enable = enable;
      }

      // Thêm điều kiện lọc theo email
      if (email) {
        where.email = Like(`%${email}%`);
      }

      // Thêm điều kiện lọc theo số điện thoại
      if (phoneNumber) {
        where.phoneNumber = Like(`%${phoneNumber}%`);
      }

      // Đếm tổng số bản ghi
      const totalItems = await this.repository.count({ where });

      // Lấy danh sách nhân viên
      const items = await this.repository.find({
        where,
        order: { [sortBy]: sortDirection },
        skip: offset,
        take: limit,
      });

      // Tính toán metadata
      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Error finding employees: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Lỗi khi tìm kiếm danh sách nhân viên');
    }
  }
}
