import { Column, Entity, JoinTable, ManyToMany, PrimaryGeneratedColumn } from 'typeorm';
import { EmployeeRole } from './employee-role.entity';

/**
 * Entity đại diện cho bảng employees trong cơ sở dữ liệu
 * Tài khoản nhân viên
 */
@Entity('employees')
export class Employee {
  /**
   * ID của nhân viên
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Tên đầy đủ của nhân viên
   */
  @Column({ name: 'full_name', length: 255 })
  fullName: string;

  /**
   * Email nhân viên
   */
  @Column({ name: 'email', length: 100, unique: true, comment: 'Email nhân viên' })
  email: string;

  /**
   * Số điện thoại nhân viên
   */
  @Column({ name: 'phone_number', length: 20, unique: true })
  phoneNumber: string;

  /**
   * <PERSON><PERSON><PERSON> khẩu đã được mã hóa
   */
  @Column({ name: 'password', length: 1000 })
  password: string;

  /**
   * Địa chỉ nhân viên
   */
  @Column({ name: 'address', length: 2000 })
  address: string;

  /**
   * Thời gian tạo tài khoản (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời gian cập nhật thông tin (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;

  /**
   * Trạng thái hoạt động của tài khoản
   */
  @Column({ name: 'enable', type: 'boolean', default: true })
  enable: boolean;

  /**
   * Avatar của nhân viên (đường dẫn đến file ảnh)
   */
  @Column({ name: 'avatar', length: 500, nullable: true })
  avatar?: string;

  /**
   * Quan hệ nhiều-nhiều với bảng employee_roles
   */
  @ManyToMany(() => EmployeeRole)
  @JoinTable({
    name: 'employee_has_roles',
    joinColumn: {
      name: 'employee_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'role_id',
      referencedColumnName: 'id',
    },
  })
  roles: EmployeeRole[];
}
