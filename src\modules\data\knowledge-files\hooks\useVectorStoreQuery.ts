import { useApiQuery, useApiMutation, useApiDeleteMutation } from '@/shared/api/hooks';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import {
  AssignFilesDto,
  AssignFilesResponseDto,
  CreateVectorStoreDto,
  FileResponseDto,
  PaginatedVectorStoreResult,
  QueryVectorStoreDto,
  VectorStoreResponseDto,
} from '../types/knowledge-files.types';
import * as vectorStoreService from '../services/vector-store.service';

// Định nghĩa các query key
export const VECTOR_STORE_QUERY_KEYS = {
  all: ['vector-stores'] as const,
  lists: () => [...VECTOR_STORE_QUERY_KEYS.all, 'list'] as const,
  list: (filters: QueryVectorStoreDto) => [...VECTOR_STORE_QUERY_KEYS.lists(), filters] as const,
  details: () => [...VECTOR_STORE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...VECTOR_STORE_QUERY_KEYS.details(), id] as const,
  files: () => [...VECTOR_STORE_QUERY_KEYS.all, 'files'] as const,
  filesByVectorStore: (id: string) => [...VECTOR_STORE_QUERY_KEYS.files(), id] as const,
};

/**
 * Hook để lấy danh sách vector store
 * @param queryDto Tham số truy vấn
 * @returns Query object
 */
export const useVectorStores = (queryDto?: QueryVectorStoreDto) => {
  return useApiQuery<PaginatedVectorStoreResult>(
    VECTOR_STORE_QUERY_KEYS.list(queryDto || {}),
    '/user/vector-stores',
    {
      params: queryDto,
    }
  );
};

/**
 * Hook để lấy thông tin chi tiết vector store
 * @param id ID của vector store
 * @returns Query object
 */
export const useVectorStoreDetail = (id: string) => {
  return useApiQuery<VectorStoreResponseDto>(
    VECTOR_STORE_QUERY_KEYS.detail(id),
    `/user/vector-stores/${id}`
  );
};

/**
 * Hook để tạo vector store mới
 * @returns Mutation object
 */
export const useCreateVectorStore = () => {
  const queryClient = useQueryClient();

  return useApiMutation<VectorStoreResponseDto, CreateVectorStoreDto>('/user/vector-stores', {
    onSuccess: () => {
      // Invalidate và refetch danh sách vector store
      queryClient.invalidateQueries({ queryKey: VECTOR_STORE_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để gán các file vào vector store
 * @param vectorStoreId ID của vector store
 * @returns Mutation object
 */
export const useAssignFilesToVectorStore = (vectorStoreId: string) => {
  const queryClient = useQueryClient();

  return useApiMutation<AssignFilesResponseDto, AssignFilesDto>(
    `/user/vector-stores/${vectorStoreId}/files`,
    {
      onSuccess: () => {
        // Invalidate và refetch thông tin chi tiết vector store
        queryClient.invalidateQueries({ queryKey: VECTOR_STORE_QUERY_KEYS.detail(vectorStoreId) });
        // Invalidate và refetch danh sách file
        queryClient.invalidateQueries({ queryKey: ['knowledge-files', 'list'] });
      },
    }
  );
};

/**
 * Hook để gỡ file khỏi vector store
 * @param vectorStoreId ID của vector store
 * @returns Mutation object
 */
export const useRemoveFileFromVectorStore = (vectorStoreId: string) => {
  const queryClient = useQueryClient();

  return {
    removeFile: async (fileId: string) => {
      const result = await vectorStoreService.removeFileFromVectorStore(vectorStoreId, fileId);

      // Invalidate và refetch thông tin chi tiết vector store
      queryClient.invalidateQueries({ queryKey: VECTOR_STORE_QUERY_KEYS.detail(vectorStoreId) });
      // Invalidate và refetch danh sách file
      queryClient.invalidateQueries({ queryKey: ['knowledge-files', 'list'] });

      return result;
    },
  };
};

/**
 * Hook để xóa vector store
 * @returns Mutation object
 */
export const useDeleteVectorStore = () => {
  const queryClient = useQueryClient();

  return useApiDeleteMutation<{ success: boolean }, string>('/user/vector-stores', {
    onSuccess: () => {
      // Invalidate và refetch danh sách vector store
      queryClient.invalidateQueries({ queryKey: VECTOR_STORE_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa nhiều vector store cùng lúc
 * @returns Mutation object
 */
export const useDeleteMultipleVectorStores = () => {
  const queryClient = useQueryClient();

  return useMutation<{ success: boolean }, Error, { ids: string[] }>({
    mutationFn: (variables: { ids: string[] }) =>
      vectorStoreService.deleteMultipleVectorStores(variables.ids),
    onSuccess: () => {
      // Invalidate và refetch danh sách vector store
      queryClient.invalidateQueries({ queryKey: VECTOR_STORE_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa nhiều file khỏi vector store
 * @param vectorStoreId ID của vector store
 * @returns Mutation object
 */
export const useRemoveFilesFromVectorStore = (vectorStoreId: string) => {
  const queryClient = useQueryClient();

  return {
    removeFiles: async (fileIds: string[]) => {
      const result = await vectorStoreService.removeFilesFromVectorStore(vectorStoreId, fileIds);

      // Invalidate và refetch thông tin chi tiết vector store
      queryClient.invalidateQueries({ queryKey: VECTOR_STORE_QUERY_KEYS.detail(vectorStoreId) });
      // Invalidate và refetch danh sách file đã gán
      queryClient.invalidateQueries({ queryKey: VECTOR_STORE_QUERY_KEYS.filesByVectorStore(vectorStoreId) });
      // Invalidate và refetch danh sách file
      queryClient.invalidateQueries({ queryKey: ['knowledge-files', 'list'] });

      return result;
    },
  };
};

/**
 * Hook để lấy danh sách file đã được gán vào vector store
 * @param vectorStoreId ID của vector store
 * @returns Query object
 */
export const useVectorStoreFiles = (vectorStoreId: string) => {
  return useApiQuery<FileResponseDto[]>(
    VECTOR_STORE_QUERY_KEYS.filesByVectorStore(vectorStoreId),
    `/user/vector-stores/${vectorStoreId}/files`
  );
};
