import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';
import { Loader2, ExternalLink } from 'lucide-react';
import { Button } from '@/shared/components/common';
import { Input } from '@/shared/components/common';
import { Alert } from '@/shared/components/common';
import { FormItem } from '@/shared/components/common';
import { connectZaloAccountSchema, type ConnectZaloAccountFormData } from '../../schemas/zalo.schema';
import { useConnectZaloAccount } from '../../hooks/zalo/useZaloAccounts';

interface ConnectZaloAccountFormProps {
  onSuccess?: () => void;
}

/**
 * Form kết nối Zalo Official Account
 */
export function ConnectZaloAccountForm({ onSuccess }: ConnectZaloAccountFormProps) {
  const { t } = useTranslation('marketing');
  const connectAccount = useConnectZaloAccount();

  const form = useForm<ConnectZaloAccountFormData>({
    resolver: zodResolver(connectZaloAccountSchema),
    defaultValues: {
      oaId: '',
      accessToken: '',
      refreshToken: '',
      name: '',
      avatar: '',
    },
  });

  const onSubmit = async (data: ConnectZaloAccountFormData) => {
    try {
      await connectAccount.mutateAsync(data);
      onSuccess?.();
    } catch {
      // Error được handle trong hook
    }
  };

  return (
    <div className="space-y-6">
      {/* Instructions */}
      <Alert
        type="info"
        message={t('zalo.connect.instructions', 'Để kết nối Zalo OA, bạn cần có quyền quản trị viên và lấy thông tin từ Zalo Developer Console.')}
      >
        <div className="mt-2">
          <a
            href="https://developers.zalo.me"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
          >
            {t('zalo.connect.learnMore', 'Tìm hiểu thêm')}
            <ExternalLink className="h-3 w-3 ml-1" />
          </a>
        </div>
      </Alert>

      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {/* OA ID */}
          <FormItem
            name="oaId"
            label={t('zalo.connect.oaId', 'OA ID')}
            helpText={t('zalo.connect.oaIdDescription', 'ID của Zalo Official Account (có thể tìm trong Zalo OA Manager)')}
            required
          >
            <Input
              placeholder={t('zalo.connect.oaIdPlaceholder', 'Nhập OA ID...')}
            />
          </FormItem>

          {/* Name */}
          <FormItem
            name="name"
            label={t('zalo.connect.name', 'Tên OA')}
            helpText={t('zalo.connect.nameDescription', 'Tên hiển thị của Official Account')}
            required
          >
            <Input
              placeholder={t('zalo.connect.namePlaceholder', 'Nhập tên hiển thị...')}
            />
          </FormItem>

          {/* Access Token */}
          <FormItem
            name="accessToken"
            label={t('zalo.connect.accessToken', 'Access Token')}
            helpText={t('zalo.connect.accessTokenDescription', 'Access token từ Zalo Developer Console')}
            required
          >
            <Input
              type="password"
              placeholder={t('zalo.connect.accessTokenPlaceholder', 'Nhập access token...')}
            />
          </FormItem>

          {/* Refresh Token */}
          <FormItem
            name="refreshToken"
            label={t('zalo.connect.refreshToken', 'Refresh Token')}
            helpText={t('zalo.connect.refreshTokenDescription', 'Refresh token để gia hạn access token')}
            required
          >
            <Input
              type="password"
              placeholder={t('zalo.connect.refreshTokenPlaceholder', 'Nhập refresh token...')}
            />
          </FormItem>

          {/* Avatar URL (Optional) */}
          <FormItem
            name="avatar"
            label={t('zalo.connect.avatar', 'Avatar URL (Tùy chọn)')}
            helpText={t('zalo.connect.avatarDescription', 'URL ảnh đại diện của OA')}
          >
            <Input
              placeholder={t('zalo.connect.avatarPlaceholder', 'https://...')}
            />
          </FormItem>

          {/* Submit Button */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onSuccess?.()}
              disabled={connectAccount.isPending}
            >
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button
              type="submit"
              disabled={connectAccount.isPending}
            >
              {connectAccount.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t('zalo.connect.submit', 'Kết nối')}
            </Button>
          </div>
        </form>
      </FormProvider>

      {/* Help Section */}
      <div className="border-t pt-4">
        <h4 className="text-sm font-medium mb-2">
          {t('zalo.connect.help.title', 'Cần trợ giúp?')}
        </h4>
        <div className="text-sm text-muted-foreground space-y-1">
          <p>
            {t('zalo.connect.help.step1', '1. Truy cập Zalo Developer Console và tạo ứng dụng')}
          </p>
          <p>
            {t('zalo.connect.help.step2', '2. Lấy OA ID từ phần quản lý Official Account')}
          </p>
          <p>
            {t('zalo.connect.help.step3', '3. Tạo access token và refresh token')}
          </p>
          <p>
            {t('zalo.connect.help.step4', '4. Cấu hình webhook URL nếu cần thiết')}
          </p>
        </div>
      </div>
    </div>
  );
}
