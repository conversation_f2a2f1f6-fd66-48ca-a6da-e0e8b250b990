import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import {
  createAgent,
  deleteAgent,
  getAgentDetail,
  getAgents,
  getAgentStatistics,
  toggleAgentActive,
  updateAgent,
  updateAgentVectorStore,
} from '../api/agent.api';

import {
  AgentDetailDto,
  AgentListResponse,
  AgentStatisticsQueryDto,
  AgentStatisticsResponseDto,
  CreateAgentDto,
  CreateAgentResponseDto,
  GetAgentsQueryDto,
  UpdateAgentDto,
  UpdateAgentResponseDto,
  UpdateAgentVectorStoreDto,
} from '../types';

/**
 * Service layer cho Agent - chứa business logic
 * Theo pattern của blog module
 */

/**
 * Lấy danh sách agents với business logic
 * @param params Query params
 * @returns Promise với response từ API
 */
export const getAgentsWithBusinessLogic = async (
  params?: GetAgentsQueryDto
): Promise<ApiResponse<AgentListResponse>> => {
  // C<PERSON> thể thêm business logic ở đây như:
  // - Validate params
  // - Transform data
  // - Cache logic
  // - Error handling

  return getAgents(params);
};

/**
 * Lấy chi tiết agent với business logic
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const getAgentDetailWithBusinessLogic = async (
  id: string
): Promise<ApiResponse<AgentDetailDto>> => {
  // Business logic có thể bao gồm:
  // - Validate ID format
  // - Check permissions
  // - Transform response data

  return getAgentDetail(id);
};

/**
 * Tạo agent mới với business logic
 * @param data Dữ liệu tạo agent
 * @returns Promise với response từ API
 */
export const createAgentWithBusinessLogic = async (
  data: CreateAgentDto
): Promise<ApiResponse<CreateAgentResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate input data
  // - Transform data format
  // - Set default values
  // - Pre-processing

  // Ví dụ: Set default model config nếu không có
  const processedData = {
    ...data,
    modelConfig: {
      modelId: 'gpt-4o',
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      max_tokens: 1000,
      ...data.modelConfig,
    },
  };

  return createAgent(processedData);
};

/**
 * Cập nhật agent với business logic
 * @param id ID của agent
 * @param data Dữ liệu cập nhật
 * @returns Promise với response từ API
 */
export const updateAgentWithBusinessLogic = async (
  id: string,
  data: UpdateAgentDto
): Promise<ApiResponse<UpdateAgentResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate permissions
  // - Merge with existing data
  // - Transform data

  return updateAgent(id, data);
};

/**
 * Xóa agent với business logic
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const deleteAgentWithBusinessLogic = async (
  id: string
): Promise<ApiResponse<void>> => {
  // Business logic có thể bao gồm:
  // - Check dependencies
  // - Cleanup related data
  // - Audit logging

  return deleteAgent(id);
};

/**
 * Bật/tắt agent với business logic
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const toggleAgentActiveWithBusinessLogic = async (
  id: string
): Promise<ApiResponse<{ active: boolean }>> => {
  // Business logic có thể bao gồm:
  // - Check business rules
  // - Update related systems
  // - Notifications

  return toggleAgentActive(id);
};

/**
 * Lấy thống kê agent với business logic
 * @param id ID của agent
 * @param params Query params
 * @returns Promise với response từ API
 */
export const getAgentStatisticsWithBusinessLogic = async (
  id: string,
  params?: AgentStatisticsQueryDto
): Promise<ApiResponse<AgentStatisticsResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Calculate additional metrics
  // - Apply filters
  // - Format data for display

  return getAgentStatistics(id, params);
};

/**
 * Cập nhật vector store với business logic
 * @param id ID của agent
 * @param data Dữ liệu vector store
 * @returns Promise với response từ API
 */
export const updateAgentVectorStoreWithBusinessLogic = async (
  id: string,
  data: UpdateAgentVectorStoreDto
): Promise<ApiResponse<void>> => {
  // Business logic có thể bao gồm:
  // - Validate vector store exists
  // - Check compatibility
  // - Update indexes

  return updateAgentVectorStore(id, data);
};
