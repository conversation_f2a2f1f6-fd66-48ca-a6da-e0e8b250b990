/**
 * Interface cho dữ liệu tài nguyên Media
 */
export interface Media {
    id: string;
    name: string;
    type: 'image' | 'video' | 'audio' | 'document';
    url: string;
    thumbnailUrl?: string;
    fileSize?: number;
    duration?: number;
    createdAt: string;
    format?: string;
}

/**
 * Interface cho dữ liệu tài nguyên URL
 */
export interface Url {
    id: string;
    title: string;
    url: string;
    description?: string;
    category?: string;
    createdAt: string;
    lastChecked?: string;
    status?: 'active' | 'broken' | 'pending';
}

/**
 * Interface cho dữ liệu tài nguyên Product
 */
export interface Product {
    id: string;
    name: string;
    sku: string;
    price: number;
    salePrice?: number;
    imageUrl?: string;
    category?: string;
    stock: number;
    status: 'active' | 'inactive' | 'out_of_stock';
    createdAt: string;
}

/**
 * Interface cho dữ liệu tài nguyên phản hồi
 */
export interface ResponseData {
    media: Media[];
    urls: Url[];
    products: Product[];
}

// ===== API Response Types =====

import { AgentListItemDto, TypeAgentListItemDto, BaseModelListItemDto } from './dto';

/**
 * Response cho tạo Agent
 */
export interface CreateAgentResponseDto {
  id: string;
  avatarUploadUrl: string;
}

/**
 * Response cho cập nhật Agent
 */
export type UpdateAgentResponseDto = CreateAgentResponseDto;

/**
 * Response cho thống kê Agent
 */
export interface AgentStatisticsResponseDto {
  totalConversations: number;
  totalMessages: number;
  averageResponseTime: number;
  satisfactionRate: number;
  dailyStats: Array<{
    date: string;
    conversations: number;
    messages: number;
  }>;
}

/**
 * Interface cho response danh sách agents với cấu trúc items và meta
 */
export interface AgentListResponse {
  items: AgentListItemDto[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Interface cho response danh sách type agents với cấu trúc items và meta
 */
export interface TypeAgentListResponse {
  items: TypeAgentListItemDto[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Interface cho response danh sách base models với cấu trúc items và meta
 */
export interface BaseModelListResponse {
  items: BaseModelListItemDto[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
