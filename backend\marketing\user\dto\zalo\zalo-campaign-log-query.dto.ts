import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * Enum cho trạng thái log chiến dịch Zalo
 */
export enum ZaloCampaignLogStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  DELETED = 'deleted',
  ALL = 'all',
}

/**
 * DTO cho việc truy vấn danh sách log chiến dịch Zalo
 */
export class ZaloCampaignLogQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo ID của chiến dịch',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsString()
  campaignId?: string;

  @ApiProperty({
    description: 'Lọc theo ID của người theo dõi',
    example: '123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  followerId?: string;

  @ApiProperty({
    description: 'Lọc theo trạng thái',
    enum: ZaloCampaignLogStatus,
    example: ZaloCampaignLogStatus.SUCCESS,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloCampaignLogStatus)
  status?: ZaloCampaignLogStatus;

  constructor() {
    super();
    this.sortBy = 'createdAt';
    this.sortDirection = SortDirection.DESC;
  }
}
