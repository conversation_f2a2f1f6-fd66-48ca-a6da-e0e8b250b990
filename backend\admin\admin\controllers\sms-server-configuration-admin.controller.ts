import { Controller, UseGuards } from '@nestjs/common';
import { ApiB<PERSON>erAuth, ApiTags } from '@nestjs/swagger';
import { SmsServerConfigurationAdminService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { JwtEmployeeGuard } from '@/modules/auth/guards';

@ApiTags(SWAGGER_API_TAGS.INTEGRATION_ADMIN)
@Controller('admin/integration/sms-server')
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtEmployeeGuard)
export class SmsServerConfigurationAdminController {
  constructor(
    private readonly smsServerConfigurationAdminService: SmsServerConfigurationAdminService,
  ) {}
}
