import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho việc cập nhật role
 */
export class UpdateEmployeeRoleDto {
  /**
   * Tên role
   */
  @ApiProperty({
    description: 'Tên role',
    example: 'Admin',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên role phải là chuỗi' })
  name?: string;

  /**
   * <PERSON><PERSON> tả role
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả role',
    example: 'Quản trị viên hệ thống',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * Danh sách ID của các permission
   */
  @ApiProperty({
    description: 'Danh sách ID của các permission',
    example: [1, 2, 3],
    type: [Number],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: '<PERSON>h sách permission phải là mảng' })
  permissionIds?: number[];
}
