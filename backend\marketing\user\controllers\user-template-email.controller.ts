import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, getSchemaPath } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { UserTemplateEmailService } from '../services/user-template-email.service';
import { 
  CreateTemplateEmailDto, 
  TemplateEmailQueryDto, 
  TemplateEmailResponseDto, 
  UpdateTemplateEmailDto 
} from '../dto/template-email';
import { UserTemplateEmail } from '../entities/user-template-email.entity';

/**
 * Controller xử lý API liên quan đến template email
 */
@ApiTags(SWAGGER_API_TAGS.USER_TEMPLATE_EMAIL)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/template-emails')
export class UserTemplateEmailController {
  constructor(private readonly userTemplateEmailService: UserTemplateEmailService) {}

  /**
   * Lấy danh sách template email với phân trang và filter
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách template email với phân trang và filter' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách template email với phân trang',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(TemplateEmailResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: TemplateEmailQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserTemplateEmail>>> {
    const result = await this.userTemplateEmailService.findAll(user.id, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách template email thành công');
  }

  /**
   * Lấy chi tiết template email
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết template email' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết template email',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(TemplateEmailResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Template email không tồn tại' })
  async findOne(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<UserTemplateEmail>> {
    const result = await this.userTemplateEmailService.findById(id, user.id);
    return ApiResponseDto.success(result, 'Lấy chi tiết template email thành công');
  }

  /**
   * Tạo mới template email
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới template email' })
  @ApiResponse({
    status: 201,
    description: 'Template email đã được tạo',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(TemplateEmailResponseDto) }
          }
        }
      ]
    }
  })
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateTemplateEmailDto,
  ): Promise<ApiResponseDto<UserTemplateEmail>> {
    const result = await this.userTemplateEmailService.create(user.id, createDto);
    return ApiResponseDto.success(result, 'Tạo mới template email thành công');
  }

  /**
   * Cập nhật template email
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật template email' })
  @ApiResponse({
    status: 200,
    description: 'Template email đã được cập nhật',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(TemplateEmailResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Template email không tồn tại' })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateTemplateEmailDto,
  ): Promise<ApiResponseDto<UserTemplateEmail>> {
    const result = await this.userTemplateEmailService.update(id, user.id, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật template email thành công');
  }

  /**
   * Xóa template email
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa template email' })
  @ApiResponse({
    status: 200,
    description: 'Template email đã được xóa',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { type: 'boolean' }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Template email không tồn tại' })
  async delete(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.userTemplateEmailService.delete(id, user.id);
    return ApiResponseDto.success(result, 'Xóa template email thành công');
  }
}
