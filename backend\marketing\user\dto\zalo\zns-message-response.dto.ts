import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin tin nhắn ZNS
 */
export class ZnsMessageResponseDto {
  @ApiProperty({
    description: 'ID của tin nhắn trong hệ thống',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của tin nhắn trên Z<PERSON>',
    example: 'msg123456789',
    nullable: true,
  })
  messageId?: string;

  @ApiProperty({
    description: 'ID của template',
    example: 'template123456789',
  })
  templateId: string;

  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0912345678',
  })
  phone: string;

  @ApiProperty({
    description: 'ID giao dịch',
    example: 'tracking123456789',
  })
  trackingId: string;

  @ApiProperty({
    description: 'Trạng thái tin nhắn (pending, delivered, failed)',
    example: 'delivered',
  })
  status: string;

  @ApiProperty({
    description: 'Thời điểm gửi thành công (Unix timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  deliveredTime?: number;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: 1625097600000,
  })
  createdAt: number;
}
