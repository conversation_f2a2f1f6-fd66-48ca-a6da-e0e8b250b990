import React, { useMemo, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import {
  <PERSON>ert,
  Button,
  Card,
  Chip,
  Icon,
  IconCard,
  Loading,
  Modal,
  ModernMenu,
  ResponsiveImage,
  Table,
  Typography,
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import ProductForSaleForm, {
  ProductForSaleFormValues,
} from '../components/forms/ProductForSaleForm';
import { useQuery } from '@tanstack/react-query';
import { MarketplaceApiService, ApiProduct, CreateProductDto, UpdateProductDto } from '../services/marketplace-api.service';
import { PRODUCT_QUERY_KEYS } from '../constants/product-query-keys';
import { formatPrice } from '../utils/price-formatter';

import {
  useCreateProduct,
  useUpdateProduct,
  useDeleteProduct,
  useSubmitProductForApproval,
  useCancelProductSubmission,
} from '../hooks/useUserProducts';

/**
 * Trang quản lý sản phẩm đăng bán
 */
const ProductsForSalePage: React.FC = () => {
  const { t } = useTranslation('marketplace');
  const navigate = useNavigate();
  const { productId } = useParams<{ productId: string }>();

  // State cho filter
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');
  const [selectedProduct, setSelectedProduct] = useState<ApiProduct | null>(null);
  const [productToDelete, setProductToDelete] = useState<ApiProduct | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [page] = useState(1); // setPage unused for now
  const [limit] = useState(10);

  // State quản lý menu hành động
  const [actionMenu, setActionMenu] = useState<{
    visible: boolean;
    recordId: number | null;
    recordName: string | null;
  }>({
    visible: false,
    recordId: null,
    recordName: null,
  });

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // API Hooks
  const createProductMutation = useCreateProduct();
  const updateProductMutation = useUpdateProduct();
  const deleteProductMutation = useDeleteProduct();
  const submitForApprovalMutation = useSubmitProductForApproval();
  const cancelSubmissionMutation = useCancelProductSubmission();

  // Lấy danh sách sản phẩm của user từ API
  const {
    data: userProductsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: [...PRODUCT_QUERY_KEYS.USER_PRODUCTS, { page, limit, search: searchTerm }],
    queryFn: () => MarketplaceApiService.getUserProducts({ page, limit, search: searchTerm }),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Lấy chi tiết sản phẩm nếu có productId trong URL
  const {
    data: productDetail,
    isLoading: isLoadingDetail,
  } = useQuery({
    queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCT_DETAIL(Number(productId || 0)),
    queryFn: () => MarketplaceApiService.getUserProductDetail(Number(productId)),
    enabled: !!productId,
    staleTime: 2 * 60 * 1000,
  });

  // Effect để auto-open form khi có productId
  useEffect(() => {
    if (productId && productDetail && !selectedProduct) {
      console.log('🔍 Auto-opening edit form for product:', productId, productDetail);
      setSelectedProduct(productDetail);
      showForm();
    }
  }, [productId, productDetail, selectedProduct, showForm]);

  // Status mapping
  const getStatusChip = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <Chip variant="success" size="sm">{t('marketplace:productsForSale.status.approved', 'Đã duyệt')}</Chip>;
      case 'PENDING':
        return <Chip variant="warning" size="sm">{t('marketplace:productsForSale.status.pending', 'Chờ duyệt')}</Chip>;
      case 'REJECTED':
        return <Chip variant="danger" size="sm">{t('marketplace:productsForSale.status.rejected', 'Từ chối')}</Chip>;
      case 'DRAFT':
        return <Chip variant="default" size="sm">{t('marketplace:productsForSale.status.draft', 'Bản nháp')}</Chip>;
      case 'DELETED':
        return <Chip variant="danger" size="sm">{t('marketplace:productsForSale.status.deleted', 'Đã xóa')}</Chip>;
      default:
        return <Chip variant="default" size="sm">{status}</Chip>;
    }
  };

  // Columns cho bảng
  const columns: TableColumn<ApiProduct>[] = [
    { key: 'id', title: 'ID', dataIndex: 'id', width: '5%' },
    {
      key: 'images',
      title: t('marketplace:productsForSale.table.image', 'Ảnh sản phẩm'),
      dataIndex: 'images',
      width: '10%',
      render: (_: unknown, record: ApiProduct) => (
        <div className="w-12 h-12 rounded overflow-hidden bg-gray-100">
          {record.images && record.images.length > 0 ? (
            <ResponsiveImage
              src={typeof record.images[0] === 'string' ? record.images[0] : record.images[0]?.url || '/placeholder-image.jpg'}
              alt="Product"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Icon name="image" size="sm" className="text-gray-400" />
            </div>
          )}
        </div>
      ),
    },
    { key: 'name', title: t('marketplace:productsForSale.table.name', 'Tên sản phẩm'), dataIndex: 'name', width: '20%' },
    {
      key: 'soldCount',
      title: t('marketplace:productsForSale.table.soldCount', 'Đã bán'),
      dataIndex: 'soldCount',
      width: '8%',
      render: (value: unknown) => <span className="text-green-600 font-medium">{String(value || 0)}</span>,
    },
    {
      key: 'createdAt',
      title: t('marketplace:productsForSale.table.createdAt', 'Ngày tạo'),
      dataIndex: 'createdAt',
      width: '12%',
      render: (value: unknown) => new Date(value as string).toLocaleDateString('vi-VN'),
    },
    {
      key: 'category',
      title: t('marketplace:productsForSale.table.category', 'Thể loại'),
      dataIndex: 'category',
      width: '10%',
      render: (value: unknown): React.ReactNode => {
        const categoryMap: Record<string, string> = {
          AGENT: t('marketplace:productsForSale.categories.agent', 'AI Agent'),
          KNOWLEDGE_FILE: t('marketplace:productsForSale.categories.knowledgeFile', 'Tài liệu'),
          FUNCTION: t('marketplace:productsForSale.categories.function', 'Chức năng'),
          FINETUNE: t('marketplace:productsForSale.categories.finetune', 'Fine-tune'),
          STRATEGY: t('marketplace:productsForSale.categories.strategy', 'Chiến lược'),
        };
        return <span>{categoryMap[value as string] || String(value || '')}</span>;
      },
    },
    {
      key: 'status',
      title: t('marketplace:productsForSale.table.status', 'Trạng thái'),
      dataIndex: 'status',
      width: '10%',
      render: (value: unknown) => getStatusChip(value as string),
    },
    {
      key: 'discountedPrice',
      title: t('marketplace:productsForSale.table.price', 'Giá bán'),
      dataIndex: 'discountedPrice',
      width: '15%',
      render: (value: unknown, record: ApiProduct) => (
        <div className="flex items-center">
          <div className="flex flex-col">
            <div className="flex items-center">
              <span className="mr-1">{formatPrice(value as number)}</span>
              <Icon name="rpoint" size="sm" className="text-red-600" />
            </div>
            {record.listedPrice > record.discountedPrice && (
              <div className="flex items-center text-gray-500 text-xs">
              </div>
            )}
          </div>
        </div>
      ),
    },
    {
      key: 'actions',
      title: t('common:actions', 'Thao tác'),
      width: '10%',
      render: (_: unknown, record: ApiProduct) => (
        <div className="relative">
          <IconCard
            icon="menu"
            variant="default"
            size="sm"
            onClick={() => {
              setActionMenu({
                visible: true,
                recordId: record.id,
                recordName: record.name,
              });
            }}
          />
          {actionMenu.visible && actionMenu.recordId === record.id && (
            <ModernMenu
              isOpen={true}
              onClose={() => setActionMenu({ visible: false, recordId: null, recordName: null })}
              placement="left"
              items={[
                {
                  id: 'edit',
                  label: t('common:edit', 'Chỉnh sửa'),
                  icon: 'edit',
                  onClick: () => {
                    handleEdit(record);
                    setActionMenu({ visible: false, recordId: null, recordName: null });
                  },
                },
                // Submit for approval button - chỉ hiển thị khi status là DRAFT
                ...(record.status === 'DRAFT' ? [{
                  id: 'submit',
                  label: t('marketplace:productsForSale.submitForApproval', 'Gửi duyệt'),
                  icon: 'send',
                  onClick: () => {
                    handleSubmitForApproval(record);
                    setActionMenu({ visible: false, recordId: null, recordName: null });
                  },
                  disabled: submitForApprovalMutation.isPending,
                }] : []),
                // Cancel submission button - chỉ hiển thị khi status là PENDING
                ...(record.status === 'PENDING' ? [{
                  id: 'cancel',
                  label: t('marketplace:productsForSale.cancelSubmission', 'Hủy gửi duyệt'),
                  icon: 'x',
                  onClick: () => {
                    handleCancelSubmission(record);
                    setActionMenu({ visible: false, recordId: null, recordName: null });
                  },
                  disabled: cancelSubmissionMutation.isPending,
                }] : []),
                {
                  id: 'delete',
                  label: t('common:delete', 'Xóa'),
                  icon: 'trash',
                  onClick: () => {
                    handleShowDeleteConfirm(record);
                    setActionMenu({ visible: false, recordId: null, recordName: null });
                  },
                  disabled: deleteProductMutation.isPending,
                },
              ]}
            />
          )}
        </div>
      ),
    },
  ];

  // Lọc dữ liệu
  const filteredData = useMemo(() => {
    const products = userProductsData?.items || [];
    return products.filter((product: ApiProduct) => {
      const matchesSearch =
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesFilter = filter === 'all' || product.status === filter;
      return matchesSearch && matchesFilter;
    });
  }, [userProductsData?.items, searchTerm, filter]);

  // Xử lý thêm mới
  const handleAdd = () => {
    setSelectedProduct(null); // Reset selected product when adding new
    showForm();
  };

  // Xử lý chỉnh sửa
  const handleEdit = (product: ApiProduct) => {
    setSelectedProduct(product);
    showForm();
  };

  // Xử lý submit form
  const handleSubmit = async (values: ProductForSaleFormValues & {
    imageFiles?: File[];
    oldImageKeys?: string[];
    deletedImageKeys?: string[];
    hasImageChanged?: boolean;
    uploadedImageUrls?: string[];
    hasDetailChanged?: boolean;
    hasUserManualChanged?: boolean;
  }, submitForApproval = false) => {
    try {
      if (selectedProduct) {
        // Tạo mảng operations cho ảnh
        const imageOperations: Array<{
          operation: 'ADD' | 'DELETE';
          key?: string;
          index?: number;
          mimeType?: string;
        }> = [];

        console.log('🔍 [IMAGE OPERATIONS] Building operations...');
        console.log('🔍 [IMAGE OPERATIONS] deletedImageKeys:', values.deletedImageKeys);
        console.log('🔍 [IMAGE OPERATIONS] imageFiles:', values.imageFiles?.map(f => f.name));

        // Xóa chỉ những ảnh được đánh dấu xóa
        if (values.deletedImageKeys && values.deletedImageKeys.length > 0) {
          console.log('🗑️ [IMAGE OPERATIONS] Processing DELETE operations...');
          values.deletedImageKeys.forEach((key, index) => {
            console.log(`🔍 [DELETE ${index}] Processing key:`, key);
            if (key && key.trim() && key.includes('marketplace/')) { // Chỉ xóa key hợp lệ có format đúng
              console.log('✅ [DELETE] Adding DELETE operation for key:', key);
              imageOperations.push({
                operation: 'DELETE',
                key: key,
              });
            } else {
              console.log('❌ [DELETE] Skipping invalid key:', key);
            }
          });
        }

        // Thêm ảnh mới nếu có
        if (values.imageFiles && values.imageFiles.length > 0) {
          console.log('➕ [IMAGE OPERATIONS] Processing ADD operations...');
          values.imageFiles.forEach((file, index) => {
            console.log(`✅ [ADD ${index}] Adding ADD operation for file:`, file.name, 'type:', file.type);
            imageOperations.push({
              operation: 'ADD',
              index: index + 1,
              mimeType: file.type,
            });
          });
        }

        console.log('🔍 [IMAGE OPERATIONS] Final operations:', imageOperations);

        // Debug flags
        console.log('🔍 [FLAGS] hasDetailChanged:', values.hasDetailChanged);
        console.log('🔍 [FLAGS] hasUserManualChanged:', values.hasUserManualChanged);
        console.log('🔍 [FLAGS] detail content:', values.detail?.substring(0, 50) + '...');
        console.log('🔍 [FLAGS] userManual content:', values.userManual?.substring(0, 50) + '...');

        // CẬP NHẬT sản phẩm hiện tại - KHÔNG tạo bản mới
        const updateData: UpdateProductDto = {
          productInfo: {
            name: values.name,
            description: values.description || '',
            // Backend yêu cầu listedPrice và discountedPrice bắt buộc
            listedPrice: values.price && values.price > 0 ? values.price : selectedProduct.listedPrice,
            discountedPrice: values.price && values.price > 0 ? values.price : selectedProduct.discountedPrice,
            // Note: User DTO không có category field
          },
          images: imageOperations, // Optional theo user DTO
          detailEdited: values.hasDetailChanged || false, // Sử dụng flag thay vì check content
          userManual: values.hasUserManualChanged || false, // Sử dụng flag thay vì check content
          updateOption: submitForApproval ? 'SUBMIT_FOR_APPROVAL' : 'SAVE_DRAFT',
        };

        console.log('🔍 UPDATING product ID:', selectedProduct.id);
        console.log('🔍 Selected product images:', selectedProduct.images);
        console.log('🔍 Old image keys:', values.oldImageKeys);
        console.log('🔍 Deleted image keys:', values.deletedImageKeys);
        console.log('🔍 Has image changed:', values.hasImageChanged);
        console.log('🔍 Image operations:', imageOperations);
        console.log('🔍 Update data:', JSON.stringify(updateData, null, 2));
        console.log('🔍 Image files:', values.imageFiles);

        // Gọi API cập nhật - KHÔNG tạo mới
        const updateResponse = await updateProductMutation.mutateAsync({
          productId: selectedProduct.id,
          data: updateData,
        });

        console.log('✅ Product updated successfully:', updateResponse);

        // Tạo mảng promises cho tất cả uploads
        const allUploadPromises: Promise<void>[] = [];

        // Upload detail content nếu có thay đổi
        if (values.hasDetailChanged && values.detail && updateResponse.presignedUrlDetail) {
          console.log('📤 [DETAIL] Uploading detail content to presigned URL...');
          const detailUploadPromise = fetch(updateResponse.presignedUrlDetail, {
            method: 'PUT',
            headers: {
              'Content-Type': 'text/plain',
            },
            body: values.detail,
          }).then(uploadResponse => {
            if (uploadResponse.ok) {
              console.log('✅ [DETAIL] Detail content uploaded successfully');
            } else {
              console.error('❌ [DETAIL] Failed to upload detail content:', uploadResponse.status);
              throw new Error(`Failed to upload detail content: ${uploadResponse.status}`);
            }
          });
          allUploadPromises.push(detailUploadPromise);
        }

        // Upload userManual content nếu có thay đổi
        if (values.hasUserManualChanged && values.userManual && updateResponse.presignedUrlUserManual) {
          console.log('📤 [USER_MANUAL] Uploading user manual content to presigned URL...');
          const userManualUploadPromise = fetch(updateResponse.presignedUrlUserManual, {
            method: 'PUT',
            headers: {
              'Content-Type': 'text/plain',
            },
            body: values.userManual,
          }).then(uploadResponse => {
            if (uploadResponse.ok) {
              console.log('✅ [USER_MANUAL] User manual content uploaded successfully');
            } else {
              console.error('❌ [USER_MANUAL] Failed to upload user manual content:', uploadResponse.status);
              throw new Error(`Failed to upload user manual content: ${uploadResponse.status}`);
            }
          });
          allUploadPromises.push(userManualUploadPromise);
        }

        // Upload images nếu có ảnh mới
        if (values.imageFiles && values.imageFiles.length > 0 && updateResponse.presignedUrlImage && updateResponse.presignedUrlImage.length > 0) {
          console.log('📤 [IMAGES] Uploading images to S3...');
          const imageUploadPromises = values.imageFiles.map(async (file, index) => {
            const presignedUrl = updateResponse.presignedUrlImage?.[index];
            if (!presignedUrl) return;

            const response = await fetch(presignedUrl.uploadUrl, {
              method: 'PUT',
              body: file,
              headers: {
                'Content-Type': file.type,
              },
            });

            if (response.ok) {
              console.log(`✅ [IMAGES] Uploaded image ${index + 1} successfully`);
            } else {
              console.error(`❌ [IMAGES] Failed to upload image ${index + 1}:`, response.status);
              throw new Error(`Failed to upload image ${index + 1}: ${response.status}`);
            }
          });
          allUploadPromises.push(...imageUploadPromises);
        }

        // Đợi tất cả uploads hoàn thành
        if (allUploadPromises.length > 0) {
          try {
            await Promise.all(allUploadPromises);
            console.log('🎉 All uploads completed successfully');
          } catch (error) {
            console.error('❌ Error during uploads:', error);
            // Không throw error để không block việc cập nhật thông tin khác
          }
        }
      } else {
        // Tạo sản phẩm mới
        const createData: CreateProductDto = {
          name: values.name,
          description: values.description || '',
          listedPrice: values.price || 0,
          discountedPrice: values.price || 0,
          category: (values.category as unknown) as 'AGENT' | 'KNOWLEDGE_FILE' | 'FUNCTION' | 'FINETUNE' | 'STRATEGY',
          sourceId: 'temp-source-id', // TODO: Cần xử lý sourceId thực tế
          imagesMediaTypes: values.imageFiles?.map(file => file.type) || [],
          userManualMediaType: values.userManual ? 'text/html' : undefined,
          detailMediaType: values.detail ? 'text/html' : undefined,
        };

        console.log('🔍 CREATING new product with data:', createData);
        console.log('🔍 Image files:', values.imageFiles);

        const createResponse = await createProductMutation.mutateAsync(createData);

        console.log('✅ Product created successfully:', createResponse);

        // Tạo mảng promises cho tất cả uploads
        const allUploadPromises: Promise<void>[] = [];

        // Upload detail content nếu có
        if (values.detail && createResponse.presignedUrlDetail) {
          console.log('📤 [CREATE-DETAIL] Uploading detail content to presigned URL...');
          const detailUploadPromise = fetch(createResponse.presignedUrlDetail, {
            method: 'PUT',
            headers: {
              'Content-Type': 'text/html',
            },
            body: values.detail,
          }).then(uploadResponse => {
            if (uploadResponse.ok) {
              console.log('✅ [CREATE-DETAIL] Detail content uploaded successfully');
            } else {
              console.error('❌ [CREATE-DETAIL] Failed to upload detail content:', uploadResponse.status);
              throw new Error(`Failed to upload detail content: ${uploadResponse.status}`);
            }
          });
          allUploadPromises.push(detailUploadPromise);
        }

        // Upload userManual content nếu có
        if (values.userManual && createResponse.presignedUrlUserManual) {
          console.log('📤 [CREATE-USER_MANUAL] Uploading user manual content to presigned URL...');
          const userManualUploadPromise = fetch(createResponse.presignedUrlUserManual, {
            method: 'PUT',
            headers: {
              'Content-Type': 'text/html',
            },
            body: values.userManual,
          }).then(uploadResponse => {
            if (uploadResponse.ok) {
              console.log('✅ [CREATE-USER_MANUAL] User manual content uploaded successfully');
            } else {
              console.error('❌ [CREATE-USER_MANUAL] Failed to upload user manual content:', uploadResponse.status);
              throw new Error(`Failed to upload user manual content: ${uploadResponse.status}`);
            }
          });
          allUploadPromises.push(userManualUploadPromise);
        }

        // Upload images nếu có ảnh mới
        if (values.imageFiles && values.imageFiles.length > 0 && createResponse.presignedUrlImage && createResponse.presignedUrlImage.length > 0) {
          console.log('📤 [CREATE-IMAGES] Uploading images to S3...');
          const imageUploadPromises = values.imageFiles.map(async (file, index) => {
            const presignedUrl = createResponse.presignedUrlImage?.[index];
            if (!presignedUrl) return;

            const response = await fetch(presignedUrl.uploadUrl, {
              method: 'PUT',
              body: file,
              headers: {
                'Content-Type': file.type,
              },
            });

            if (response.ok) {
              console.log(`✅ [CREATE-IMAGES] Uploaded image ${index + 1} successfully`);
            } else {
              console.error(`❌ [CREATE-IMAGES] Failed to upload image ${index + 1}:`, response.status);
              throw new Error(`Failed to upload image ${index + 1}: ${response.status}`);
            }
          });
          allUploadPromises.push(...imageUploadPromises);
        }

        // Đợi tất cả uploads hoàn thành
        if (allUploadPromises.length > 0) {
          try {
            await Promise.all(allUploadPromises);
            console.log('🎉 All create uploads completed successfully');
          } catch (error) {
            console.error('❌ Error during create uploads:', error);
            // Không throw error để không block việc tạo sản phẩm
          }
        }
      }

      setSelectedProduct(null);
      hideForm();
      // Navigate về list sau khi submit thành công
      if (productId) {
        navigate('/marketplace/products-for-sale');
      }
    } catch (error) {
      console.error('Error submitting product:', error);
      // Error đã được handle trong mutation hooks
    }
  };

  // Xử lý hủy form
  const handleCancel = () => {
    try {
      console.log('🔍 [CANCEL] Canceling form...');

      // Reset mutations nếu đang pending
      if (createProductMutation.isPending) {
        console.log('⚠️ [CANCEL] Create mutation is pending, resetting...');
        createProductMutation.reset();
      }

      if (updateProductMutation.isPending) {
        console.log('⚠️ [CANCEL] Update mutation is pending, resetting...');
        updateProductMutation.reset();
      }

      setSelectedProduct(null);
      hideForm();

      // Navigate về list nếu đang ở edit URL
      if (productId) {
        navigate('/marketplace/products-for-sale');
      }

      console.log('✅ [CANCEL] Form canceled successfully');
    } catch (error) {
      console.error('❌ [CANCEL] Error during cancel:', error);
      // Vẫn thực hiện cancel dù có lỗi
      setSelectedProduct(null);
      hideForm();
      if (productId) {
        navigate('/marketplace/products-for-sale');
      }
    }
  };

  // Xử lý hiển thị dialog xác nhận xóa
  const handleShowDeleteConfirm = (product: ApiProduct) => {
    setProductToDelete(product);
    setShowDeleteConfirm(true);
  };

  // Xử lý hủy xóa
  const handleCancelDelete = () => {
    setProductToDelete(null);
    setShowDeleteConfirm(false);
  };

  // Xử lý xác nhận xóa
  // const handleConfirmDelete = async () => {
  //   if (productToDelete) {
  //     try {
  //       console.log('🔍 [ProductsForSalePage] Starting delete for product:', productToDelete);
  //       await deleteProductMutation.mutateAsync(productToDelete.id);
  //       console.log('✅ [ProductsForSalePage] Delete completed successfully');
  //       setProductToDelete(null);
  //       setShowDeleteConfirm(false);

  //       // Force refetch để đảm bảo data được cập nhật
  //       console.log('🔍 [ProductsForSalePage] Forcing refetch...');
  //       await refetch();
  //       console.log('✅ [ProductsForSalePage] Refetch completed');
  //     } catch (error) {
  //       console.error('❌ [ProductsForSalePage] Error deleting product:', error);
  //       // Error đã được handle trong mutation hooks
  //     }
  //   }
  // };

  // Xử lý gửi sản phẩm để duyệt
  const handleSubmitForApproval = async (product: ApiProduct) => {
    try {
      await submitForApprovalMutation.mutateAsync(product.id);
    } catch (error) {
      console.error('Error submitting product for approval:', error);
      // Error đã được handle trong mutation hooks
    }
  };

  // Xử lý hủy gửi duyệt
  const handleCancelSubmission = async (product: ApiProduct) => {
    try {
      await cancelSubmissionMutation.mutateAsync(product.id);
    } catch (error) {
      console.error('Error canceling product submission:', error);
      // Error đã được handle trong mutation hooks
    }
  };

  // Hiển thị loading
  if (isLoading || isLoadingDetail) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading />
      </div>
    );
  }



  // Hiển thị lỗi
  if (error) {
    return (
      <Alert
        type="error"
        title={t('marketplace:productsForSale.error.loadDataTitle', 'Lỗi tải dữ liệu')}
        message={error.message}
        action={
          <Button variant="outline" onClick={() => refetch()}>
            {t('marketplace:productsForSale.error.retry', 'Thử lại')}
          </Button>
        }
      />
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={setSearchTerm}
        onAdd={handleAdd}
        items={[
          {
            id: 'all',
            label: t('common:all', 'Tất cả'),
            icon: 'list',
            onClick: () => setFilter('all'),
          },
          {
            id: 'APPROVED',
            label: t('marketplace:productsForSale.status.approved', 'Đã duyệt'),
            icon: 'check',
            onClick: () => setFilter('APPROVED'),
          },
          {
            id: 'PENDING',
            label: t('marketplace:productsForSale.status.pending', 'Chờ duyệt'),
            icon: 'clock',
            onClick: () => setFilter('PENDING'),
          },
          {
            id: 'DRAFT',
            label: t('marketplace:productsForSale.status.draft', 'Bản nháp'),
            icon: 'file',
            onClick: () => setFilter('DRAFT'),
          },
          {
            id: 'REJECTED',
            label: t('marketplace:productsForSale.status.rejected', 'Từ chối'),
            icon: 'x',
            onClick: () => setFilter('REJECTED'),
          },
        ]}
      />

      <SlideInForm isVisible={isVisible}>
        <ProductForSaleForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={createProductMutation.isPending || updateProductMutation.isPending}
          initialValues={
            selectedProduct
              ? ({
                  name: selectedProduct.name,
                  description: selectedProduct.description,
                  images: selectedProduct.images && selectedProduct.images.length > 0
                    ? selectedProduct.images.map(img =>
                        typeof img === 'string' ? img : img?.url || ''
                      ).filter(Boolean)
                    : [],
                  oldImageKeys: selectedProduct.images && selectedProduct.images.length > 0
                    ? (() => {
                        console.log('🔍 Processing selectedProduct.images:', selectedProduct.images);
                        const validImages = selectedProduct.images.filter(img => {
                          const isValid = img && typeof img === 'object' && img.key;
                          console.log('🔍 Image:', img, 'isValid:', isValid);
                          return isValid;
                        });
                        const keys = validImages.map(img => img.key).filter(Boolean) as string[];
                        console.log('🔍 Extracted keys:', keys);
                        return keys;
                      })()
                    : [],
                  price: selectedProduct.discountedPrice || 0,
                  category: selectedProduct.category, // Use as-is, form will handle type compatibility
                  detail: selectedProduct.detail || '', // Existing detail content
                  userManual: selectedProduct.userManual || '', // Existing userManual content
                } as unknown as Partial<ProductForSaleFormValues & { oldImageKeys?: string[]; images?: string[]; detail?: string; userManual?: string }>)
              : undefined
          }
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table columns={columns} data={filteredData} rowKey="id" pagination />
      </Card>

      {/* Confirmation Modal for Delete */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('marketplace:productsForSale.delete.confirmTitle', 'Xác nhận xóa')}
        size="sm"
      >
        <div className="p-6">
          <div className="flex items-center mb-4">
            <div className="flex-shrink-0 w-10 h-10 mx-auto bg-red-100 rounded-full flex items-center justify-center">
              <Icon name="trash" size="md" className="text-red-600" />
            </div>
            <div className="ml-4">
              <Typography variant="h6">
                {t('marketplace:productsForSale.delete.confirmTitle', 'Xác nhận xóa')}
              </Typography>
              <Typography variant="body2" >
                {t('marketplace:productsForSale.delete.confirmMessage', 'Bạn có chắc chắn muốn xóa sản phẩm "{{name}}" không?', { name: productToDelete?.name })}
              </Typography>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ProductsForSalePage;
