/**
 * <PERSON><PERSON><PERSON> nghĩa các kiểu dữ liệu cho module marketplace
 * Cập nhật để phù hợp với backend API
 */

/**
 * Enum cho loại sản phẩm (từ backend)
 */
export enum ProductCategory {
  AGENT = 'AGENT',
  KNOWLEDGE_FILE = 'KNOWLEDGE_FILE',
  FUNCTION = 'FUNCTION',
  FINETUNE = 'FINETUNE',
  STRATEGY = 'STRATEGY'
}

/**
 * Enum cho trạng thái sản phẩm (từ backend)
 */
export enum ProductStatus {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DELETED = 'DELETED'
}

/**
 * Interface cho category hiển thị
 */
export interface ProductCategoryDisplay {
  id: string;
  name: string;
  slug: string;
}

export interface ProductBrand {
  id: string;
  name: string;
  logo?: string;
}

export interface ProductReview {
  id: string;
  rating: number;
  comment: string;
  userName: string;
  userAvatar?: string;
  createdAt: string;
}

/**
 * Interface cho thông tin người bán (từ backend API)
 */
export interface Seller {
  id?: number;
  name: string;
  avatar: string;
  email?: string;
  phoneNumber?: string;
  type: 'user' | 'employee';
}

/**
 * Interface cho sản phẩm trong danh sách (từ backend API)
 */
export interface ProductListItem {
  id: number; // Backend trả về number
  name: string;
  description: string;
  listedPrice: number; // Giá niêm yết
  discountedPrice: number; // Giá sau giảm
  category: ProductCategory; // Enum từ backend
  status?: ProductStatus; // Trạng thái sản phẩm
  images: Array<{ key: string; position: number; url: string }>; // Mảng ảnh từ backend
  seller: Seller;
  createdAt: number; // Timestamp từ backend
  soldCount: number;
  canPurchase: boolean;
  userManual?: string; // URL tài liệu hướng dẫn
  detail?: string; // URL thông tin chi tiết
  sourceId: string; // ID nguồn tài nguyên

  // Computed fields cho UI
  slug?: string;
  thumbnail?: string; // Lấy từ images[0]
  price?: number; // Alias cho discountedPrice
  originalPrice?: number; // Alias cho listedPrice
  discount?: number; // Tính từ listedPrice - discountedPrice
  rating?: number; // Mock data cho UI
  reviewCount?: number; // Mock data cho UI
  inStock?: boolean; // Luôn true cho digital products
  stockQuantity?: number; // Luôn 999 cho digital products
  categoryDisplay?: ProductCategoryDisplay; // Category hiển thị
  brand?: ProductBrand; // Mock data cho UI
  isNew?: boolean; // Computed từ createdAt
  isFeatured?: boolean; // Mock data cho UI
  tags?: string[]; // Mock data cho UI
}

export interface ProductDetail extends Omit<ProductListItem, 'images'> {
  images: string[]; // Array of image URLs for gallery (override từ ProductListItem)
  specifications: Record<string, string>;
  reviews: ProductReview[];
  relatedProducts: ProductListItem[];
  detailHtml?: string; // Nội dung HTML cho thông tin chi tiết
  usageGuideHtml?: string; // Nội dung HTML cho hướng dẫn sử dụng

  // Thêm các field từ backend API
  isPurchased?: boolean; // Người dùng đã mua sản phẩm này chưa
  userManualUrl?: string; // URL tài liệu hướng dẫn từ backend
  detailUrl?: string; // URL thông tin chi tiết từ backend
}

export interface ProductListResponse {
  data: ProductListItem[];
  total: number;
  page: number;
  limit: number;
}

export interface ProductDetailResponse {
  data: ProductDetail;
}

export interface ProductFilterOptions {
  categories: ProductCategory[];
  brands: ProductBrand[];
  priceRange: {
    min: number;
    max: number;
  };
}

export interface ProductListParams {
  page: number;
  limit: number;
  search?: string;
  category?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: 'price' | 'rating' | 'newest';
  sortOrder?: 'asc' | 'desc';
}
