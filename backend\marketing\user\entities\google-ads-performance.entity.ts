import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng google_ads_performance trong cơ sở dữ liệu
 * Lưu trữ thông tin hiệu suất chiến dịch Google Ads
 */
@Entity('google_ads_performance')
export class GoogleAdsPerformance {
  /**
   * ID của báo cáo hiệu suất trong hệ thống
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })
  userId: number;

  /**
   * ID của chiến dịch trong hệ thống
   */
  @Column({ name: 'campaign_id', nullable: false, comment: 'ID của chiến dịch trong hệ thống' })
  campaignId: number;

  /**
   * <PERSON><PERSON><PERSON> của báo cáo (YYYY-MM-DD)
   */
  @Column({ name: 'date', length: 10, nullable: false, comment: '<PERSON><PERSON><PERSON> của báo cáo (YYYY-MM-DD)' })
  date: string;

  /**
   * Số lần hiển thị
   */
  @Column({ name: 'impressions', type: 'integer', nullable: false, default: 0, comment: 'Số lần hiển thị' })
  impressions: number;

  /**
   * Số lần nhấp chuột
   */
  @Column({ name: 'clicks', type: 'integer', nullable: false, default: 0, comment: 'Số lần nhấp chuột' })
  clicks: number;

  /**
   * Chi phí (micro amount)
   */
  @Column({ name: 'cost', type: 'bigint', nullable: false, default: 0, comment: 'Chi phí (micro amount)' })
  cost: number;

  /**
   * Tỷ lệ nhấp chuột (%)
   */
  @Column({ name: 'ctr', type: 'float', nullable: false, default: 0, comment: 'Tỷ lệ nhấp chuột (%)' })
  ctr: number;

  /**
   * CPC trung bình
   */
  @Column({ name: 'average_cpc', type: 'bigint', nullable: false, default: 0, comment: 'CPC trung bình' })
  averageCpc: number;

  /**
   * Số lượt chuyển đổi
   */
  @Column({ name: 'conversions', type: 'float', nullable: false, default: 0, comment: 'Số lượt chuyển đổi' })
  conversions: number;

  /**
   * Giá trị chuyển đổi
   */
  @Column({ name: 'conversion_value', type: 'float', nullable: false, default: 0, comment: 'Giá trị chuyển đổi' })
  conversionValue: number;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;
}
