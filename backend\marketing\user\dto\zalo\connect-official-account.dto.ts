import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

/**
 * DTO cho vi<PERSON><PERSON> kế<PERSON> nối Official Account
 */
export class ConnectOfficialAccountDto {
  @ApiProperty({
    description: 'Access token của Official Account',
    example: 'abcdef123456789',
  })
  @IsString()
  @IsNotEmpty()
  accessToken: string;

  @ApiProperty({
    description: 'Refresh token của Official Account',
    example: 'refresh123456789',
  })
  @IsString()
  @IsNotEmpty()
  refreshToken: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của access token (Unix timestamp)',
    example: *************,
  })
  @IsNumber()
  @IsNotEmpty()
  expiresAt: number;
}
