import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { CustomFieldType } from './custom-field-response.dto';

/**
 * DTO cho tạo trường tùy chỉnh
 */
export class CreateCustomFieldDto {
  /**
   * Tên trường tùy chỉnh
   * @example "Địa chỉ"
   */
  @ApiProperty({
    description: 'Tên trường tùy chỉnh',
    example: 'Địa chỉ',
  })
  @IsNotEmpty({ message: 'Tên trường tùy chỉnh không được để trống' })
  @IsString({ message: 'Tên trường tùy chỉnh phải là chuỗi' })
  fieldName: string;

  /**
   * Giá trị trường tùy chỉnh
   * @example "Hà Nội, Việt Nam"
   */
  @ApiProperty({
    description: 'Giá trị trường tùy chỉnh',
    example: '<PERSON><PERSON>, Vi<PERSON>t Nam',
  })
  @IsNotEmpty({ message: '<PERSON>i<PERSON> trị trường tùy chỉnh không được để trống' })
  @IsString({ message: 'Giá trị trường tùy chỉnh phải là chuỗi' })
  fieldValue: string;

  /**
   * Loại trường tùy chỉnh
   * @example "TEXT"
   */
  @ApiProperty({
    description: 'Loại trường tùy chỉnh',
    enum: CustomFieldType,
    example: CustomFieldType.TEXT,
  })
  @IsNotEmpty({ message: 'Loại trường tùy chỉnh không được để trống' })
  @IsEnum(CustomFieldType, {
    message: `Loại trường tùy chỉnh phải là một trong các giá trị: ${Object.values(CustomFieldType).join(', ')}`,
  })
  fieldType: CustomFieldType;
}
