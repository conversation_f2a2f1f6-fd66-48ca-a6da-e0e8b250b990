import { useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { NotificationUtil } from '@/shared/utils/notification';
import { useRPoint } from '@/shared/contexts/useRPoint';
import { ProductDetail } from '../types/product.types';
import {
  addToCart as addToCartAction,
  removeFromCart as removeFromCartAction,
  updateQuantity as updateQuantityAction,
  clearCart as clearCartAction,
  selectItem,
  selectAll,
  setCheckingOut,
  updateSoldCount as updateSoldCountAction,
  removeSelectedItems,
} from '@/shared/store/slices/cartSlice';
import { useAppDispatch as useDispatch, useAppSelector as useSelector } from '@/shared/store';

/**
 * Hook để quản lý giỏ hàng sử dụng Redux
 */
export const useCartRedux = () => {
  const { t } = useTranslation(['marketplace', 'common']);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { deductPoints } = useRPoint();

  // Lấy state từ Redux store
  const cartItems = useSelector(state => state.cart.items);
  const selectedItems = useSelector(state => state.cart.selectedItems);
  const isCheckingOut = useSelector(state => state.cart.isCheckingOut);
  const soldCounts = useSelector(state => state.cart.soldCounts);
  const discount = useSelector(state => state.cart.discount);

  // Tính tổng số sản phẩm trong giỏ hàng
  const totalItems = useMemo(() => {
    return cartItems.reduce((sum, item) => sum + item.quantity, 0);
  }, [cartItems]);

  // Thêm sản phẩm vào giỏ hàng
  const addToCart = useCallback(
    (product: ProductDetail, quantity: number) => {
      dispatch(addToCartAction({ product, quantity }));
    },
    [dispatch]
  );

  // Xóa sản phẩm khỏi giỏ hàng
  const removeFromCart = useCallback(
    (itemId: number) => {
      dispatch(removeFromCartAction(itemId));
    },
    [dispatch]
  );

  // Cập nhật số lượng sản phẩm
  const updateQuantity = useCallback(
    (itemId: number, quantity: number) => {
      dispatch(updateQuantityAction({ itemId, quantity }));
    },
    [dispatch]
  );

  // Xóa tất cả sản phẩm trong giỏ hàng
  const clearCart = useCallback(() => {
    dispatch(clearCartAction());
  }, [dispatch]);

  // Chọn/bỏ chọn sản phẩm
  const handleSelectItem = useCallback(
    (itemId: number, selected: boolean) => {
      dispatch(selectItem({ itemId, selected }));
    },
    [dispatch]
  );

  // Chọn/bỏ chọn tất cả sản phẩm
  const handleSelectAll = useCallback(
    (selected: boolean) => {
      dispatch(selectAll(selected));
    },
    [dispatch]
  );

  // Cập nhật số lượng đã bán
  const updateSoldCount = useCallback(
    (productId: number) => {
      dispatch(updateSoldCountAction(productId));
    },
    [dispatch]
  );

  // Tính toán tổng tiền
  const calculateTotals = useCallback(() => {
    const selectedCartItems = cartItems.filter(item => selectedItems.includes(item.id));

    const totalItems = selectedCartItems.reduce((sum, item) => sum + item.quantity, 0);
    const subtotal = selectedCartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const total = subtotal - discount;

    return {
      totalItems,
      subtotal,
      discount,
      total,
    };
  }, [cartItems, selectedItems, discount]);

  // Xử lý thanh toán
  const checkout = useCallback(() => {
    dispatch(setCheckingOut(true));

    // Kiểm tra xem có sản phẩm nào được chọn không
    if (selectedItems.length === 0) {
      NotificationUtil.warning({
        title: t('marketplace:cart.noItemSelected', 'Chưa chọn sản phẩm'),
        message: t(
          'marketplace:cart.selectItemMessage',
          'Vui lòng chọn ít nhất một sản phẩm để thanh toán'
        ),
        duration: 3000,
      });
      dispatch(setCheckingOut(false));
      return;
    }

    // Tính tổng tiền cần thanh toán
    const { total } = calculateTotals();

    // Trừ điểm của người dùng
    if (deductPoints(total)) {
      // Nếu trừ điểm thành công, xử lý thanh toán
      setTimeout(() => {
        // Cập nhật số lượng đã bán cho mỗi sản phẩm
        const selectedCartItems = cartItems.filter(item => selectedItems.includes(item.id));
        selectedCartItems.forEach(item => {
          dispatch(updateSoldCountAction(item.id));
        });

        // Hiển thị thông báo thành công
        NotificationUtil.success({
          title: t('marketplace:cart.checkoutSuccess', 'Thanh toán thành công'),
          message: t(
            'marketplace:cart.checkoutSuccessMessage',
            'Đơn hàng của bạn đã được xử lý thành công'
          ),
          duration: 3000,
          action: {
            label: t('marketplace:cart.viewProducts', 'Xem sản phẩm'),
            onClick: () => navigate('/marketplace'),
          },
        });

        // Xóa các sản phẩm đã chọn khỏi giỏ hàng
        dispatch(removeSelectedItems());
        dispatch(setCheckingOut(false));

        // Chuyển hướng đến trang marketplace sau khi thanh toán thành công
        setTimeout(() => {
          navigate('/marketplace');
        }, 3000);
      }, 1000);
    } else {
      // Nếu không đủ điểm, hiển thị thông báo lỗi
      NotificationUtil.error({
        title: t('marketplace:cart.notEnoughPoints', 'Không đủ R-Point'),
        message: t(
          'marketplace:cart.notEnoughPointsMessage',
          'Bạn không đủ R-Point để thanh toán. Vui lòng nạp thêm!'
        ),
        duration: 5000,
        action: {
          label: t('marketplace:cart.addPoints', 'Nạp R-Point'),
          onClick: () => navigate('/rpoint/packages'),
        },
      });
      dispatch(setCheckingOut(false));
    }
  }, [dispatch, selectedItems, cartItems, calculateTotals, deductPoints, navigate, t]);

  return {
    cartItems,
    selectedItems,
    isCheckingOut,
    soldCounts,
    totalItems,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    handleSelectItem,
    handleSelectAll,
    calculateTotals,
    checkout,
    updateSoldCount,
  };
};
