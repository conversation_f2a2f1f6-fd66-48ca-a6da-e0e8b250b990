import { z } from 'zod';
import {
  EmailTemplateType,
  EmailTemplateStatus,
  EmailCampaignStatus
} from '../types/email.types';

/**
 * Schema cho Email Variable
 */
export const emailVariableSchema = z.object({
  name: z.string().min(1, 'Tên biến là bắt buộc'),
  type: z.enum(['TEXT', 'NUMBER', 'DATE', 'URL', 'IMAGE']),
  defaultValue: z.string().optional(),
  required: z.boolean(),
  description: z.string().optional(),
});

/**
 * Schema cho tạo Email Template
 */
export const createEmailTemplateSchema = z.object({
  name: z.string().min(1, 'Tên template là bắt buộc'),
  subject: z.string().min(1, 'Tiêu đề email là bắt buộc'),
  htmlContent: z.string().min(1, 'Nội dung HTML là bắt buộc'),
  textContent: z.string().optional(),
  type: z.nativeEnum(EmailTemplateType),
  previewText: z.string().max(150, 'Preview text không được vượt quá 150 ký tự').optional(),
  tags: z.array(z.string()).optional(),
  variables: z.array(emailVariableSchema).optional(),
});

export type CreateEmailTemplateFormData = z.infer<typeof createEmailTemplateSchema>;

/**
 * Schema cho cập nhật Email Template
 */
export const updateEmailTemplateSchema = z.object({
  name: z.string().min(1, 'Tên template là bắt buộc').optional(),
  subject: z.string().min(1, 'Tiêu đề email là bắt buộc').optional(),
  htmlContent: z.string().min(1, 'Nội dung HTML là bắt buộc').optional(),
  textContent: z.string().optional(),
  type: z.nativeEnum(EmailTemplateType).optional(),
  status: z.nativeEnum(EmailTemplateStatus).optional(),
  previewText: z.string().max(150, 'Preview text không được vượt quá 150 ký tự').optional(),
  tags: z.array(z.string()).optional(),
  variables: z.array(emailVariableSchema).optional(),
});

export type UpdateEmailTemplateFormData = z.infer<typeof updateEmailTemplateSchema>;

/**
 * Schema cho query Email Templates
 */
export const emailTemplateQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  type: z.nativeEnum(EmailTemplateType).optional(),
  status: z.nativeEnum(EmailTemplateStatus).optional(),
  tags: z.array(z.string()).optional(),
  sortBy: z.string().default('createdAt'),
  sortDirection: z.enum(['ASC', 'DESC']).default('DESC'),
});

export type EmailTemplateQueryFormData = z.infer<typeof emailTemplateQuerySchema>;

/**
 * Schema cho tạo Email Campaign
 */
export const createEmailCampaignSchema = z.object({
  name: z.string().min(1, 'Tên chiến dịch là bắt buộc'),
  subject: z.string().min(1, 'Tiêu đề email là bắt buộc'),
  templateId: z.string().min(1, 'Template là bắt buộc'),
  audienceIds: z.array(z.string()).optional(),
  segmentIds: z.array(z.string()).optional(),
  scheduledAt: z.date().optional(),
});

export type CreateEmailCampaignFormData = z.infer<typeof createEmailCampaignSchema>;

/**
 * Schema cho query Email Campaigns
 */
export const emailCampaignQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  status: z.nativeEnum(EmailCampaignStatus).optional(),
  templateId: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  sortBy: z.string().default('createdAt'),
  sortDirection: z.enum(['ASC', 'DESC']).default('DESC'),
});

export type EmailCampaignQueryFormData = z.infer<typeof emailCampaignQuerySchema>;

/**
 * Schema cho gửi test email
 */
export const sendTestEmailSchema = z.object({
  templateId: z.string().min(1, 'Template là bắt buộc'),
  recipientEmails: z.array(z.string().email('Email không hợp lệ'))
    .min(1, 'Ít nhất một email người nhận'),
  variables: z.record(z.string()).optional(),
});

export type SendTestEmailFormData = z.infer<typeof sendTestEmailSchema>;

/**
 * Schema cho Email Automation Trigger
 */
export const emailTriggerSchema = z.object({
  type: z.enum(['AUDIENCE_JOINED', 'TAG_ADDED', 'DATE_BASED', 'BEHAVIOR', 'API_CALL']),
  conditions: z.record(z.unknown()).default({}),
  delay: z.number().min(0).optional(),
});

/**
 * Schema cho Email Automation Action
 */
export const emailActionSchema = z.object({
  id: z.string(),
  type: z.enum(['SEND_EMAIL', 'ADD_TAG', 'REMOVE_TAG', 'WAIT', 'CONDITION']),
  templateId: z.string().optional(),
  delay: z.number().min(0).optional(),
  conditions: z.record(z.unknown()).optional(),
  nextActionId: z.string().optional(),
});

/**
 * Schema cho tạo Email Automation
 */
export const createEmailAutomationSchema = z.object({
  name: z.string().min(1, 'Tên automation là bắt buộc'),
  description: z.string().optional(),
  trigger: emailTriggerSchema,
  actions: z.array(emailActionSchema).min(1, 'Ít nhất một action'),
});

export type CreateEmailAutomationFormData = z.infer<typeof createEmailAutomationSchema>;

/**
 * Schema cho bulk email operations
 */
export const bulkEmailOperationSchema = z.object({
  campaignIds: z.array(z.string()).min(1, 'Ít nhất một chiến dịch'),
  operation: z.enum(['PAUSE', 'RESUME', 'CANCEL', 'DELETE']),
});

export type BulkEmailOperationFormData = z.infer<typeof bulkEmailOperationSchema>;

/**
 * Schema cho import email list
 */
export const importEmailListSchema = z.object({
  file: z.instanceof(File)
    .refine((file) => file.size <= 5 * 1024 * 1024, 'File không được vượt quá 5MB')
    .refine(
      (file) => ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].includes(file.type),
      'Chỉ hỗ trợ file CSV hoặc Excel'
    ),
  audienceId: z.string().min(1, 'Audience là bắt buộc'),
  mapping: z.record(z.string()).default({}),
});

export type ImportEmailListFormData = z.infer<typeof importEmailListSchema>;

/**
 * Schema cho email analytics query
 */
export const emailAnalyticsQuerySchema = z.object({
  period: z.enum(['TODAY', 'WEEK', 'MONTH', 'YEAR']).default('WEEK'),
  campaignIds: z.array(z.string()).optional(),
  templateIds: z.array(z.string()).optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

export type EmailAnalyticsQueryFormData = z.infer<typeof emailAnalyticsQuerySchema>;

/**
 * Schema cho email personalization
 */
export const emailPersonalizationSchema = z.object({
  recipientEmail: z.string().email('Email không hợp lệ'),
  variables: z.record(z.string()).default({}),
  segmentData: z.record(z.unknown()).optional(),
});

export type EmailPersonalizationFormData = z.infer<typeof emailPersonalizationSchema>;

/**
 * Schema cho email A/B testing
 */
export const emailAbTestSchema = z.object({
  name: z.string().min(1, 'Tên A/B test là bắt buộc'),
  testType: z.enum(['SUBJECT', 'CONTENT', 'SENDER', 'TIME']),
  variantA: z.object({
    templateId: z.string(),
    subject: z.string().optional(),
    senderName: z.string().optional(),
    sendTime: z.date().optional(),
  }),
  variantB: z.object({
    templateId: z.string(),
    subject: z.string().optional(),
    senderName: z.string().optional(),
    sendTime: z.date().optional(),
  }),
  trafficSplit: z.number().min(10).max(90).default(50),
  winnerCriteria: z.enum(['OPEN_RATE', 'CLICK_RATE', 'CONVERSION_RATE']),
  testDuration: z.number().min(1).max(168).default(24), // hours
});

export type EmailAbTestFormData = z.infer<typeof emailAbTestSchema>;
