import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Enum cho các trường sắp xếp
 */
export enum SegmentSortField {
  ID = 'id',
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho thứ tự sắp xếp
 */
export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * DTO cho query parameters khi lấy danh sách segment
 */
export class SegmentQueryDto {
  /**
   * Trang hiện tại (bắt đầu từ 1)
   * @example 1
   */
  @ApiProperty({
    description: 'Trang hiện tại (bắt đầu từ 1)',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Trang phải là số nguyên' })
  @Min(1, { message: 'Trang phải lớn hơn hoặc bằng 1' })
  @Type(() => Number)
  page?: number = 1;

  /**
   * Số lượng item trên mỗi trang
   * @example 10
   */
  @ApiProperty({
    description: 'Số lượng item trên mỗi trang',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Số lượng item phải là số nguyên' })
  @Min(1, { message: 'Số lượng item phải lớn hơn hoặc bằng 1' })
  @Type(() => Number)
  limit?: number = 10;

  /**
   * Tìm kiếm theo tên
   * @example "VIP"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tên',
    example: 'VIP',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi' })
  name?: string;

  /**
   * Sắp xếp theo trường
   * @example "createdAt"
   */
  @ApiProperty({
    description: 'Sắp xếp theo trường',
    enum: SegmentSortField,
    example: SegmentSortField.CREATED_AT,
    default: SegmentSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(SegmentSortField, {
    message: `Trường sắp xếp phải là một trong các giá trị: ${Object.values(SegmentSortField).join(', ')}`,
  })
  sortBy?: SegmentSortField = SegmentSortField.CREATED_AT;

  /**
   * Thứ tự sắp xếp
   * @example "DESC"
   */
  @ApiProperty({
    description: 'Thứ tự sắp xếp',
    enum: SortOrder,
    example: SortOrder.DESC,
    default: SortOrder.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortOrder, {
    message: `Thứ tự sắp xếp phải là một trong các giá trị: ${Object.values(SortOrder).join(', ')}`,
  })
  sortOrder?: SortOrder = SortOrder.DESC;
}
