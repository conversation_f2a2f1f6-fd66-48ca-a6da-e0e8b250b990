import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity cho log tự động hóa Zalo
 */
@Entity('zalo_automation_logs')
export class ZaloAutomationLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'automation_id' })
  automationId: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'oa_id' })
  oaId: string;

  @Column({ name: 'follower_id' })
  followerId: number;

  @Column({ name: 'follower_user_id' })
  followerUserId: string;

  @Column({ name: 'trigger_type' })
  triggerType: string;

  @Column({ name: 'action_type' })
  actionType: string;

  @Column()
  status: string;

  @Column({ nullable: true })
  error?: string;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;
}
