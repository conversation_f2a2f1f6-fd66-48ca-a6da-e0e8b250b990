# AI Agents Module - API Structure

Cấu trúc API của module AI Agents đã được chuẩn hóa theo pattern của blog module.

## 📁 Cấu trúc thư mục

```
src/modules/ai-agents/
├── api/                    # API layer - Raw API calls
│   ├── agent.api.ts       # Agent & Type Agent API functions
│   └── index.ts
├── constants/             # Query keys và constants
│   ├── agent-query-keys.ts
│   └── index.ts
├── services/              # Business logic layer
│   ├── agent.service.ts   # Agent business logic
│   ├── type-agent.service.ts
│   └── index.ts
├── hooks/                 # React Query hooks
│   ├── useAgent.ts        # Direct API hooks
│   ├── useTypeAgent.ts    # Direct API hooks
│   ├── useAgentService.ts # Service hooks with business logic
│   ├── useAgentManagement.ts # Composite hook
│   └── index.ts
└── types/                 # TypeScript interfaces
    └── ...
```

## 🔄 Layers và Responsibilities

### 1. API Layer (`api/`)
- **Mục đích**: Raw API calls, không chứa business logic
- **Chức năng**: 
  - Gọi API endpoints
  - Type definitions cho request/response
  - Error handling cơ bản

```typescript
// Example
export const getAgents = async (params?: GetAgentsQueryDto): Promise<ApiResponse<AgentListResponse>> => {
  return apiClient.get('/user/agents', { params });
};
```

### 2. Services Layer (`services/`)
- **Mục đích**: Business logic, data transformation
- **Chức năng**:
  - Validate input data
  - Transform data format
  - Set default values
  - Complex business rules

```typescript
// Example
export const createAgentWithBusinessLogic = async (data: CreateAgentDto) => {
  // Set default model config
  const processedData = {
    ...data,
    modelConfig: {
      modelId: 'gpt-4o',
      temperature: 0.7,
      ...data.modelConfig,
    },
  };
  
  return createAgent(processedData);
};
```

### 3. Hooks Layer (`hooks/`)
- **Mục đích**: React Query integration
- **Types**:
  - **Direct API hooks**: `useGetAgents`, `useGetAgentDetail`
  - **Service hooks**: `useGetAgentsWithService` (with business logic)
  - **Composite hooks**: `useAgentManagement` (multiple operations)

## 📋 Usage Patterns

### 1. Simple API Call (Direct)
```typescript
import { useGetAgents } from '@/modules/ai-agents';

const { data, isLoading } = useGetAgents({ page: 1, limit: 10 });
```

### 2. With Business Logic (Service)
```typescript
import { useGetAgentsWithService } from '@/modules/ai-agents';

const { data, isLoading } = useGetAgentsWithService({ page: 1, limit: 10 });
```

### 3. Composite Operations (Management)
```typescript
import { useAgentManagement } from '@/modules/ai-agents';

const {
  agents,
  typeAgents,
  handleCreateAgent,
  handleSearch,
  isLoading
} = useAgentManagement();
```

## 🔑 Query Keys

Centralized trong `constants/agent-query-keys.ts`:

```typescript
export const AGENT_QUERY_KEYS = {
  AGENT_LIST: 'agent-list',
  AGENT_DETAIL: 'agent-detail',
  TYPE_AGENT_LIST: 'type-agent-list',
  // ...
} as const;
```

## 🎯 Best Practices

### 1. Khi nào sử dụng layer nào?

- **API Layer**: Khi cần raw API call, testing, hoặc custom logic
- **Services Layer**: Khi cần business logic, validation, transformation
- **Hooks Layer**: Trong React components, với React Query features

### 2. Error Handling

```typescript
// API Layer: Basic error handling
export const getAgents = async (params) => {
  return apiClient.get('/user/agents', { params });
};

// Services Layer: Business error handling
export const getAgentsWithBusinessLogic = async (params) => {
  // Validate params
  if (params?.limit && params.limit > 100) {
    throw new Error('Limit cannot exceed 100');
  }
  
  return getAgents(params);
};

// Hooks Layer: React Query error handling
export const useGetAgentsWithService = (params, options) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AGENT_LIST, params],
    queryFn: () => getAgentsWithBusinessLogic(params),
    onError: (error) => {
      console.error('Failed to fetch agents:', error);
    },
    ...options,
  });
};
```

### 3. Data Transformation

```typescript
// Services Layer
export const getTypeAgentsForSelection = async () => {
  const params = {
    status: 'APPROVED',
    isSystem: true,
    limit: 100,
    sort: 'name',
    order: 'ASC',
  };
  
  return getTypeAgents(params);
};
```

## 🔄 Migration từ cấu trúc cũ

### Trước (Old Structure)
```typescript
// services/agent.service.ts - Mixed API + business logic
export const AgentService = {
  getAgents: (params) => apiClient.get('/agents', { params }),
  // ...
};

// hooks/useAgent.ts
const { data } = useQuery({
  queryFn: () => AgentService.getAgents(params)
});
```

### Sau (New Structure)
```typescript
// api/agent.api.ts - Pure API
export const getAgents = (params) => apiClient.get('/agents', { params });

// services/agent.service.ts - Business logic
export const getAgentsWithBusinessLogic = (params) => {
  // Business logic here
  return getAgents(params);
};

// hooks/useAgent.ts - React Query integration
export const useGetAgents = (params) => useQuery({
  queryFn: () => getAgents(params)
});

export const useGetAgentsWithService = (params) => useQuery({
  queryFn: () => getAgentsWithBusinessLogic(params)
});
```

## 📊 Benefits

1. **Separation of Concerns**: Mỗi layer có responsibility rõ ràng
2. **Reusability**: API functions có thể reuse ở nhiều nơi
3. **Testability**: Dễ test từng layer riêng biệt
4. **Maintainability**: Dễ maintain và extend
5. **Consistency**: Theo chuẩn của dự án (blog module pattern)
