import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { UserAudienceService } from '../services/user-audience.service';
import { CreateAudienceDto, UpdateAudienceDto, AudienceResponseDto, AudienceQueryDto } from '../dto/audience';
import { PaginatedResponseDto } from '../dto/common';
import { JwtUserGuard } from '@/modules/auth/guards';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý API liên quan đến audience
 */
@ApiTags(SWAGGER_API_TAGS.USER_AUDIENCE)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/audiences')
export class UserAudienceController {
  constructor(private readonly userAudienceService: UserAudienceService) {}

  /**
   * Tạo audience mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo audience mới' })
  @ApiResponse({ status: 201, description: 'Audience đã được tạo thành công', type: AudienceResponseDto })
  async create(@CurrentUser() user: JwtPayload, @Body() createAudienceDto: CreateAudienceDto): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.userAudienceService.create(user.id, createAudienceDto);
    return wrapResponse(result, 'Audience đã được tạo thành công');
  }

  /**
   * Lấy danh sách audience với phân trang và filter
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách audience với phân trang và filter' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách audience với phân trang',
    type: PaginatedResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/PaginatedResponseDto' },
        {
          properties: {
            data: {
              type: 'array',
              items: { $ref: '#/components/schemas/AudienceResponseDto' }
            }
          }
        }
      ]
    }
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() query: AudienceQueryDto
  ): Promise<AppApiResponse<PaginatedResponseDto<AudienceResponseDto>>> {
    const result = await this.userAudienceService.findAll(user.id, query);
    return wrapResponse(result, 'Danh sách audience');
  }

  /**
   * Lấy thông tin audience theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin audience theo ID' })
  @ApiResponse({ status: 200, description: 'Thông tin audience', type: AudienceResponseDto })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async findOne(@CurrentUser() user: JwtPayload, @Param('id') id: string): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.userAudienceService.findOne(user.id, +id);
    return wrapResponse(result, 'Thông tin audience');
  }

  /**
   * Cập nhật audience
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật audience' })
  @ApiResponse({ status: 200, description: 'Audience đã được cập nhật thành công', type: AudienceResponseDto })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() updateAudienceDto: UpdateAudienceDto,
  ): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.userAudienceService.update(user.id, +id, updateAudienceDto);
    return wrapResponse(result, 'Audience đã được cập nhật thành công');
  }

  /**
   * Xóa audience
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa audience' })
  @ApiResponse({ status: 200, description: 'Audience đã được xóa thành công' })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async remove(@CurrentUser() user: JwtPayload, @Param('id') id: string): Promise<AppApiResponse<{ success: boolean }>> {
    const result = await this.userAudienceService.remove(user.id, +id);
    return wrapResponse({ success: result }, 'Audience đã được xóa thành công');
  }
}
