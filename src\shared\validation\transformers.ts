import { ValidationTransformer, ValidationContext } from './types';

/**
 * Common data transformers for validation
 */
export const ValidationTransformers = {
  /**
   * Trim whitespace from string
   */
  trim: (): ValidationTransformer<string, string> => {
    return (value: string) => {
      return typeof value === 'string' ? value.trim() : value;
    };
  },

  /**
   * Convert to lowercase
   */
  toLowerCase: (): ValidationTransformer<string, string> => {
    return (value: string) => {
      return typeof value === 'string' ? value.toLowerCase() : value;
    };
  },

  /**
   * Convert to uppercase
   */
  toUpperCase: (): ValidationTransformer<string, string> => {
    return (value: string) => {
      return typeof value === 'string' ? value.toUpperCase() : value;
    };
  },

  /**
   * Capitalize first letter
   */
  capitalize: (): ValidationTransformer<string, string> => {
    return (value: string) => {
      if (typeof value !== 'string' || value.length === 0) return value;
      return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
    };
  },

  /**
   * Title case transformation
   */
  titleCase: (): ValidationTransformer<string, string> => {
    return (value: string) => {
      if (typeof value !== 'string') return value;
      return value.replace(/\w\S*/g, (txt) => 
        txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
      );
    };
  },

  /**
   * Remove non-numeric characters
   */
  numbersOnly: (): ValidationTransformer<string, string> => {
    return (value: string) => {
      return typeof value === 'string' ? value.replace(/[^0-9]/g, '') : value;
    };
  },

  /**
   * Remove non-alphanumeric characters
   */
  alphanumericOnly: (): ValidationTransformer<string, string> => {
    return (value: string) => {
      return typeof value === 'string' ? value.replace(/[^a-zA-Z0-9]/g, '') : value;
    };
  },

  /**
   * Format phone number
   */
  formatPhone: (format = 'international'): ValidationTransformer<string, string> => {
    return (value: string) => {
      if (typeof value !== 'string') return value;
      
      // Remove all non-numeric characters
      const numbers = value.replace(/[^0-9]/g, '');
      
      if (format === 'international') {
        // Format as +1 (*************
        if (numbers.length === 11 && numbers.startsWith('1')) {
          return `+1 (${numbers.slice(1, 4)}) ${numbers.slice(4, 7)}-${numbers.slice(7)}`;
        } else if (numbers.length === 10) {
          return `+1 (${numbers.slice(0, 3)}) ${numbers.slice(3, 6)}-${numbers.slice(6)}`;
        }
      } else if (format === 'national') {
        // Format as (*************
        if (numbers.length === 10) {
          return `(${numbers.slice(0, 3)}) ${numbers.slice(3, 6)}-${numbers.slice(6)}`;
        }
      }
      
      return value;
    };
  },

  /**
   * Format Vietnamese phone number
   */
  formatPhoneVN: (): ValidationTransformer<string, string> => {
    return (value: string) => {
      if (typeof value !== 'string') return value;
      
      // Remove all non-numeric characters
      const numbers = value.replace(/[^0-9]/g, '');
      
      // Format Vietnamese phone number
      if (numbers.length === 10 && numbers.startsWith('0')) {
        return `${numbers.slice(0, 4)} ${numbers.slice(4, 7)} ${numbers.slice(7)}`;
      } else if (numbers.length === 11 && numbers.startsWith('84')) {
        return `+84 ${numbers.slice(2, 5)} ${numbers.slice(5, 8)} ${numbers.slice(8)}`;
      }
      
      return value;
    };
  },

  /**
   * Convert string to number
   */
  toNumber: (): ValidationTransformer<string | number, number> => {
    return (value: string | number) => {
      if (typeof value === 'number') return value;
      if (typeof value === 'string') {
        const num = parseFloat(value);
        return isNaN(num) ? 0 : num;
      }
      return 0;
    };
  },

  /**
   * Convert string to integer
   */
  toInteger: (): ValidationTransformer<string | number, number> => {
    return (value: string | number) => {
      if (typeof value === 'number') return Math.floor(value);
      if (typeof value === 'string') {
        const num = parseInt(value, 10);
        return isNaN(num) ? 0 : num;
      }
      return 0;
    };
  },

  /**
   * Convert to boolean
   */
  toBoolean: (): ValidationTransformer<unknown, boolean> => {
    return (value: unknown) => {
      if (typeof value === 'boolean') return value;
      if (typeof value === 'string') {
        const lower = value.toLowerCase();
        return lower === 'true' || lower === '1' || lower === 'yes' || lower === 'on';
      }
      if (typeof value === 'number') return value !== 0;
      return Boolean(value);
    };
  },

  /**
   * Convert to date
   */
  toDate: (): ValidationTransformer<string | Date, Date | null> => {
    return (value: string | Date) => {
      if (value instanceof Date) return value;
      if (typeof value === 'string') {
        const date = new Date(value);
        return isNaN(date.getTime()) ? null : date;
      }
      return null;
    };
  },

  /**
   * Format currency
   */
  formatCurrency: (
    currency = 'USD',
    locale = 'en-US'
  ): ValidationTransformer<string | number, string> => {
    return (value: string | number) => {
      const num = typeof value === 'string' ? parseFloat(value) : value;
      if (isNaN(num)) return '0';
      
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency,
      }).format(num);
    };
  },

  /**
   * Format Vietnamese currency
   */
  formatCurrencyVN: (): ValidationTransformer<string | number, string> => {
    return (value: string | number) => {
      const num = typeof value === 'string' ? parseFloat(value) : value;
      if (isNaN(num)) return '0 ₫';
      
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
      }).format(num);
    };
  },

  /**
   * Remove HTML tags
   */
  stripHtml: (): ValidationTransformer<string, string> => {
    return (value: string) => {
      if (typeof value !== 'string') return value;
      return value.replace(/<[^>]*>/g, '');
    };
  },

  /**
   * Escape HTML entities
   */
  escapeHtml: (): ValidationTransformer<string, string> => {
    return (value: string) => {
      if (typeof value !== 'string') return value;
      
      const htmlEntities: Record<string, string> = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;',
      };
      
      return value.replace(/[&<>"']/g, (match) => htmlEntities[match]);
    };
  },

  /**
   * Normalize Vietnamese text (remove diacritics)
   */
  normalizeVietnamese: (): ValidationTransformer<string, string> => {
    return (value: string) => {
      if (typeof value !== 'string') return value;
      
      const vietnameseMap: Record<string, string> = {
        'à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ': 'a',
        'è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ': 'e',
        'ì|í|ị|ỉ|ĩ': 'i',
        'ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ': 'o',
        'ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ': 'u',
        'ỳ|ý|ỵ|ỷ|ỹ': 'y',
        'đ': 'd',
      };
      
      let result = value.toLowerCase();
      
      Object.keys(vietnameseMap).forEach(pattern => {
        const regex = new RegExp(pattern, 'g');
        result = result.replace(regex, vietnameseMap[pattern]);
      });
      
      return result;
    };
  },

  /**
   * Generate slug from string
   */
  toSlug: (): ValidationTransformer<string, string> => {
    return (value: string) => {
      if (typeof value !== 'string') return value;
      
      return value
        .toLowerCase()
        .trim()
        .replace(/[^\w\s-]/g, '') // Remove special characters
        .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
        .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
    };
  },

  /**
   * Mask sensitive data
   */
  mask: (
    visibleStart = 4,
    visibleEnd = 4,
    maskChar = '*'
  ): ValidationTransformer<string, string> => {
    return (value: string) => {
      if (typeof value !== 'string' || value.length <= visibleStart + visibleEnd) {
        return value;
      }
      
      const start = value.slice(0, visibleStart);
      const end = value.slice(-visibleEnd);
      const middle = maskChar.repeat(value.length - visibleStart - visibleEnd);
      
      return start + middle + end;
    };
  },

  /**
   * Truncate string
   */
  truncate: (
    maxLength: number,
    suffix = '...'
  ): ValidationTransformer<string, string> => {
    return (value: string) => {
      if (typeof value !== 'string' || value.length <= maxLength) {
        return value;
      }
      
      return value.slice(0, maxLength - suffix.length) + suffix;
    };
  },

  /**
   * Pad string
   */
  pad: (
    length: number,
    padString = ' ',
    padStart = false
  ): ValidationTransformer<string, string> => {
    return (value: string) => {
      if (typeof value !== 'string') return value;
      
      return padStart 
        ? value.padStart(length, padString)
        : value.padEnd(length, padString);
    };
  },

  /**
   * Replace patterns
   */
  replace: (
    pattern: string | RegExp,
    replacement: string
  ): ValidationTransformer<string, string> => {
    return (value: string) => {
      if (typeof value !== 'string') return value;
      return value.replace(pattern, replacement);
    };
  },

  /**
   * Chain multiple transformers
   */
  chain: <T>(...transformers: ValidationTransformer<T, T>[]): ValidationTransformer<T, T> => {
    return (value: T, context?: ValidationContext) => {
      return transformers.reduce((acc, transformer) => {
        return transformer(acc, context);
      }, value);
    };
  },

  /**
   * Conditional transformer
   */
  conditional: <T>(
    condition: (value: T, context?: ValidationContext) => boolean,
    trueTransformer: ValidationTransformer<T, T>,
    falseTransformer?: ValidationTransformer<T, T>
  ): ValidationTransformer<T, T> => {
    return (value: T, context?: ValidationContext) => {
      if (condition(value, context)) {
        return trueTransformer(value, context);
      } else if (falseTransformer) {
        return falseTransformer(value, context);
      }
      return value;
    };
  },

  /**
   * Safe transformer (catches errors)
   */
  safe: <T>(
    transformer: ValidationTransformer<T, T>,
    fallback?: T
  ): ValidationTransformer<T, T> => {
    return (value: T, context?: ValidationContext) => {
      try {
        return transformer(value, context);
      } catch (error) {
        console.warn('Transformer error:', error);
        return fallback !== undefined ? fallback : value;
      }
    };
  },
};

/**
 * Transformer utilities
 */
export const TransformerUtils = {
  /**
   * Create a custom transformer
   */
  create: <TInput, TOutput>(
    fn: (value: TInput, context?: ValidationContext) => TOutput
  ): ValidationTransformer<TInput, TOutput> => {
    return fn;
  },

  /**
   * Compose transformers
   */
  compose: <T>(...transformers: ValidationTransformer<T, T>[]): ValidationTransformer<T, T> => {
    return ValidationTransformers.chain(...transformers);
  },

  /**
   * Apply transformer with error handling
   */
  apply: <TInput, TOutput>(
    transformer: ValidationTransformer<TInput, TOutput>,
    value: TInput,
    context?: ValidationContext
  ): TOutput => {
    try {
      return transformer(value, context);
    } catch (error) {
      console.error('Transformer application failed:', error);
      throw error;
    }
  },

  /**
   * Test transformer with sample data
   */
  test: <TInput, TOutput>(
    transformer: ValidationTransformer<TInput, TOutput>,
    testCases: Array<{ input: TInput; expected: TOutput }>
  ): boolean => {
    return testCases.every(({ input, expected }) => {
      try {
        const result = transformer(input);
        return JSON.stringify(result) === JSON.stringify(expected);
      } catch (error) {
        console.error('Transformer test failed:', error);
        return false;
      }
    });
  },
};
