import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Mail,
  Edit,
  Send,
  FileText
} from 'lucide-react';
import { Card } from '@/shared/components/common';
import { Button } from '@/shared/components/common';
import { Input } from '@/shared/components/common';
import { Badge } from '@/shared/components/common';
import { Modal } from '@/shared/components/common';
import { Pagination } from '@/shared/components/common';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import { useEmailTemplates } from '../../hooks/email/useEmailTemplates';
import { CreateEmailTemplateForm } from '../../components/email/CreateEmailTemplateForm';
import type { EmailTemplateQueryDto, EmailTemplateDto } from '../../types/email.types';

/**
 * Trang quản lý Email Templates
 */
export function EmailTemplatesPage() {
  const { t } = useTranslation('marketing');
  const [searchParams, setSearchParams] = useSearchParams();

  const [query, setQuery] = useState<EmailTemplateQueryDto>({
    page: 1,
    limit: 20,
    search: '',
  });
  const [showCreateModal, setShowCreateModal] = useState(
    searchParams.get('action') === 'create'
  );
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [selectedTemplate] = useState<EmailTemplateDto | null>(null);
  const { data: templatesData, isLoading } = useEmailTemplates(query);

  const handleSearch = (value: string) => {
    setQuery(prev => ({ ...prev, search: value, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setQuery(prev => ({ ...prev, page }));
  };

  const handleCreateSuccess = () => {
    setShowCreateModal(false);
    setSearchParams({});
  };

  // Removed unused handler functions - will be implemented when needed

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <Badge variant="success">Hoạt động</Badge>;
      case 'DRAFT':
        return <Badge variant="warning">Bản nháp</Badge>;
      case 'ARCHIVED':
        return <Badge variant="info">Đã lưu trữ</Badge>;
      default:
        return <Badge variant="primary">{status}</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    const typeColors: Record<string, string> = {
      NEWSLETTER: 'bg-blue-100 text-blue-800',
      PROMOTIONAL: 'bg-orange-100 text-orange-800',
      TRANSACTIONAL: 'bg-green-100 text-green-800',
      WELCOME: 'bg-purple-100 text-purple-800',
      ABANDONED_CART: 'bg-red-100 text-red-800',
      FOLLOW_UP: 'bg-gray-100 text-gray-800',
    };

    return (
      <Badge variant="info" className={typeColors[type] || 'bg-gray-100 text-gray-800'}>
        {type}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <MarketingViewHeader
        title={t('email.templates.title', 'Email Templates')}
        description={t('email.templates.description', 'Tạo và quản lý các mẫu email marketing')}
        actions={
          <Button onClick={() => setShowCreateModal(true)} className="gap-2">
            <Plus className="h-4 w-4" />
            {t('email.templates.createTemplate', 'Tạo Template')}
          </Button>
        }
      />

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Tổng Templates</span>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">
            {templatesData?.meta.totalItems || 0}
          </div>
          <p className="text-xs text-muted-foreground">+3 template mới</p>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Hoạt động</span>
              <Mail className="h-4 w-4 text-green-600" />
            </div>
          }
        >
          <div className="text-2xl font-bold text-green-600">
            {templatesData?.items.filter((t: EmailTemplateDto) => t.status === 'ACTIVE').length || 0}
          </div>
          <p className="text-xs text-muted-foreground">Sẵn sàng sử dụng</p>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Bản nháp</span>
              <Edit className="h-4 w-4 text-yellow-600" />
            </div>
          }
        >
          <div className="text-2xl font-bold text-yellow-600">
            {templatesData?.items.filter((t: EmailTemplateDto) => t.status === 'DRAFT').length || 0}
          </div>
          <p className="text-xs text-muted-foreground">Chưa hoàn thành</p>
        </Card>

        <Card
          title={
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Đã gửi test</span>
              <Send className="h-4 w-4 text-muted-foreground" />
            </div>
          }
        >
          <div className="text-2xl font-bold">24</div>
          <p className="text-xs text-muted-foreground">Tuần này</p>
        </Card>
      </div>

      {/* Filters */}
      <Card title="Bộ lọc">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('email.templates.searchPlaceholder', 'Tìm kiếm template...')}
                value={query.search || ''}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Bộ lọc nâng cao
          </Button>
        </div>
      </Card>

      {/* Templates Table */}
      <Card
        title="Danh sách Templates"
        subtitle={
          templatesData?.meta.totalItems
            ? `Tổng cộng ${templatesData.meta.totalItems} templates`
            : 'Chưa có template nào'
        }
      >
        <div className="rounded-md border">
          <div className="overflow-x-auto">
            {/* Table Header */}
            <div className="grid grid-cols-7 gap-4 p-4 bg-muted/50 border-b font-medium text-sm">
              <div>Template</div>
              <div>Loại</div>
              <div>Trạng thái</div>
              <div>Tags</div>
              <div>Biến</div>
              <div>Cập nhật</div>
              <div>Thao tác</div>
            </div>

            {/* Table Body */}
            <div>
              {isLoading ? (
                [...Array(5)].map((_, i) => (
                  <div key={i} className="grid grid-cols-7 gap-4 p-4 border-b">
                    <div className="flex items-center space-x-3">
                      <div className="h-12 w-12 rounded bg-muted animate-pulse" />
                      <div className="space-y-2">
                        <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                        <div className="h-3 w-24 bg-muted animate-pulse rounded" />
                      </div>
                    </div>
                    <div className="h-6 w-20 bg-muted animate-pulse rounded" />
                    <div className="h-6 w-20 bg-muted animate-pulse rounded" />
                    <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                    <div className="h-4 w-12 bg-muted animate-pulse rounded" />
                    <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                    <div className="h-8 w-8 bg-muted animate-pulse rounded" />
                  </div>
                ))
              ) : templatesData?.items.length === 0 ? (
                <div className="text-center py-8">
                  <div className="flex flex-col items-center space-y-2">
                    <Mail className="h-8 w-8 text-muted-foreground" />
                    <p className="text-muted-foreground">Chưa có template nào</p>
                    <Button onClick={() => setShowCreateModal(true)} size="sm">
                      Tạo template đầu tiên
                    </Button>
                  </div>
                </div>
              ) : (
                templatesData?.items.map((template: EmailTemplateDto) => (
                  <div key={template.id} className="grid grid-cols-7 gap-4 p-4 border-b hover:bg-muted/50">
                    <div className="flex items-center space-x-3">
                      <div className="h-12 w-12 rounded bg-gradient-to-r from-orange-500 to-red-600 flex items-center justify-center text-white">
                        <Mail className="h-6 w-6" />
                      </div>
                      <div>
                        <div className="font-medium">{template.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {template.subject}
                        </div>
                      </div>
                    </div>
                    <div>
                      {getTypeBadge(template.type)}
                    </div>
                    <div>
                      {getStatusBadge(template.status)}
                    </div>
                    <div>
                      <div className="flex flex-wrap gap-1">
                        {template.tags.slice(0, 2).map((tag: string) => (
                          <Badge key={tag} variant="info" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {template.tags.length > 2 && (
                          <Badge variant="info" className="text-xs">
                            +{template.tags.length - 2}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm">
                        {template.variables.length} biến
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(template.updatedAt).toLocaleDateString('vi-VN')}
                    </div>
                    <div>
                      <div className="relative">
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                        {/* TODO: Implement proper dropdown menu */}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Pagination */}
        {templatesData && templatesData.meta.totalPages > 1 && (
          <div className="mt-4">
            <Pagination
              currentPage={templatesData.meta.currentPage}
              totalPages={templatesData.meta.totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </Card>

      {/* Create Template Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="Tạo Email Template"
        size="lg"
      >
        <CreateEmailTemplateForm onSuccess={handleCreateSuccess} />
      </Modal>

      {/* Preview Template Modal */}
      <Modal
        isOpen={showPreviewModal}
        onClose={() => setShowPreviewModal(false)}
        title="Xem trước Template"
        size="lg"
      >
        {selectedTemplate && (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">{selectedTemplate.name}</h4>
              <p className="text-sm text-muted-foreground">
                Subject: {selectedTemplate.subject}
              </p>
            </div>

            <div className="border rounded-lg p-4 bg-white">
              <div
                dangerouslySetInnerHTML={{
                  __html: selectedTemplate.htmlContent || '<p>Không có nội dung preview</p>'
                }}
              />
            </div>

            <div className="space-y-2">
              <h5 className="text-sm font-medium">Biến template:</h5>
              <div className="space-y-1">
                {selectedTemplate.variables.map((variable, index: number) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="font-mono">{variable.name}</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="info" className="text-xs">
                        {variable.type}
                      </Badge>
                      {variable.required && (
                        <Badge variant="danger" className="text-xs">
                          Bắt buộc
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}

export default EmailTemplatesPage;
