import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { ZaloMessage } from '../entities/zalo-message.entity';

/**
 * Repository cho ZaloMessage
 */
@Injectable()
export class ZaloMessageRepository {
  constructor(
    @InjectRepository(ZaloMessage)
    private readonly repository: Repository<ZaloMessage>,
  ) {}

  /**
   * Tìm kiếm nhiều tin nhắn
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách tin nhắn
   */
  async find(options?: FindManyOptions<ZaloMessage>): Promise<ZaloMessage[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một tin nhắn
   * @param options Tùy chọn tìm kiếm
   * @returns Tin nhắn hoặc null
   */
  async findOne(options?: FindOneOptions<ZaloMessage>): Promise<ZaloMessage | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Tìm tin nhắn theo ID
   * @param id ID của tin nhắn
   * @returns Tin nhắn hoặc null
   */
  async findById(id: number): Promise<ZaloMessage | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm tin nhắn theo ID tin nhắn trên Zalo
   * @param messageId ID của tin nhắn trên Zalo
   * @returns Tin nhắn hoặc null
   */
  async findByMessageId(messageId: string): Promise<ZaloMessage | null> {
    return this.repository.findOne({ where: { messageId } });
  }

  /**
   * Tìm tất cả tin nhắn của một Official Account
   * @param oaId ID của Official Account
   * @param options Tùy chọn tìm kiếm bổ sung
   * @returns Danh sách tin nhắn
   */
  async findByOaId(oaId: string, options?: FindManyOptions<ZaloMessage>): Promise<ZaloMessage[]> {
    const findOptions: FindManyOptions<ZaloMessage> = {
      where: { oaId },
      ...options,
    };
    return this.repository.find(findOptions);
  }

  /**
   * Tìm tất cả tin nhắn của một người dùng Zalo
   * @param userId ID của người dùng Zalo
   * @param options Tùy chọn tìm kiếm bổ sung
   * @returns Danh sách tin nhắn
   */
  async findByUserId(userId: string, options?: FindManyOptions<ZaloMessage>): Promise<ZaloMessage[]> {
    const findOptions: FindManyOptions<ZaloMessage> = {
      where: { userId },
      ...options,
    };
    return this.repository.find(findOptions);
  }

  /**
   * Tìm tất cả tin nhắn giữa một Official Account và một người dùng Zalo
   * @param oaId ID của Official Account
   * @param userId ID của người dùng Zalo
   * @param options Tùy chọn tìm kiếm bổ sung
   * @returns Danh sách tin nhắn
   */
  async findByOaIdAndUserId(
    oaId: string,
    userId: string,
    options?: FindManyOptions<ZaloMessage>,
  ): Promise<ZaloMessage[]> {
    const findOptions: FindManyOptions<ZaloMessage> = {
      where: { oaId, userId },
      ...options,
    };
    return this.repository.find(findOptions);
  }

  /**
   * Tạo mới tin nhắn
   * @param data Dữ liệu tin nhắn
   * @returns Tin nhắn đã tạo
   */
  async create(data: Partial<ZaloMessage>): Promise<ZaloMessage> {
    const message = this.repository.create(data);
    return this.repository.save(message);
  }

  /**
   * Tạo nhiều tin nhắn
   * @param dataArray Mảng dữ liệu tin nhắn
   * @returns Danh sách tin nhắn đã tạo
   */
  async createMany(dataArray: Partial<ZaloMessage>[]): Promise<ZaloMessage[]> {
    const messages = this.repository.create(dataArray);
    return this.repository.save(messages);
  }

  /**
   * Cập nhật tin nhắn
   * @param id ID của tin nhắn
   * @param data Dữ liệu cập nhật
   * @returns Tin nhắn đã cập nhật
   */
  async update(id: number, data: Partial<ZaloMessage>): Promise<ZaloMessage | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa tin nhắn
   * @param id ID của tin nhắn
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Đếm số lượng tin nhắn
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng tin nhắn
   */
  async count(options?: FindManyOptions<ZaloMessage>): Promise<number> {
    return this.repository.count(options);
  }
}
