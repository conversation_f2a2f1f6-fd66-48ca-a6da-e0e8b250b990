/**
 * Query keys cho module ai-agents
 * Sử dụng cho TanStack Query
 */

export const AGENT_QUERY_KEYS = {
  /**
   * Key cho danh sách agents
   */
  AGENT_LIST: 'agent-list',

  /**
   * Key cho chi tiết agent
   */
  AGENT_DETAIL: 'agent-detail',

  /**
   * Key cho thống kê agent
   */
  AGENT_STATISTICS: 'agent-statistics',

  /**
   * Key cho tạo agent mới
   */
  CREATE_AGENT: 'create-agent',

  /**
   * Key cho cập nhật agent
   */
  UPDATE_AGENT: 'update-agent',

  /**
   * Key cho xóa agent
   */
  DELETE_AGENT: 'delete-agent',

  /**
   * Key cho bật/tắt agent
   */
  TOGGLE_AGENT_ACTIVE: 'toggle-agent-active',

  /**
   * Key cho cập nhật vector store
   */
  UPDATE_AGENT_VECTOR_STORE: 'update-agent-vector-store',

  /**
   * Key cho danh sách type agents
   */
  TYPE_AGENT_LIST: 'type-agent-list',

  /**
   * Key cho chi tiết type agent
   */
  TYPE_AGENT_DETAIL: 'type-agent-detail',

  /**
   * Key cho tạo type agent mới
   */
  CREATE_TYPE_AGENT: 'create-type-agent',

  /**
   * Key cho cập nhật type agent
   */
  UPDATE_TYPE_AGENT: 'update-type-agent',

  /**
   * Key cho xóa type agent
   */
  DELETE_TYPE_AGENT: 'delete-type-agent',

  /**
   * Key cho danh sách agent resources
   */
  AGENT_RESOURCES: 'agent-resources',

  /**
   * Key cho agent strategies
   */
  AGENT_STRATEGIES: 'agent-strategies',

  /**
   * Key cho agent integrations
   */
  AGENT_INTEGRATIONS: 'agent-integrations',

  /**
   * Key cho danh sách base models
   */
  BASE_MODEL_LIST: 'base-model-list',
} as const;
