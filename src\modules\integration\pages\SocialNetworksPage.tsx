import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, ResponsiveGrid } from '@/shared/components/common';
import { SocialNetworkIcon } from '../components/Social';
import { SocialNetwork } from '../types/social';
import { useNavigate } from 'react-router-dom';

/**
 * Page displaying social networks integration options
 */
const SocialNetworksPage: React.FC = () => {
  const { t } = useTranslation();
  // Example social networks
  const socialNetworks: SocialNetwork[] = [
    {
      id: 'facebook',
      name: 'Facebook',
      code: 'facebook',
      url: '/integrations/facebook',
      color: '#1877F2',
      active: true,
    },
    // {
    //   id: 'instagram',
    //   name: 'Instagram',
    //   code: 'instagram',
    //   url: '/integrations/instagram',
    //   color: '#E4405F',
    //   active: true,
    // },
    // {
    //   id: 'linkedin',
    //   name: 'LinkedIn',
    //   code: 'linkedin',
    //   url: '/integrations/linkedin',
    //   color: '#0A66C2',
    //   active: true,
    // },
    // {
    //   id: 'twitter',
    //   name: 'Twitter',
    //   code: 'twitter',
    //   url: '/integrations/twitter',
    //   color: '#1DA1F2',
    //   active: true,
    // },
    // {
    //   id: 'tiktok',
    //   name: 'TikTok',
    //   code: 'tiktok',
    //   url: '/integrations/tiktok',
    //   color: '#000000',
    //   active: true,
    // },
    // {
    //   id: 'telegram',
    //   name: 'Telegram',
    //   code: 'telegram',
    //   url: '/integrations/telegram',
    //   color: '#0088cc',
    //   active: true,
    // },
    {
      id: 'website',
      name: 'Website',
      code: 'website',
      url: '/integrations/website',
      color: '#4285F4',
      active: true,
    },
  ];

  const navigate = useNavigate();

  // Handle click on social network icon
  const handleSocialNetworkClick = (network: SocialNetwork) => {
    navigate(network.url);
  };

  return (
    <div>
      <Typography variant="h4" className="mb-4">
        {t('integration.social.title')}
      </Typography>

      <Typography variant="body1" color="muted" className="mb-6">
        {t('integration.social.description')}
      </Typography>

      <ResponsiveGrid
        maxColumnsWithChatPanel={{
          xs: 2,
          sm: 3,
          md: 3,
          lg: 3,
          xl: 3,
        }}
      >
        {socialNetworks.map(network => (
          <SocialNetworkIcon
            key={network.id}
            network={network}
            onClick={handleSocialNetworkClick}
          />
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default SocialNetworksPage;
