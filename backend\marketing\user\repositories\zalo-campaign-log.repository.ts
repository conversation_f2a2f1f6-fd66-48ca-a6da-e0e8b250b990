import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ZaloCampaignLog } from '../entities';

/**
 * Repository cho log chiến dịch Zalo
 */
@Injectable()
export class ZaloCampaignLogRepository {
  constructor(
    @InjectRepository(ZaloCampaignLog)
    private readonly repository: Repository<ZaloCampaignLog>,
  ) {}

  /**
   * Tìm log chiến dịch theo ID
   * @param id ID của log chiến dịch
   * @returns Log chiến dịch
   */
  async findById(id: number): Promise<ZaloCampaignLog | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm danh sách log chiến dịch theo ID chiến dịch
   * @param campaignId ID của chiến dịch
   * @returns Danh sách log chiến dịch
   */
  async findByCampaignId(campaignId: number): Promise<ZaloCampaignLog[]> {
    return this.repository.find({ where: { campaignId } });
  }

  /**
   * Tìm danh sách log chiến dịch theo ID chiến dịch và trạng thái
   * @param campaignId ID của chiến dịch
   * @param status Trạng thái
   * @returns Danh sách log chiến dịch
   */
  async findByCampaignIdAndStatus(campaignId: number, status: string): Promise<ZaloCampaignLog[]> {
    return this.repository.find({ where: { campaignId, status } });
  }

  /**
   * Đếm số lượng log chiến dịch theo ID chiến dịch và trạng thái
   * @param campaignId ID của chiến dịch
   * @param status Trạng thái
   * @returns Số lượng log chiến dịch
   */
  async countByCampaignIdAndStatus(campaignId: number, status: string): Promise<number> {
    return this.repository.count({ where: { campaignId, status } });
  }

  /**
   * Tìm danh sách log chiến dịch với phân trang
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách log chiến dịch và tổng số log chiến dịch
   */
  async findWithPagination(options: any): Promise<[ZaloCampaignLog[], number]> {
    const { where, skip, take, order } = options;
    return this.repository.findAndCount({
      where,
      skip,
      take,
      order,
    });
  }

  /**
   * Tạo log chiến dịch mới
   * @param data Dữ liệu log chiến dịch
   * @returns Log chiến dịch đã tạo
   */
  async create(data: Partial<ZaloCampaignLog>): Promise<ZaloCampaignLog> {
    const log = this.repository.create(data);
    return this.repository.save(log);
  }

  /**
   * Tạo nhiều log chiến dịch mới
   * @param dataList Danh sách dữ liệu log chiến dịch
   * @returns Danh sách log chiến dịch đã tạo
   */
  async createMany(dataList: Partial<ZaloCampaignLog>[]): Promise<ZaloCampaignLog[]> {
    const logs = dataList.map(data => this.repository.create(data));
    return this.repository.save(logs);
  }

  /**
   * Cập nhật log chiến dịch
   * @param id ID của log chiến dịch
   * @param data Dữ liệu cập nhật
   * @returns Log chiến dịch đã cập nhật
   */
  async update(id: number, data: Partial<ZaloCampaignLog>): Promise<ZaloCampaignLog | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }
}
