{"integration": {"title": "Integration Management", "description": "Manage system integrations and connection configurations", "email": {"title": "Email Server Management", "description": "Manage email server configurations for automated email sending", "list": {"title": "Email Server List", "empty": "No email server configurations yet", "search": "Search by name or host...", "columns": {"serverName": "Server Name", "host": "Host", "port": "Port", "username": "Username", "ssl": "SSL", "status": "Status", "actions": "Actions"}}, "form": {"create": "Create Email Server", "edit": "Edit Email Server", "test": "Test Connection", "fields": {"serverName": "Server Name", "host": "Host", "port": "Port", "username": "Username", "password": "Password", "useSsl": "Use SSL", "useStartTls": "Use StartTLS", "additionalSettings": "Additional Settings (JSON)", "isActive": "Active", "recipientEmail": "Test Recipient Email", "subject": "Email Subject"}, "placeholders": {"serverName": "Enter server name...", "host": "smtp.gmail.com", "port": "587", "username": "<EMAIL>", "password": "Enter password...", "additionalSettings": "{}", "recipientEmail": "<EMAIL>", "subject": "Test Email"}}, "actions": {"create": "Create", "edit": "Edit", "delete": "Delete", "test": "Test", "save": "Save", "cancel": "Cancel", "close": "Close"}, "validation": {"serverName": {"required": "Server name is required", "maxLength": "Server name must not exceed 100 characters"}, "host": {"required": "Host is required", "maxLength": "Host must not exceed 255 characters"}, "port": {"min": "Port must be greater than 0", "max": "Port must be less than 65536"}, "username": {"required": "Username is required", "email": "Username must be a valid email"}, "password": {"required": "Password is required", "minLength": "Password must be at least 6 characters"}, "additionalSettings": {"invalidJson": "Additional settings must be valid JSON"}, "recipientEmail": {"email": "Recipient email must be a valid email"}, "subject": {"maxLength": "Subject must not exceed 200 characters"}}, "notifications": {"createSuccess": "Email server created successfully", "createError": "Error creating email server", "updateSuccess": "Email server updated successfully", "updateError": "Error updating email server", "deleteSuccess": "Email server deleted successfully", "deleteError": "Error deleting email server", "testSuccess": "Connection test successful", "testError": "Error testing connection"}, "confirmations": {"delete": "Are you sure you want to delete this email server?", "deleteTitle": "Confirm Delete"}}, "providerModel": {"title": "Provider Model Management", "description": "Manage AI providers and API key configurations", "list": {"title": "Provider Model List", "empty": "No provider models yet", "search": "Search by provider name...", "columns": {"name": "Provider Name", "type": "Type", "createdAt": "Created Date", "actions": "Actions"}}, "form": {"create": "Create Provider Model", "edit": "Edit Provider Model", "view": "View Provider Model", "fields": {"name": "Provider Name", "type": "Provider Type", "apiKey": "API Key"}, "placeholders": {"name": "Enter provider name...", "apiKey": "Enter API key..."}}, "actions": {"create": "Create", "edit": "Edit", "view": "View Details", "delete": "Delete", "save": "Save", "cancel": "Cancel"}, "validation": {"name": {"required": "Provider name is required", "maxLength": "Provider name cannot exceed 255 characters"}, "type": {"required": "Provider type is required"}, "apiKey": {"required": "API key is required", "minLength": "API key must be at least 10 characters"}}, "notifications": {"createSuccess": "Provider model created successfully", "createError": "Error creating provider model", "updateSuccess": "Provider model updated successfully", "updateError": "Error updating provider model", "deleteSuccess": "Provider model deleted successfully", "deleteError": "Error deleting provider model"}, "confirmations": {"delete": "Are you sure you want to delete this provider model?", "deleteTitle": "Confirm Delete"}, "providers": {"OPENAI": "OpenAI", "ANTHROPIC": "Anthropic", "GOOGLE": "Google", "META": "Meta", "DEEPSEEK": "DeepSeek", "XAI": "XAI"}}}}