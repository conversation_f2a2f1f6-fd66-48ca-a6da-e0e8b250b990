import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, FormItem, Input, Button, Typography, Textarea, TagsInput, Select } from '@/shared/components/common';
import { z } from 'zod';
import { FormRef } from '@/shared/components/common/Form/Form';
import { UrlDto } from '@/modules/data/url/types/url.types';

// Schema cho form tạo URL mới
const createUrlSchema = z.object({
  url: z.string().url('URL không hợp lệ').min(1, 'URL là bắt buộc'),
  title: z.string().min(1, 'Tiêu đề là bắt buộc'),
  description: z.string().optional(),
  type: z.string().optional(),
  tags: z.union([z.string(), z.array(z.string())]).optional(),
});

export type CreateUrlFormValues = z.infer<typeof createUrlSchema>;

export interface UrlFormProps {
  initialValues?: Partial<UrlDto>;
  onSubmit: (values: CreateUrlFormValues) => void;
  onCancel: () => void;
  readOnly?: boolean;
  isSubmitting?: boolean;
}

/**
 * Form component for creating or editing URLs
 */
const UrlForm: React.FC<UrlFormProps> = ({
  initialValues,
  onSubmit,
  onCancel,
  readOnly = false,
  isSubmitting = false,
}) => {
  const { t } = useTranslation();

  // Form ref
  const formRef = useRef<FormRef<CreateUrlFormValues>>(null);

  return (
    <div className="p-6">
      <Typography variant="h6" className="mb-4">
        {readOnly
          ? t('data.url.viewUrl', 'Xem URL')
          : initialValues?.id
            ? t('data.url.editUrl', 'Chỉnh sửa URL')
            : t('data.url.addNew', 'Thêm URL mới')}
      </Typography>

      <Form
        // @ts-expect-error - Form component has type compatibility issues with generic FormRef
        ref={formRef}
        defaultValues={initialValues}
        schema={createUrlSchema}
        // @ts-expect-error - Form component has type compatibility issues with onSubmit
        onSubmit={onSubmit}
        className="space-y-4"
      >
        <FormItem name="url" label={t('data.url.form.url', 'URL')} required>
          <Input fullWidth readOnly={readOnly} placeholder="https://example.com" />
        </FormItem>
        {!readOnly && (
          <div className="text-xs text-gray-500 -mt-3 mb-3">
            {t('data.url.form.urlDescription', 'Nhập URL đầy đủ bao gồm http:// hoặc https://')}
          </div>
        )}

        <FormItem name="title" label={t('data.url.form.title', 'Tiêu đề')} required>
          <Input
            fullWidth
            readOnly={readOnly}
            placeholder={t('data.url.form.titlePlaceholder', 'Nhập tiêu đề')}
          />
        </FormItem>

        <FormItem name="description" label={t('data.url.form.description', 'Mô tả')}>
          <Textarea
            fullWidth
            readOnly={readOnly}
            rows={3}
            placeholder={t('data.url.form.descriptionPlaceholder', 'Nhập mô tả (không bắt buộc)')}
          />
        </FormItem>

        <FormItem name="type" label={t('data.url.form.type', 'Loại')}>
          <Select
            fullWidth
            disabled={readOnly}
            placeholder={t('data.url.form.typePlaceholder', 'Chọn loại URL')}
            options={[
              { value: 'web', label: 'Web' },
              { value: 'video', label: 'Video' },
              { value: 'image', label: 'Image' },
              { value: 'audio', label: 'Audio' },
              { value: 'document', label: 'Document' },
              { value: 'other', label: 'Other' },
            ]}
          />
        </FormItem>

        <FormItem name="tags" label={t('data.url.form.tags', 'Tags')}>
          <TagsInput
            fieldName="tags"
            formRef={formRef}
            placeholder={t('data.url.form.tagsPlaceholder', 'Nhập tag và nhấn Enter')}
            initialValue={Array.isArray(initialValues?.tags) ? initialValues?.tags.join(', ') : initialValues?.tags || ''}
            readOnly={readOnly}
          />
        </FormItem>
        {!readOnly && (
          <div className="text-xs text-gray-500 -mt-3 mb-3">
            {t(
              'data.url.form.tagsDescription',
              'Các tags giúp phân loại và tìm kiếm URL dễ dàng hơn'
            )}
          </div>
        )}

        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={onCancel} type="button" disabled={isSubmitting}>
            {readOnly ? t('common.close', 'Đóng') : t('common.cancel', 'Hủy')}
          </Button>
          {!readOnly && (
            <Button
              variant="primary"
              type="submit"
              disabled={isSubmitting}
              isLoading={isSubmitting}
            >
              {t('common.save', 'Lưu')}
            </Button>
          )}
        </div>
      </Form>
    </div>
  );
};

export default UrlForm;
