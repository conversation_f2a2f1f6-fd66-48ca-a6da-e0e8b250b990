import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, FormItem, Input, Button, Typography, Checkbox, Card } from '@/shared/components/common';
import { z } from 'zod';
import { FormRef } from '@/shared/components/common/Form/Form';
import { Controller } from 'react-hook-form';

/**
 * Schema cho form crawl URL
 */
const crawlUrlSchema = z.object({
  url: z.string()
    .min(1, 'URL không được để trống')
    .url('URL không hợp lệ'),
  depth: z.preprocess(
    (val) => (typeof val === 'string' ? parseInt(val, 10) : val),
    z.number()
      .min(1, 'Độ sâu tối thiểu là 1')
      .max(3, 'Độ sâu tối đa là 3')
  ),
  maxUrls: z.preprocess(
    (val) => (val === '' ? undefined : typeof val === 'string' ? parseInt(val, 10) : val),
    z.number()
      .min(1, 'S<PERSON> lượng URL tối thiểu là 1')
      .max(100, 'Số lượng URL tối đa là 100')
      .optional()
  ),
  ignoreRobotsTxt: z.boolean().default(false),
});

export type CrawlUrlFormValues = z.infer<typeof crawlUrlSchema>;

interface CrawlUrlFormProps {
  onSubmit: (values: CrawlUrlFormValues) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

/**
 * Form component for crawling URLs
 */
const CrawlUrlForm: React.FC<CrawlUrlFormProps> = ({ onSubmit, onCancel, isLoading = false }) => {
  const { t } = useTranslation();

  // Form ref
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const formRef = useRef<FormRef<any>>(null);

  // Handle form submission
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleSubmit = (values: any) => {
    onSubmit(values);
  };

  return (
    <Card>
      <Typography variant="h6" className="mb-4">
        {t('data:url.crawl', 'Crawl URL')}
      </Typography>

      <Form
        ref={formRef}
        schema={crawlUrlSchema}
        onSubmit={handleSubmit}
        className="space-y-4"
        defaultValues={{
          depth: 1,
          maxUrls: 10,
          ignoreRobotsTxt: false,
        }}
      >
        <FormItem name="url" label={t('data:url.form.url', 'URL')} required>
          <Input
            fullWidth
            placeholder="https://example.com"
          />
        </FormItem>
        <div className="text-xs text-gray-500 -mt-3 mb-3">
          {t('data:url.form.crawlUrlDescription', 'URL gốc để bắt đầu crawl')}
        </div>

        <FormItem name="depth" label={t('data:url.form.depth', 'Độ sâu')} required>
          <Input
            type="number"
            min={1}
            max={3}
          />
        </FormItem>
        <div className="text-xs text-gray-500 -mt-3 mb-3">
          {t('data:url.form.depthDescription', 'Số cấp độ URL con sẽ được crawl (1-3)')}
        </div>

        <FormItem name="maxUrls" label={t('data:url.form.maxUrls', 'Số lượng URL tối đa')}>
          <Input
            type="number"
            min={1}
            max={100}
          />
        </FormItem>
        <div className="text-xs text-gray-500 -mt-3 mb-3">
          {t(
            'data:url.form.maxUrlsDescription',
            'Giới hạn số lượng URL sẽ được crawl (tối đa 100)'
          )}
        </div>

        <FormItem name="ignoreRobotsTxt" label={t('data:url.form.ignoreRobotsTxt', 'Bỏ qua robots.txt')}>
          <Controller
            name="ignoreRobotsTxt"
            render={({ field }) => (
              <div className="flex items-center">
                <Checkbox
                  checked={field.value}
                  onChange={(checked) => {
                    field.onChange(checked);
                  }}
                  color="danger"
                />
              </div>
            )}
          />
        </FormItem>
        <div className="text-xs text-gray-500 -mt-3 mb-3">
          {t(
            'data:url.form.ignoreRobotsTxtDescription',
            'Bỏ qua các quy tắc trong file robots.txt'
          )}
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button variant="primary" type="submit" disabled={isLoading} isLoading={isLoading}>
            {t('data:url.startCrawl', 'Bắt đầu crawl')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default CrawlUrlForm;
