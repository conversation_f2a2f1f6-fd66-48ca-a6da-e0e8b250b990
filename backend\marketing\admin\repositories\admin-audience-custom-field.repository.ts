import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeleteResult, FindManyOptions, FindOneOptions, FindOptionsWhere, Repository } from 'typeorm';
import { AdminAudienceCustomField } from '@modules/marketing/admin/entities';

/**
 * Repository cho AdminAudienceCustomField
 */
@Injectable()
export class AdminAudienceCustomFieldRepository {
  constructor(
    @InjectRepository(AdminAudienceCustomField)
    private readonly repository: Repository<AdminAudienceCustomField>,
  ) {}

  /**
   * Tìm kiếm nhiều trường tùy chỉnh
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách trường tùy chỉnh
   */
  async find(options?: FindManyOptions<AdminAudienceCustomField>): Promise<AdminAudienceCustomField[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một trường tùy chỉnh
   * @param options Tùy chọn tìm kiếm
   * @returns Trường tùy chỉnh hoặc null
   */
  async findOne(options?: FindOneOptions<AdminAudienceCustomField>): Promise<AdminAudienceCustomField | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Lưu trường tùy chỉnh
   * @param customField Trường tùy chỉnh cần lưu
   * @returns Trường tùy chỉnh đã lưu
   */
  async save(customField: AdminAudienceCustomField): Promise<AdminAudienceCustomField>;
  async save(customField: AdminAudienceCustomField[]): Promise<AdminAudienceCustomField[]>;
  async save(customField: AdminAudienceCustomField | AdminAudienceCustomField[]): Promise<AdminAudienceCustomField | AdminAudienceCustomField[]> {
    return this.repository.save(customField as any);
  }

  /**
   * Xóa trường tùy chỉnh
   * @param criteria Điều kiện xóa
   * @returns Kết quả xóa
   */
  async delete(criteria: string | number | string[] | number[] | FindOptionsWhere<AdminAudienceCustomField>): Promise<DeleteResult> {
    return this.repository.delete(criteria);
  }

  /**
   * Xóa trường tùy chỉnh
   * @param customField Trường tùy chỉnh cần xóa
   * @returns Trường tùy chỉnh đã xóa
   */
  async remove(customField: AdminAudienceCustomField): Promise<AdminAudienceCustomField>;
  async remove(customField: AdminAudienceCustomField[]): Promise<AdminAudienceCustomField[]>;
  async remove(customField: AdminAudienceCustomField | AdminAudienceCustomField[]): Promise<AdminAudienceCustomField | AdminAudienceCustomField[]> {
    return this.repository.remove(customField as any);
  }
}
