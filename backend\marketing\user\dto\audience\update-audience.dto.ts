import { IsEmail, IsOptional, IsPhoneNumber, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CreateCustomFieldDto } from './create-custom-field.dto';

/**
 * DTO cho việc cập nhật audience
 */
export class UpdateAudienceDto {
  /**
   * Email của khách hàng
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Email của khách hàng',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email?: string;

  /**
   * Số điện thoại của khách hàng
   * @example "+84912345678"
   */
  @ApiProperty({
    description: 'Số điện thoại của khách hàng',
    example: '+84912345678',
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber(undefined, { message: '<PERSON><PERSON> điện thoại không hợp lệ' })
  phone?: string;

  /**
   * Danh sách các trường tùy chỉnh
   */
  @ApiProperty({
    description: 'Danh sách các trường tùy chỉnh',
    type: [CreateCustomFieldDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateCustomFieldDto)
  customFields?: CreateCustomFieldDto[];

  /**
   * Danh sách ID của các tag
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID của các tag',
    example: [1, 2, 3],
    required: false,
    type: [Number],
  })
  @IsOptional()
  tagIds?: number[];
}
