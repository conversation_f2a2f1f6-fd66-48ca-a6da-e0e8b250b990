import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  ProviderModel,
  ProviderModelListItem,
  CreateProviderModelDto,
  UpdateProviderModelDto,
  ProviderModelQueryParams,
} from '../types';

/**
 * Provider Model Service
 */
export class ProviderModelService {
  private static readonly BASE_URL = '/admin/provider-model';

  /**
   * Lấy danh sách provider models
   */
  static async getProviderModels(
    params?: ProviderModelQueryParams
  ): Promise<ApiResponseDto<PaginatedResult<ProviderModelListItem>>> {
    return apiClient.get(this.BASE_URL, { params });
  }

  /**
   * Lấy thông tin chi tiết provider model
   */
  static async getProviderModel(id: string): Promise<ApiResponseDto<ProviderModel>> {
    return apiClient.get(`${this.BASE_URL}/${id}`);
  }

  /**
   * Tạo mới provider model
   */
  static async createProviderModel(
    data: CreateProviderModelDto
  ): Promise<ApiResponseDto<{ message: string }>> {
    return apiClient.post(this.BASE_URL, data);
  }

  /**
   * Cập nhật provider model
   */
  static async updateProviderModel(
    id: string,
    data: UpdateProviderModelDto
  ): Promise<ApiResponseDto<{ message: string }>> {
    return apiClient.patch(`${this.BASE_URL}/${id}`, data);
  }

  /**
   * Xóa provider model
   */
  static async deleteProviderModel(id: string): Promise<ApiResponseDto<{ message: string }>> {
    return apiClient.delete(`${this.BASE_URL}/${id}`);
  }
}
