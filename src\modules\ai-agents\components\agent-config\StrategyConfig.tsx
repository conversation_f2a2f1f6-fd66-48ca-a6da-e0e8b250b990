import { Button, CollapsibleCard, FormItem, Icon, Input, Typography } from '@/shared/components/common';
import React, { useState } from 'react';

// Interface cho dữ liệu chiến lược
interface Strategy {
  id: string;
  name: string;
  description: string;
  icon?: string;
  type: 'basic' | 'advanced' | 'custom';
}

// Interface cho dữ liệu cấu hình
interface StrategyConfigData {
  strategyId: string;
  steps: {
    step1: string;
    step2: string;
    step3: string;
    step4: string;
  };
}

interface StrategyConfigProps {
  initialData?: StrategyConfigData;
  onSave?: (data: StrategyConfigData) => void;
}

/**
 * Component hiển thị một card chiến lư<PERSON>
 */
const StrategyCard: React.FC<{
  strategy: Strategy;
  isSelected: boolean;
  onClick: () => void;
}> = ({ strategy, isSelected, onClick }) => {
  // Xác định màu nền dựa trên loại chiến lược
  const getBgColorClass = () => {
    switch (strategy.type) {
      case 'basic':
        return 'bg-blue-50 dark:bg-blue-900/10';
      case 'advanced':
        return 'bg-purple-50 dark:bg-purple-900/10';
      case 'custom':
        return 'bg-green-50 dark:bg-green-900/10';
      default:
        return 'bg-gray-50 dark:bg-gray-800/10';
    }
  };

  // Xác định màu viền khi được chọn
  const getBorderClass = () => {
    if (!isSelected) return 'border-gray-200 dark:border-gray-700';

    switch (strategy.type) {
      case 'basic':
        return 'border-blue-500 dark:border-blue-400';
      case 'advanced':
        return 'border-purple-500 dark:border-purple-400';
      case 'custom':
        return 'border-green-500 dark:border-green-400';
      default:
        return 'border-primary dark:border-primary';
    }
  };

  // Xác định icon dựa trên loại chiến lược
  const getIconName = () => {
    if (strategy.icon) return strategy.icon;

    switch (strategy.type) {
      case 'basic':
        return 'chat';
      case 'advanced':
        return 'settings';
      case 'custom':
        return 'code';
      default:
        return 'document';
    }
  };

  return (
    <div
      className={`flex flex-col p-4 ${getBgColorClass()} rounded-lg border-2 ${getBorderClass()} cursor-pointer transition-all hover:shadow-md ${isSelected ? 'shadow-md' : ''}`}
      onClick={onClick}
    >
      {/* Icon/Header */}
      <div className="flex items-center mb-3">
        <div className="w-10 h-10 rounded-full bg-white dark:bg-gray-800 flex items-center justify-center mr-3 flex-shrink-0 border border-gray-200 dark:border-gray-700">
          <Icon
            name={getIconName()}
            size="md"
            className={`text-${strategy.type === 'basic' ? 'blue' : strategy.type === 'advanced' ? 'purple' : 'green'}-600`}
          />
        </div>
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">{strategy.name}</h4>
      </div>

      {/* Mô tả */}
      <p className="text-sm text-gray-500 dark:text-gray-400 flex-grow mb-2">{strategy.description}</p>

      {/* Indicator khi được chọn */}
      {isSelected && (
        <div className="flex items-center justify-end mt-2">
          <div className="flex items-center text-primary">
            <Icon name="check-circle" size="sm" className="mr-1" />
            <span className="text-xs font-medium">Đã chọn</span>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Component cấu hình chiến lược cho Agent
 */
const StrategyConfig: React.FC<StrategyConfigProps> = ({
  initialData,
  onSave
}) => {
  // Danh sách chiến lược mẫu
  const strategies: Strategy[] = [
    {
      id: 'strategy1',
      name: 'Chiến lược cơ bản',
      description: 'Chiến lược đơn giản với các cài đặt mặc định',
      type: 'basic'
    },
    {
      id: 'strategy2',
      name: 'Chiến lược nâng cao',
      description: 'Chiến lược với các tùy chọn nâng cao và xử lý phức tạp',
      type: 'advanced'
    },
    {
      id: 'strategy3',
      name: 'Chiến lược tùy chỉnh',
      description: 'Tạo chiến lược riêng với các cài đặt tùy chỉnh hoàn toàn',
      type: 'custom'
    }
  ];

  // State cho dữ liệu cấu hình
  const [configData, setConfigData] = useState<StrategyConfigData>(initialData || {
    strategyId: '',
    steps: {
      step1: '',
      step2: '',
      step3: '',
      step4: ''
    }
  });

  // State cho trạng thái mở/đóng của card
  const [isExpanded, setIsExpanded] = useState(false);

  // State cho chiến lược được chọn
  const [selectedStrategyId, setSelectedStrategyId] = useState<string>(initialData?.strategyId || '');

  // Xử lý khi chọn một chiến lược
  const handleSelectStrategy = (strategyId: string) => {
    setSelectedStrategyId(strategyId);
    setConfigData(prev => ({
      ...prev,
      strategyId
    }));
  };

  // Xử lý khi thay đổi giá trị input
  const handleInputChange = (step: keyof StrategyConfigData['steps'], value: string) => {
    setConfigData(prev => ({
      ...prev,
      steps: {
        ...prev.steps,
        [step]: value
      }
    }));
  };

  // Xử lý khi lưu cấu hình
  const handleSave = () => {
    if (onSave) {
      onSave(configData);
    }
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center">
          <Icon name="workflow" size="md" className="mr-2" />
          <span>Chiến lược</span>
        </div>
      }
      defaultOpen={isExpanded}
      onToggle={setIsExpanded}
      className="mb-6"
    >
      <div className="p-4 space-y-6">
        {/* Tiêu đề chính */}
        <div className="mb-6 text-center">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Chọn chiến lược cho Agent
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Chọn một chiến lược và cấu hình các bước xử lý
          </p>
        </div>

        {/* Danh sách chiến lược */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {strategies.map(strategy => (
            <StrategyCard
              key={strategy.id}
              strategy={strategy}
              isSelected={selectedStrategyId === strategy.id}
              onClick={() => handleSelectStrategy(strategy.id)}
            />
          ))}
        </div>

        {/* Phần cấu hình các bước */}
        {selectedStrategyId && (
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <Typography variant="subtitle1" className="mb-4">
              Cấu hình chiến lược
            </Typography>

            <div className="space-y-4">
              {/* Bước 1 */}
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <Typography variant="subtitle2" className="mb-2">
                  Bước 1
                </Typography>
                <FormItem>
                  <Input
                    value={configData.steps.step1}
                    onChange={(e) => handleInputChange('step1', e.target.value)}
                    placeholder="Nhập thông tin cho bước 1"
                    fullWidth
                  />
                </FormItem>
              </div>

              {/* Bước 2 */}
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <Typography variant="subtitle2" className="mb-2">
                  Bước 2
                </Typography>
                <FormItem>
                  <Input
                    value={configData.steps.step2}
                    onChange={(e) => handleInputChange('step2', e.target.value)}
                    placeholder="Nhập thông tin cho bước 2"
                    fullWidth
                  />
                </FormItem>
              </div>

              {/* Bước 3 */}
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <Typography variant="subtitle2" className="mb-2">
                  Bước 3
                </Typography>
                <FormItem>
                  <Input
                    value={configData.steps.step3}
                    onChange={(e) => handleInputChange('step3', e.target.value)}
                    placeholder="Nhập thông tin cho bước 3"
                    fullWidth
                  />
                </FormItem>
              </div>

              {/* Bước 4 */}
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <Typography variant="subtitle2" className="mb-2">
                  Bước 4
                </Typography>
                <FormItem label="Input">
                  <Input
                    value={configData.steps.step4}
                    onChange={(e) => handleInputChange('step4', e.target.value)}
                    placeholder="Nhập thông tin cho bước 4"
                    fullWidth
                  />
                </FormItem>
              </div>
            </div>

            {/* Nút lưu */}
            <div className="mt-6 flex justify-end">
              <Button variant="primary" onClick={handleSave}>
                <Icon name="save" size="sm" className="mr-1" />
                Lưu cấu hình
              </Button>
            </div>
          </div>
        )}
      </div>
    </CollapsibleCard>
  );
};

export default StrategyConfig;

// Export các interface để có thể sử dụng ở các file khác
export type { Strategy, StrategyConfigData, StrategyConfigProps };

