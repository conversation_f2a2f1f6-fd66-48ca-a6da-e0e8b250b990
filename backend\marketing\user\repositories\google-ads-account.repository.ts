import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GoogleAdsAccount } from '../entities/google-ads-account.entity';

@Injectable()
export class GoogleAdsAccountRepository {
  constructor(
    @InjectRepository(GoogleAdsAccount)
    private readonly repository: Repository<GoogleAdsAccount>,
  ) {}

  /**
   * Tìm tài khoản Google Ads theo ID
   * @param id ID của tài khoản
   * @returns Tài khoản Google Ads
   */
  async findById(id: number): Promise<GoogleAdsAccount | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm tài khoản Google Ads theo ID và ID người dùng
   * @param id ID của tài khoản
   * @param userId ID của người dùng
   * @returns Tài khoản Google Ads
   */
  async findByIdAndUserId(id: number, userId: number): Promise<GoogleAdsAccount | null> {
    return this.repository.findOne({ where: { id, userId } });
  }

  /**
   * Tìm tài khoản Google Ads theo Customer ID
   * @param customerId Customer ID của tài khoản Google Ads
   * @param userId ID của người dùng
   * @returns Tài khoản Google Ads
   */
  async findByCustomerId(customerId: string, userId: number): Promise<GoogleAdsAccount | null> {
    return this.repository.findOne({ where: { customerId, userId } });
  }

  /**
   * Lấy danh sách tài khoản Google Ads của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách tài khoản Google Ads
   */
  async findByUserId(userId: number): Promise<GoogleAdsAccount[]> {
    return this.repository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tạo tài khoản Google Ads mới
   * @param data Dữ liệu tài khoản
   * @returns Tài khoản Google Ads đã tạo
   */
  async create(data: Partial<GoogleAdsAccount>): Promise<GoogleAdsAccount> {
    const account = this.repository.create(data);
    return this.repository.save(account);
  }

  /**
   * Cập nhật tài khoản Google Ads
   * @param id ID của tài khoản
   * @param data Dữ liệu cập nhật
   * @returns true nếu cập nhật thành công
   */
  async update(id: number, data: Partial<GoogleAdsAccount>): Promise<boolean> {
    const result = await this.repository.update(id, data);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Xóa tài khoản Google Ads
   * @param id ID của tài khoản
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }
}
