import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, getSchemaPath } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ZaloService } from '../services/zalo.service';
import { ZaloZnsTemplate, ZaloZnsMessage } from '../entities';
import {
  RegisterZnsTemplateDto,
  SendZnsMessageDto,
  ZnsMessageQueryDto,
  ZnsMessageResponseDto,
  ZnsTemplateQueryDto,
  ZnsTemplateResponseDto,
} from '../dto/zalo';

/**
 * Controller xử lý API liên quan đến Zalo <PERSON>NS
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_ZNS)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/:oaId/zns')
export class ZaloZnsController {
  constructor(private readonly zaloService: ZaloService) {}

  /**
   * Lấy danh sách template ZNS
   */
  @Get('templates')
  @ApiOperation({ summary: 'Lấy danh sách template ZNS' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZnsTemplateResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getTemplates(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query() queryDto: ZnsTemplateQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloZnsTemplate>>> {
    const result = await this.zaloService.getZnsTemplates(user.id, oaId, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách template ZNS thành công');
  }

  /**
   * Lấy thông tin chi tiết template ZNS
   */
  @Get('templates/:id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết template ZNS' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsTemplateResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  async getTemplateDetail(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ZaloZnsTemplate>> {
    const result = await this.zaloService.getZnsTemplateDetail(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết template ZNS thành công');
  }

  /**
   * Đăng ký template ZNS
   */
  @Post('templates')
  @ApiOperation({ summary: 'Đăng ký template ZNS' })
  @ApiResponse({
    status: 201,
    description: 'Đăng ký template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsTemplateResponseDto) }
          }
        }
      ]
    }
  })
  async registerTemplate(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() registerDto: RegisterZnsTemplateDto,
  ): Promise<ApiResponseDto<ZaloZnsTemplate>> {
    const result = await this.zaloService.registerZnsTemplate(user.id, oaId, registerDto);
    return ApiResponseDto.success(result, 'Đăng ký template ZNS thành công');
  }

  /**
   * Cập nhật trạng thái template ZNS
   */
  @Put('templates/:id/status/:status')
  @ApiOperation({ summary: 'Cập nhật trạng thái template ZNS' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsTemplateResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  async updateTemplateStatus(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
    @Param('status') status: string,
  ): Promise<ApiResponseDto<ZaloZnsTemplate>> {
    const result = await this.zaloService.updateZnsTemplateStatus(user.id, oaId, id, status);
    return ApiResponseDto.success(result, 'Cập nhật trạng thái template ZNS thành công');
  }

  /**
   * Gửi tin nhắn ZNS
   */
  @Post('messages')
  @ApiOperation({ summary: 'Gửi tin nhắn ZNS' })
  @ApiResponse({
    status: 201,
    description: 'Gửi tin nhắn ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsMessageResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  @ApiResponse({ status: 400, description: 'Template ZNS chưa được phê duyệt' })
  async sendMessage(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() sendDto: SendZnsMessageDto,
  ): Promise<ApiResponseDto<ZaloZnsMessage>> {
    const result = await this.zaloService.sendZnsMessage(user.id, oaId, sendDto);
    return ApiResponseDto.success(result, 'Gửi tin nhắn ZNS thành công');
  }

  /**
   * Lấy lịch sử tin nhắn ZNS
   */
  @Get('messages')
  @ApiOperation({ summary: 'Lấy lịch sử tin nhắn ZNS' })
  @ApiResponse({
    status: 200,
    description: 'Lấy lịch sử tin nhắn ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZnsMessageResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async getMessages(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query() queryDto: ZnsMessageQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloZnsMessage>>> {
    const result = await this.zaloService.getZnsMessages(user.id, oaId, queryDto);
    return ApiResponseDto.success(result, 'Lấy lịch sử tin nhắn ZNS thành công');
  }

  /**
   * Lấy thông tin chi tiết tin nhắn ZNS
   */
  @Get('messages/:id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết tin nhắn ZNS' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết tin nhắn ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsMessageResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy tin nhắn ZNS' })
  async getMessageDetail(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ZaloZnsMessage>> {
    const result = await this.zaloService.getZnsMessageDetail(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết tin nhắn ZNS thành công');
  }
}
