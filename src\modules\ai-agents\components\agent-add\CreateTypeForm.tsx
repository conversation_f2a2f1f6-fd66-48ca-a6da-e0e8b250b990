import { Button, Input, Textarea } from '@/shared/components/common';
import React, { useState, useMemo } from 'react';
import { useCreateTypeAgent } from '../../hooks/useTypeAgent';
import { useUserGroupTools } from '@/modules/tools/hooks/useUserGroupTool';
import { TypeAgentConfig } from '../../types/dto';
import {
  convertCustomAgentFormToDto,
  FULL_TYPE_AGENT_CONFIG_FIELDS,
  validateCustomAgentForm,
} from '../../utils/type-agent.utils';
import ConfigFieldsGrid from '../common/ConfigFieldsGrid';
import { CustomAgentFormData } from './CustomAgentForm';
import GroupToolTable, { GroupTool } from './GroupToolTable';

interface CreateTypeFormProps {
  onSave: (data: CustomAgentFormData) => void;
  onCancel: () => void;
  initialData?: CustomAgentFormData;
  onSuccess?: () => void; // Callback khi tạo thành công
}

/**
 * Component form tạo và cấu hình Agent
 */
const CreateTypeForm: React.FC<CreateTypeFormProps> = ({
  onSave,
  onCancel,
  initialData,
  onSuccess
}) => {
  // Hook để tạo type agent
  const createTypeAgentMutation = useCreateTypeAgent();

  // Hook để lấy danh sách group tools
  const {
    data: groupToolsResponse,
    isLoading: isLoadingGroupTools,
    error: groupToolsError
  } = useUserGroupTools({
    page: 1,
    limit: 100, // Lấy nhiều để có đủ lựa chọn
  });
  // Chuyển đổi dữ liệu từ API sang format của GroupTool - wrapped in useMemo
  const groupTools: GroupTool[] = useMemo(() => {
    return groupToolsResponse?.items?.map(item => ({
      id: item.id,
      name: item.name,
      description: item.description || '',
      userId: item.createdBy?.id || 0, // Sử dụng createdBy.id hoặc default 0
      createdAt: item.createdAt.toString(),
      updatedAt: item.updatedAt.toString()
    })) || [];
  }, [groupToolsResponse?.items]);

  // State cho form
  const [formData, setFormData] = useState<CustomAgentFormData>(initialData || {
    name: "Custom Agent",
    description: "Loại agent tùy chỉnh của người dùng",
    config: {
      hasProfile: true,
      hasOutput: true,
      hasConversion: false,
      hasResources: true,
      hasStrategy: false,
      hasMultiAgent: false
    },
    groupToolIds: [] // Sẽ được set sau khi load xong group tools
  });

  // State cho validation errors
  const [errors, setErrors] = useState<Record<string, string>>({});

  // State cho loading
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Xử lý thay đổi input text
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Xử lý thay đổi checkbox config
  const handleConfigChange = (configKey: keyof TypeAgentConfig) => {
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        [configKey]: !prev.config[configKey]
      }
    }));
  };

  // Xử lý thay đổi group tool được chọn
  const handleGroupToolChange = (selectedIds: number[]) => {
    setFormData(prev => ({
      ...prev,
      groupToolIds: selectedIds
    }));
  };

  // Xử lý khi lưu form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear errors trước đó
    setErrors({});

    // Validate form
    const validation = validateCustomAgentForm(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    try {
      setIsSubmitting(true);

      // Chuyển đổi dữ liệu form sang DTO
      const createDto = convertCustomAgentFormToDto(formData);

      // Gọi API tạo type agent
      await createTypeAgentMutation.mutateAsync(createDto);

      // Gọi callback onSave để thông báo cho component cha
      onSave(formData);

      // Gọi callback onSuccess nếu có
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('Lỗi khi tạo Type Agent:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="p-4 space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Tên Agent
          </label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="Nhập tên agent"
            required
            className={'w-full'}
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name}</p>
          )}
        </div>
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Mô tả
          </label>
          <Textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="Nhập mô tả agent"
            rows={3}
            className={errors.description ? 'border-red-500' : ''}
          />
          {errors.description && (
            <p className="text-red-500 text-sm mt-1">{errors.description}</p>
          )}
        </div>
      </div>

      <div className="p-4 space-y-4">
        <ConfigFieldsGrid
          fields={FULL_TYPE_AGENT_CONFIG_FIELDS}
          config={formData.config}
          onChange={handleConfigChange}
          columns={2}
          descriptionMode="tooltip"
        />
      </div>
      <div className="p-4">
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
          Chọn các group tool cho agent này (không bắt buộc)
        </p>
        {isLoadingGroupTools ? (
          <div className="flex justify-center items-center py-8">
            <div className="text-gray-500">Đang tải danh sách group tools...</div>
          </div>
        ) : groupToolsError ? (
          <div className="flex justify-center items-center py-8">
            <div className="text-red-500">
              Lỗi khi tải danh sách group tools. Vui lòng thử lại.
            </div>
          </div>
        ) : groupTools.length === 0 ? (
          <div className="flex justify-center items-center py-8">
            <div className="text-gray-500">
              Không có group tools nào. Vui lòng tạo group tools trước.
            </div>
          </div>
        ) : (
          <GroupToolTable
            groups={groupTools}
            selectedGroupIds={formData.groupToolIds}
            onSelectionChange={handleGroupToolChange}
          />
        )}
      </div>

      {/* Hiển thị lỗi config */}
      {errors.config && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <p className="text-red-600 text-sm">{errors.config}</p>
        </div>
      )}

      {/* Hiển thị trạng thái loading */}
      {createTypeAgentMutation.isPending && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
          <p className="text-blue-600 text-sm">Đang tạo Type Agent...</p>
        </div>
      )}

      <div className="flex justify-end space-x-4">
        <Button
          variant="secondary"
          type="button"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Hủy
        </Button>
        <Button
          variant="primary"
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Đang lưu...' : 'Lưu'}
        </Button>
      </div>
    </form>
  );
};

export default CreateTypeForm;
