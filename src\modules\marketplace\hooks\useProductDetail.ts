import { useQuery } from '@tanstack/react-query';
import { MarketplaceApiService } from '../services/marketplace-api.service';
import { transformApiProductToDetailAsync } from '../utils/data-transformer';
import { PRODUCT_QUERY_KEYS } from '../constants/product-query-keys';

/**
 * Hook để lấy thông tin chi tiết sản phẩm từ API
 * @param productId ID của sản phẩm (number)
 */
export const useProductDetail = (productId: number | string) => {
  const numericProductId = typeof productId === 'string' ? parseInt(productId, 10) : productId;

  return useQuery({
    queryKey: PRODUCT_QUERY_KEYS.DETAIL(numericProductId),
    queryFn: async () => {
      console.log('🔍 [useProductDetail] Fetching product detail for ID:', numericProductId);
      const apiProduct = await MarketplaceApiService.getProductDetail(numericProductId);
      console.log('✅ [useProductDetail] API product received:', apiProduct);

      // Sử dụng async version để fetch nội dung thực tế
      const transformedProduct = await transformApiProductToDetailAsync(apiProduct);
      console.log('✅ [useProductDetail] Transformed product with content:', transformedProduct);
      return transformedProduct;
    },
    enabled: !!numericProductId && !isNaN(numericProductId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để thêm sản phẩm vào giỏ hàng (re-export từ useCartApi)
 */
export { useAddToCart, useCartManager } from './useCartApi';
