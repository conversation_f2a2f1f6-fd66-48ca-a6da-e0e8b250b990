import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Form, FormItem, Input, Button, Icon } from '@/shared/components/common';
import { createRegisterSchema, RegisterFormValues } from '../schemas/auth.schema';
import { useAuthCommon } from '@/shared/hooks';
import { useRegister } from '../hooks/useAuthQuery';
import { useRecaptcha } from '../hooks/useRecaptcha';
import { useFormErrors } from '@/shared/hooks';
// import { env } from '@/shared/utils'; // Unused import
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { RegisterResponse } from '../types/auth.types';

interface RegisterFormProps {
  onSuccess?: () => void;
}

/**
 * Register form component
 */
const RegisterForm: React.FC<RegisterFormProps> = ({ onSuccess }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { saveVerifyInfo } = useAuthCommon();
  const { mutate: register, isPending } = useRegister();
  const { formRef, setFormErrors } = useFormErrors<RegisterFormValues>();

  // Sử dụng hook useRecaptcha để quản lý reCAPTCHA
  const {
    recaptchaToken,
    error: recaptchaError,
    resetRecaptcha,
  } = useRecaptcha('register-recaptcha-container', 'REGISTER');

  // Hiển thị lỗi reCAPTCHA nếu có
  useEffect(() => {
    if (recaptchaError) {
      // Hiển thị lỗi reCAPTCHA dưới trường email
      setFormErrors({
        email: t('auth.recaptchaError', 'Lỗi xác thực reCAPTCHA: ') + recaptchaError,
      });
    }
  }, [recaptchaError, t, setFormErrors]);

  // Create register schema with translations
  const registerSchema = createRegisterSchema(t);

  // Handle form submission
  const handleSubmit = (values: unknown) => {
    // Use type assertion with a specific type instead of 'any'
    const registerValues = values as RegisterFormValues;

    // Reset form errors
    setFormErrors({});

    // Kiểm tra xem reCAPTCHA đã được xác thực chưa
    if (!recaptchaToken) {
      // Hiển thị lỗi reCAPTCHA dưới trường email
      setFormErrors({
        email: t('auth.recaptchaError', 'Vui lòng xác thực reCAPTCHA'),
      });

      // Thử reset reCAPTCHA
      resetRecaptcha();

      return;
    }

    // Call register API using the mutate function from useRegister hook
    register(
      {
        email: registerValues.email,
        password: registerValues.password,
        fullName: registerValues.fullName,
        phone: registerValues.phone, // Đúng tên trường theo RegisterFormValues
        recaptchaToken: recaptchaToken,
      },
      {
        onSuccess: (response: ApiResponseDto<RegisterResponse>) => {
          // Reset reCAPTCHA sau khi đăng ký thành công
          resetRecaptcha();

          // Xử lý các mã phản hồi khác nhau
          if (response.code === 200) {
            // Đăng ký thành công
            if (onSuccess) {
              onSuccess();
            }

            // Lưu thông tin xác thực vào Redux
            if (response.result) {
              saveVerifyInfo({
                verifyToken: response.result.otpToken,
                expiresAt: response.result.expiresAt,
                info: response.result.info,
              });

              // Log để debug
              console.log('Đăng ký thành công, lưu thông tin xác thực:', {
                otpToken: response.result.otpToken,
                expiresAt: response.result.expiresAt,
                formattedExpiresAt: new Date(response.result.expiresAt).toLocaleString(),
              });
            }

            // Chuyển hướng đến trang xác thực email
            navigate('/auth/verify-email');
          }
        },
        onError: (error: unknown) => {
          console.error('Register error:', error);

          // Lấy thông báo lỗi từ response API
          const errorMsg = t('auth.registerError', 'Đăng ký thất bại');

          // Kiểm tra xem error có phải là AxiosError không
          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as {
              response: {
                data?: {
                  message?: string;
                  code?: number;
                  path?: string;
                  requestId?: string;
                };
              };
            };

            // Kiểm tra mã lỗi 10008 (Email đã được sử dụng)
            if (axiosError.response.data?.code === 10008) {
              // Hiển thị lỗi dưới trường email
              setFormErrors({
                email: t('auth.emailAlreadyUsed', 'Email đã được sử dụng'),
              });
            }
            // Kiểm tra mã lỗi 10009 (Số điện thoại đã được sử dụng)
            else if (axiosError.response.data?.code === 10009) {
              // Hiển thị lỗi dưới trường phone
              setFormErrors({
                phone: t('auth.phoneAlreadyUsed', 'Số điện thoại đã được sử dụng'),
              });
            }
            // Xử lý các lỗi khác bằng cách hiển thị lỗi dưới trường phù hợp
            else if (axiosError.response.data?.message) {
              // Đặt lỗi chung cho trường email (có thể thay đổi thành trường khác nếu cần)
              setFormErrors({
                email: axiosError.response.data.message,
              });
            }
          } else {
            // Hiển thị lỗi chung dưới trường email
            setFormErrors({
              email: errorMsg,
            });
          }

          // Reset reCAPTCHA sau khi đăng ký thất bại
          resetRecaptcha();
        },
      }
    );
  };

  return (
    <Form
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ref={formRef as any}
      schema={registerSchema}
      onSubmit={handleSubmit}
      className="space-y-4"
      autoComplete="off"
    >
      <FormItem name="fullName" label={t('auth.fullName')} required>
        <Input
          placeholder={t('auth.fullName')}
          leftIcon={<Icon name="user" size="sm" />}
          fullWidth
          autoComplete="off"
        />
      </FormItem>

      <FormItem name="email" label={t('auth.email')} required>
        <Input
          type="email"
          placeholder={t('auth.email')}
          leftIcon={<Icon name="mail" size="sm" />}
          fullWidth
          autoComplete="off"
        />
      </FormItem>

      <FormItem name="phone" label={t('auth.phone')} required>
        <Input placeholder={t('auth.phone')} leftIcon={<Icon name="phone" size="sm" />} fullWidth />
      </FormItem>

      <FormItem
        name="password"
        label={t('auth.password')}
        required
        helpText={t('auth.passwordRequirements')}
      >
        <Input
          type="password"
          placeholder="••••••••"
          leftIcon={<Icon name="lock" size="sm" />}
          fullWidth
          autoComplete="off"
        />
      </FormItem>

      <div className="mb-4">
        {/* Container cho reCAPTCHA */}
        <div
          id="register-recaptcha-container"
          className="flex justify-center min-h-[78px] dark:border-gray-700 rounded-md p-2"
        ></div>
      </div>

      <Button type="submit" variant="primary" fullWidth isLoading={isPending}>
        {t('auth.signUp')}
      </Button>
    </Form>
  );
};

export default RegisterForm;
