import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi tạo URL tạm thời để tải lên avatar
 */
export class AvatarUploadResponseDto {
  @ApiProperty({
    description: 'URL tạm thời để tải lên avatar',
    example: 'https://storage.example.com/upload/signed-url?token=abc123'
  })
  uploadUrl: string;

  @ApiProperty({
    description: 'Khóa S3 cho avatar',
    example: 'employee-avatars/images/avatar-1-1682506092000-uuid'
  })
  avatarKey: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của URL tạm thời (giây)',
    example: 300,
    required: false
  })
  expiresIn?: number;

  @ApiProperty({
    description: 'Thời điểm hết hạn của URL tạm thời (timestamp)',
    example: 1746968772000
  })
  expiresAt: number;
}
