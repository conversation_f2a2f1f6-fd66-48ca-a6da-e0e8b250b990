import React from 'react';
import { useTranslation } from 'react-i18next';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Trang tổng quan quản lý tích hợp cho Admin
 */
const AdminIntegrationManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Email Server Card */}
        <ModuleCard
          title={t('admin:integration.email.title', 'Quản lý Email Server')}
          description={t(
            'admin:integration.email.description',
            'Quản lý cấu hình máy chủ email cho hệ thống gửi email tự động'
          )}
          icon="mail"
          linkTo="/admin/integration/email"
        />

        {/* SMS Server Card */}
        <ModuleCard
          title={t('admin:integration.sms.title', 'Quản lý SMS Server')}
          description={t(
            'admin:integration.sms.description',
            'Quản lý cấu hình máy chủ SMS cho hệ thống gửi tin nhắn tự động'
          )}
          icon="message-circle"
          linkTo="/admin/integration/sms"
        />

        {/* Payment Gateway Card */}
        <ModuleCard
          title={t('admin:integration.payment.title', 'Quản lý Payment Gateway')}
          description={t(
            'admin:integration.payment.description',
            'Quản lý cấu hình cổng thanh toán cho hệ thống'
          )}
          icon="credit-card"
          linkTo="/admin/integration/payment"
        />

        {/* Social Media Card */}
        <ModuleCard
          title={t('admin:integration.social.title', 'Quản lý Social Media')}
          description={t(
            'admin:integration.social.description',
            'Quản lý tích hợp với các nền tảng mạng xã hội'
          )}
          icon="share-2"
          linkTo="/admin/integration/social"
        />

        {/* API Keys Card */}
        <ModuleCard
          title={t('admin:integration.apiKeys.title', 'Quản lý API Keys')}
          description={t(
            'admin:integration.apiKeys.description',
            'Quản lý các khóa API và cấu hình bảo mật'
          )}
          icon="key"
          linkTo="/admin/integration/api-keys"
        />

        {/* Webhooks Card */}
        <ModuleCard
          title={t('admin:integration.webhooks.title', 'Quản lý Webhooks')}
          description={t(
            'admin:integration.webhooks.description',
            'Quản lý các webhook và tích hợp với hệ thống bên ngoài'
          )}
          icon="webhook"
          linkTo="/admin/integration/webhooks"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default AdminIntegrationManagementPage;
