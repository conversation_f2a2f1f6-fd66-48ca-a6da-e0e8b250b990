import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  Button,
  Card,
  EmptyState,
  Icon,
  Input,
  Pagination,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Interface cho item Website
 */
interface Website {
  id: string;
  name: string;
  url: string;
  icon?: string;
  category?: string;
  isConnected?: boolean;
  status?: 'active' | 'pending' | 'error';
}

/**
 * Props cho component WebsiteSlideInForm
 */
interface WebsiteSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi chọn các website
   */
  onSelect: (selectedWebsites: Website[]) => void;

  /**
   * Danh sách ID của các website đã chọn
   */
  selectedWebsiteIds?: string[];
}

/**
 * Component form trượt để chọn các website để tích hợp
 */
const WebsiteSlideInForm: React.FC<WebsiteSlideInFormProps> = ({
  isVisible,
  onClose,
  onSelect,
  selectedWebsiteIds = [],
}) => {
  // State cho dữ liệu và UI
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');
  const [websites, setWebsites] = useState<Website[]>([]);
  const [selectedIds, setSelectedIds] = useState<string[]>(selectedWebsiteIds);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // State cho phân trang
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);

  // State cho sắp xếp và lọc
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'ASC' | 'DESC'>('ASC');
  const [filterStatus, setFilterStatus] = useState<string>('');

  // State cho thêm website mới
  const [showAddForm, setShowAddForm] = useState<boolean>(false);
  const [newWebsiteName, setNewWebsiteName] = useState<string>('');
  const [newWebsiteUrl, setNewWebsiteUrl] = useState<string>('');

  // Cấu hình cột cho bảng
  const columns: TableColumn<Website>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'website',
      title: 'Website',
      dataIndex: 'name',
      width: '50%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.icon ? (
            <img
              src={record.icon}
              alt={record.name}
              className="w-10 h-10 rounded-md mr-3 object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-md bg-green-100 flex items-center justify-center mr-3">
              <Icon name="globe" size="md" className="text-green-600" />
            </div>
          )}
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              {record.url}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'category',
      title: 'Danh mục',
      dataIndex: 'category',
      width: '25%',
    },
    {
      key: 'status',
      title: 'Trạng thái',
      dataIndex: 'status',
      width: '25%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.isConnected ? (
            <span className="text-green-500 text-sm flex items-center">
              <Icon name="check-circle" size="sm" className="mr-1" />
              Đã kết nối
            </span>
          ) : record.status === 'pending' ? (
            <span className="text-yellow-500 text-sm flex items-center">
              <Icon name="clock" size="sm" className="mr-1" />
              Đang xử lý
            </span>
          ) : record.status === 'error' ? (
            <span className="text-red-500 text-sm flex items-center">
              <Icon name="alert-circle" size="sm" className="mr-1" />
              Lỗi kết nối
            </span>
          ) : (
            <span className="text-gray-500 text-sm flex items-center">
              <Icon name="circle" size="sm" className="mr-1" />
              Chưa kết nối
            </span>
          )}
        </div>
      ),
    },
  ];

  // Giả lập dữ liệu website - sử dụng useMemo để tránh re-render không cần thiết
  const mockWebsites = useMemo<Website[]>(() => [
    { id: 'website-1', name: 'Website Công ty', url: 'https://example.com', category: 'Doanh nghiệp', isConnected: true, status: 'active' as const },
    { id: 'website-2', name: 'Website Cửa hàng', url: 'https://store.example.com', category: 'Thương mại điện tử', isConnected: true, status: 'active' as const },
    { id: 'website-3', name: 'Blog Công nghệ', url: 'https://tech.example.com', category: 'Blog', isConnected: false, status: 'pending' as const },
    { id: 'website-4', name: 'Diễn đàn Hỏi đáp', url: 'https://forum.example.com', category: 'Cộng đồng', isConnected: false, status: 'error' as const },
    { id: 'website-5', name: 'Trang Tin tức', url: 'https://news.example.com', category: 'Tin tức', isConnected: false, status: 'pending' as const },
    { id: 'website-6', name: 'Trang Sự kiện', url: 'https://events.example.com', category: 'Sự kiện', isConnected: false },
    { id: 'website-7', name: 'Trang Tuyển dụng', url: 'https://jobs.example.com', category: 'Tuyển dụng', isConnected: false },
    { id: 'website-8', name: 'Trang Đào tạo', url: 'https://training.example.com', category: 'Giáo dục', isConnected: false },
    { id: 'website-9', name: 'Trang Hỗ trợ', url: 'https://support.example.com', category: 'Hỗ trợ', isConnected: false },
    { id: 'website-10', name: 'Trang Liên hệ', url: 'https://contact.example.com', category: 'Liên hệ', isConnected: false },
  ], []);

  // Giả lập việc tải dữ liệu
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Giả lập API call
        await new Promise(resolve => setTimeout(resolve, 500));

        // Lọc dữ liệu theo tìm kiếm và trạng thái
        let filteredData = [...mockWebsites];

        if (search) {
          filteredData = filteredData.filter(website =>
            website.name.toLowerCase().includes(search.toLowerCase()) ||
            website.url.toLowerCase().includes(search.toLowerCase())
          );
        }

        if (filterStatus) {
          if (filterStatus === 'connected') {
            filteredData = filteredData.filter(website => website.isConnected);
          } else if (filterStatus === 'not-connected') {
            filteredData = filteredData.filter(website => !website.isConnected);
          } else if (filterStatus === 'pending') {
            filteredData = filteredData.filter(website => website.status === 'pending');
          } else if (filterStatus === 'error') {
            filteredData = filteredData.filter(website => website.status === 'error');
          }
        }

        // Sắp xếp dữ liệu
        filteredData.sort((a, b) => {
          if (sortBy === 'name') {
            return sortDirection === 'ASC'
              ? a.name.localeCompare(b.name)
              : b.name.localeCompare(a.name);
          } else if (sortBy === 'url') {
            return sortDirection === 'ASC'
              ? a.url.localeCompare(b.url)
              : b.url.localeCompare(a.url);
          }
          return 0;
        });

        // Phân trang
        const startIndex = (currentPage - 1) * itemsPerPage;
        const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage);

        setWebsites(paginatedData);
        setTotalItems(filteredData.length);
      } catch (error) {
        console.error('Error fetching websites:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, currentPage, itemsPerPage, sortBy, sortDirection, filterStatus]);

  // Kiểm tra có thay đổi chưa lưu không
  useEffect(() => {
    const hasUnsavedChanges =
      selectedIds.length !== selectedWebsiteIds.length ||
      selectedIds.some(id => !selectedWebsiteIds.includes(id)) ||
      selectedWebsiteIds.some(id => !selectedIds.includes(id));

    setHasChanges(hasUnsavedChanges);
  }, [selectedIds, selectedWebsiteIds]);

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setSearch(term);
    setCurrentPage(1);
  };

  // Xử lý chọn tất cả
  // const handleSelectAll = () => {
  //   if (selectedIds.length === websites.length) {
  //     setSelectedIds([]);
  //   } else {
  //     setSelectedIds(websites.map(website => website.id));
  //   }
  // };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = (column: string, direction: 'ASC' | 'DESC') => {
    setSortBy(column);
    setSortDirection(direction);
  };

  // Xử lý thêm website mới
  const handleAddWebsite = () => {
    if (!newWebsiteName || !newWebsiteUrl) {
      alert('Vui lòng nhập đầy đủ thông tin website');
      return;
    }

    // Kiểm tra URL hợp lệ
    try {
      new URL(newWebsiteUrl);
    } catch {
      alert('URL không hợp lệ. Vui lòng nhập URL đúng định dạng (ví dụ: https://example.com)');
      return;
    }

    // Tạo website mới
    const newWebsite: Website = {
      id: `website-${Date.now()}`,
      name: newWebsiteName,
      url: newWebsiteUrl,
      category: 'Khác',
      isConnected: false,
      status: 'pending',
    };

    // Thêm vào danh sách đã chọn
    setSelectedIds(prev => [...prev, newWebsite.id]);

    // Reset form
    setNewWebsiteName('');
    setNewWebsiteUrl('');
    setShowAddForm(false);

    // Reload dữ liệu
    setCurrentPage(1);
  };

  // Xử lý lưu
  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      // Giả lập API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Lấy thông tin đầy đủ của các website đã chọn
      const selectedWebsites = mockWebsites.filter(website =>
        selectedIds.includes(website.id)
      );

      onSelect(selectedWebsites);
      onClose();
    } catch (error) {
      console.error('Error saving selected websites:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    if (hasChanges) {
      const confirmed = window.confirm(
        'Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn đóng form?'
      );
      if (!confirmed) return;
    }

    setSearch('');
    setShowAddForm(false);
    onClose();
  }, [hasChanges, onClose]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'sort',
      label: 'Sắp xếp theo',
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-name',
      label: 'Tên',
      onClick: () => handleSortChange('name', sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-url',
      label: 'URL',
      onClick: () => handleSortChange('url', sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'divider',
      divider: true,
    },
    {
      id: 'filter',
      label: 'Lọc theo',
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'filter-all',
      label: 'Tất cả',
      onClick: () => setFilterStatus(''),
    },
    {
      id: 'filter-connected',
      label: 'Đã kết nối',
      onClick: () => setFilterStatus('connected'),
    },
    {
      id: 'filter-not-connected',
      label: 'Chưa kết nối',
      onClick: () => setFilterStatus('not-connected'),
    },
    {
      id: 'filter-pending',
      label: 'Đang xử lý',
      onClick: () => setFilterStatus('pending'),
    },
    {
      id: 'filter-error',
      label: 'Lỗi kết nối',
      onClick: () => setFilterStatus('error'),
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-6xl">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">Chọn website</Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            Đóng
          </Button>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => setShowAddForm(!showAddForm)}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Form thêm website mới */}
        {showAddForm && (
          <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <Typography variant="subtitle1" className="mb-3">Thêm website mới</Typography>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
              <div>
                <label htmlFor="websiteName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tên website
                </label>
                <Input
                  id="websiteName"
                  value={newWebsiteName}
                  onChange={(e) => setNewWebsiteName(e.target.value)}
                  placeholder="Nhập tên website"
                  fullWidth
                />
              </div>
              <div>
                <label htmlFor="websiteUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  URL
                </label>
                <Input
                  id="websiteUrl"
                  value={newWebsiteUrl}
                  onChange={(e) => setNewWebsiteUrl(e.target.value)}
                  placeholder="https://example.com"
                  fullWidth
                />
              </div>
            </div>
            <div className="flex justify-end">
              <Button
                variant="outline"
                onClick={() => setShowAddForm(false)}
                className="mr-2"
              >
                Hủy
              </Button>
              <Button
                variant="primary"
                onClick={handleAddWebsite}
                disabled={!newWebsiteName || !newWebsiteUrl}
              >
                Thêm
              </Button>
            </div>
          </div>
        )}

        {/* Bảng dữ liệu */}
        <div className="mb-4 w-full overflow-x-auto">
          <Table
            data={websites}
            columns={columns}
            loading={isLoading}
            rowKey="id"
            size="lg"
            hoverable
            bordered={false}
            selectable={true}
            className="w-full table-fixed"
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
          />

          {/* Hiển thị khi không có dữ liệu */}
          {!isLoading && websites.length === 0 && (
            <EmptyState
              icon="search"
              title="Không có kết quả"
              description="Không tìm thấy website nào phù hợp với tìm kiếm của bạn."
              className="py-8"
            />
          )}
        </div>

        {/* Phân trang */}
        <div className="mb-4">
          <Pagination
            currentPage={currentPage}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPageOptions={[5, 10, 20, 50]}
            showItemsPerPageSelector={true}
            showPageInfo={true}
          />
        </div>

        {/* Nút lưu */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            className="mr-2"
            disabled={isSubmitting}
          >
            Hủy
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            isLoading={isSubmitting}
            disabled={isLoading || !hasChanges}
          >
            Lưu
          </Button>
        </div>
      </Card>
    </SlideInForm>
  );
};

export default WebsiteSlideInForm;
