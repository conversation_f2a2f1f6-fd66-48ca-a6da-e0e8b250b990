module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '.',
  testRegex: '.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['**/*.(t|j)s'],
  coverageDirectory: '../coverage',
  testEnvironment: 'node',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/../../$1',
    '^@modules/(.*)$': '<rootDir>/../../modules/$1',
    '^@shared/(.*)$': '<rootDir>/../../shared/$1',
    '^@utils/(.*)$': '<rootDir>/../../shared/utils/$1',
    '^@database/(.*)$': '<rootDir>/../../database/$1',
    '^@config$': '<rootDir>/../../config',
  },
  setupFiles: ['<rootDir>/../../../.env'],
};
