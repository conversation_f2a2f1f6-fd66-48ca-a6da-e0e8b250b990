import { apiClient } from '@/shared/api/axios';
import {
  AssignFilesDto,
  AssignFilesResponseDto,
  CreateVectorStoreDto,
  QueryVectorStoreDto,
  VectorStoreResponseDto,
  PaginatedVectorStoreResult,
} from '../types/knowledge-files.types';

const API_BASE_URL = '/user/vector-stores';

/**
 * Tạo vector store mới
 * @param dto Thông tin vector store cần tạo
 * @returns Thông tin vector store đã tạo
 */
export const createVectorStore = async (
  dto: CreateVectorStoreDto
): Promise<VectorStoreResponseDto> => {
  const response = await apiClient.post<VectorStoreResponseDto>(API_BASE_URL, dto);
  return response.result;
};

/**
 * Lấy danh sách vector store
 * @param queryDto Tham số truy vấn
 * @returns Danh sách vector store với phân trang
 */
export const getVectorStores = async (
  queryDto?: QueryVectorStoreDto
): Promise<PaginatedVectorStoreResult> => {
  // Xây dựng query string từ queryDto
  const queryParams = new URLSearchParams();

  if (queryDto?.page) queryParams.append('page', queryDto.page.toString());
  if (queryDto?.limit) queryParams.append('limit', queryDto.limit.toString());
  if (queryDto?.search) queryParams.append('search', queryDto.search);
  if (queryDto?.sortBy) queryParams.append('sortBy', queryDto.sortBy);
  if (queryDto?.sortDirection) queryParams.append('sortDirection', queryDto.sortDirection);

  const queryString = queryParams.toString();
  const url = queryString ? `${API_BASE_URL}?${queryString}` : API_BASE_URL;

  const response = await apiClient.get<PaginatedVectorStoreResult>(url);
  return response.result;
};

/**
 * Lấy thông tin chi tiết vector store
 * @param id ID của vector store
 * @returns Thông tin chi tiết vector store
 */
export const getVectorStoreDetail = async (id: string): Promise<VectorStoreResponseDto> => {
  const response = await apiClient.get<VectorStoreResponseDto>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * Gán các file vào vector store
 * @param vectorStoreId ID của vector store
 * @param dto Thông tin các file cần gán
 * @returns Thông tin về việc gán file thành công
 */
export const assignFilesToVectorStore = async (
  vectorStoreId: string,
  dto: AssignFilesDto
): Promise<AssignFilesResponseDto> => {
  const response = await apiClient.post<AssignFilesResponseDto>(
    `${API_BASE_URL}/${vectorStoreId}/files`,
    dto
  );
  return response.result;
};

/**
 * Gỡ file khỏi vector store
 * @param vectorStoreId ID của vector store
 * @param fileId ID của file cần gỡ
 * @returns Thông tin về việc gỡ file thành công
 */
export const removeFileFromVectorStore = async (
  vectorStoreId: string,
  fileId: string
): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(
    `${API_BASE_URL}/${vectorStoreId}/files/${fileId}`
  );
  return response.result;
};

/**
 * Xóa vector store
 * @param id ID của vector store cần xóa
 * @returns Thông tin về việc xóa vector store thành công
 */
export const deleteVectorStore = async (id: string): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * Xóa nhiều vector store cùng lúc
 * @param ids Danh sách ID của các vector store cần xóa
 * @returns Thông tin về việc xóa vector store thành công
 */
export const deleteMultipleVectorStores = async (ids: string[]): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(`${API_BASE_URL}/batch`, {
    data: { ids }
  });
  return response.result;
};

/**
 * Xóa nhiều file khỏi vector store
 * @param vectorStoreId ID của vector store
 * @param fileIds Danh sách ID của các file cần xóa
 * @returns Thông tin về việc xóa file thành công
 */
export const removeFilesFromVectorStore = async (
  vectorStoreId: string,
  fileIds: string[]
): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(
    `${API_BASE_URL}/${vectorStoreId}/files`,
    { data: { fileIds } }
  );
  return response.result;
};
