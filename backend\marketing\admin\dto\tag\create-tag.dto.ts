import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho tạo tag
 */
export class CreateTagDto {
  /**
   * Tên tag
   * @example "VIP"
   */
  @ApiProperty({
    description: 'Tên tag',
    example: 'VIP',
  })
  @IsNotEmpty({ message: 'Tên tag không được để trống' })
  @IsString({ message: 'Tên tag phải là chuỗi' })
  @MaxLength(50, { message: 'Tên tag không được vượt quá 50 ký tự' })
  name: string;

  /**
   * <PERSON>ô tả tag
   * @example "Khách hàng VIP"
   */
  @ApiProperty({
    description: 'Mô tả tag',
    example: 'Khách hàng VIP',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON>ô tả tag phải là chuỗi' })
  @MaxLength(255, { message: '<PERSON><PERSON> tả tag không được vượt quá 255 ký tự' })
  description?: string;
}
