import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng zalo_zns_templates trong cơ sở dữ liệu
 * Lưu trữ thông tin về các template ZNS (Zalo Notification Service) mà người dùng đã tạo hoặc được cấp quyền sử dụng
 */
@Entity('zalo_zns_templates')
export class ZaloZnsTemplate {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID người dùng sở hữu template
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * ID của Official Account
   */
  @Column({ name: 'oa_id', length: 50 })
  oaId: string;

  /**
   * ID của template trên Zalo
   */
  @Column({ name: 'template_id', length: 50 })
  templateId: string;

  /**
   * Tên của template
   */
  @Column({ name: 'template_name', length: 255 })
  templateName: string;

  /**
   * Nội dung của template
   */
  @Column({ name: 'template_content', type: 'text' })
  templateContent: string;

  /**
   * Các tham số của template (JSON)
   */
  @Column({ name: 'params', type: 'jsonb', default: '[]' })
  params: string[];

  /**
   * Trạng thái của template (approved, pending, rejected)
   */
  @Column({ name: 'status', length: 20 })
  status: string;

  /**
   * Thời điểm tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
