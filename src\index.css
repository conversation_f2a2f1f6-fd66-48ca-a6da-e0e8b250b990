@import 'shared/styles/animation.css';
@import 'shared/styles/responsive.css';
@import 'shared/styles/theme.css';
@import 'shared/styles/scrollbar.css';
@import 'modules/generic-page/styles/generic-page.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    font-family: var(--font-family);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-normal);
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    margin: 0;
    min-height: 100vh;
    background-color: var(--color-background);
    color: var(--color-foreground);
  }

  a {
    font-weight: var(--font-weight-medium);
    color: var(--color-primary);
    text-decoration: none;
  }

  a:hover {
    color: var(--color-primary-600);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: var(--font-weight-semibold);
  }
}

@layer components {
  .btn {
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-medium);
    transition: all 0.2s;
    cursor: pointer;
  }

  .btn-primary {
    background-color: var(--color-primary);
    color: var(--color-primary-foreground);
  }

  .btn-primary:hover {
    opacity: 0.9;
  }

  .btn-secondary {
    background-color: var(--color-secondary);
    color: var(--color-secondary-foreground);
  }

  .btn-secondary:hover {
    opacity: 0.9;
  }

  .btn-outline {
    border: 1px solid var(--color-primary);
    color: var(--color-primary);
  }

  .btn-outline:hover {
    background-color: var(--color-primary);
    color: var(--color-primary-foreground);
  }

  .card {
    background-color: var(--color-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: box-shadow 0.2s;
  }

  .card:hover {
    box-shadow: var(--shadow-lg);
  }

  .input {
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-lg);
    background-color: var(--color-card-muted);
    border: 0;
    color: var(--color-foreground);
  }

  /* Border chỉ trong dark mode */
  .dark .input {
    border: 1px solid var(--color-border);
  }

  .input:focus {
    outline: none;
  }

  /* Focus border chỉ trong dark mode */
  .dark .input:focus {
    border-color: var(--color-primary);
  }

  /* Loại bỏ hiệu ứng ring màu đỏ khi focus */
  *:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Chỉ áp dụng border màu primary khi focus trong dark mode */
  .dark input:focus,
  .dark select:focus,
  .dark textarea:focus,
  .dark button:focus {
    border-color: var(--color-primary) !important;
  }

  /* Form styles */
  .form-item {
    margin-bottom: var(--spacing-4);
  }

  .form-item label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: var(--font-weight-medium);
  }

  .form-item label.required::after {
    content: '*';
    color: var(--color-error);
    margin-left: var(--spacing-1);
  }

  .form-item .error {
    color: var(--color-error);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-1);
  }

  .form-item .help-text {
    color: var(--color-muted);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-1);
  }
}

/* Custom gradient background */
.bg-brand-gradient {
  background: linear-gradient(to right, var(--color-primary-500), var(--color-secondary-500));
}

/* Flag clip paths for UK flag */
.clip-triangle-top-left {
  clip-path: polygon(0 0, 0 100%, 100% 0);
}

.clip-triangle-top-right {
  clip-path: polygon(0 0, 100% 0, 100% 100%);
}

.clip-triangle-bottom-left {
  clip-path: polygon(0 0, 0 100%, 100% 100%);
}

.clip-triangle-bottom-right {
  clip-path: polygon(100% 0, 0 100%, 100% 100%);
}
