import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AgentGrid } from '../components';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useNavigate } from 'react-router-dom';
import { useGetAgents } from '../hooks/useAgent';
import { AgentListItemDto, GetAgentsQueryDto } from '../types';
import { Loading, EmptyState } from '@/shared/components/common';
import { Button } from '@/shared/components/common';

/**
 * Trang hiển thị danh sách AI Agents
 */
const AIAgentsPage: React.FC = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const navigate = useNavigate();

  // Query parameters cho API
  const queryParams = useMemo<GetAgentsQueryDto>(() => ({
    page: 1,
    limit: 50,
    search: searchTerm || undefined,
    // C<PERSON> thể thêm filter theo type nếu API hỗ trợ
  }), [searchTerm]);

  // Gọi API để lấy danh sách agents
  const {
    data: agentsResponse,
    isLoading,
    error,
    refetch
  } = useGetAgents(queryParams);

  const handleSelectAgent = (agent: AgentListItemDto) => {
    navigate(`/ai-agents/${agent.id}`);
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const handleFilterChange = (type: string) => {
    setFilterType(type);
  };

  const handleAddAgent = () => {
    navigate('/ai-agents/add');
  };

  // Lọc agents theo loại (client-side filtering)
  const filteredAgents = useMemo(() => {
    const agents = agentsResponse?.result?.items || [];

    if (filterType === 'all') return agents;

    // Lọc theo typeName hoặc typeId
    if (filterType === 'assistant') {
      // Giả sử assistant có typeName chứa "Assistant" hoặc "Chatbot"
      return agents.filter(agent =>
        agent.typeName.toLowerCase().includes('assistant') ||
        agent.typeName.toLowerCase().includes('chatbot')
      );
    } else {
      // Các agents khác
      return agents.filter(agent =>
        !agent.typeName.toLowerCase().includes('assistant') &&
        !agent.typeName.toLowerCase().includes('chatbot')
      );
    }
  }, [agentsResponse?.result?.items, filterType]);

  // Hiển thị loading
  if (isLoading) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddAgent}
          items={[
            {
              id: 'all',
              label: t('common.all'),
              icon: 'list',
              onClick: () => handleFilterChange('all'),
            },
            {
              id: 'assistant',
              label: t('chat.aiAssistants'),
              icon: 'assistant',
              onClick: () => handleFilterChange('assistant'),
            },
            {
              id: 'agent',
              label: t('chat.specializedAgents', 'Specialized Agents'),
              icon: 'robot',
              onClick: () => handleFilterChange('agent'),
            },
          ]}
        />
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddAgent}
          items={[
            {
              id: 'all',
              label: t('common.all'),
              icon: 'list',
              onClick: () => handleFilterChange('all'),
            },
            {
              id: 'assistant',
              label: t('chat.aiAssistants'),
              icon: 'assistant',
              onClick: () => handleFilterChange('assistant'),
            },
            {
              id: 'agent',
              label: t('chat.specializedAgents', 'Specialized Agents'),
              icon: 'robot',
              onClick: () => handleFilterChange('agent'),
            },
          ]}
        />
        <EmptyState
          icon="alert-circle"
          title={t('common.error', 'Lỗi')}
          description={t('aiAgents.list.loadError', 'Không thể tải danh sách AI Agents. Vui lòng thử lại.')}
          actions={
            <Button
              variant="primary"
              onClick={() => refetch()}
            >
              {t('common.retry', 'Thử lại')}
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddAgent}
        items={[
          {
            id: 'all',
            label: t('common.all'),
            icon: 'list',
            onClick: () => handleFilterChange('all'),
          },
          {
            id: 'assistant',
            label: t('chat.aiAssistants'),
            icon: 'assistant',
            onClick: () => handleFilterChange('assistant'),
          },
          {
            id: 'agent',
            label: t('chat.specializedAgents', 'Specialized Agents'),
            icon: 'robot',
            onClick: () => handleFilterChange('agent'),
          },
        ]}
      />

      {filteredAgents.length > 0 ? (
        <AgentGrid agents={filteredAgents} onSelectAgent={handleSelectAgent} />
      ) : (
        <EmptyState
          icon="robot"
          title={t('aiAgents.list.noAgents', 'Không có AI Agents')}
          description={
            searchTerm
              ? t('aiAgents.list.noSearchResults', 'Không tìm thấy AI Agents phù hợp với từ khóa tìm kiếm.')
              : t('aiAgents.list.noAgentsDescription', 'Hiện tại chưa có AI Agents nào. Hãy tạo Agent đầu tiên của bạn.')
          }
          actions={
            <Button
              variant="primary"
              onClick={handleAddAgent}
            >
              {t('aiAgents.list.createFirst', 'Tạo AI Agent đầu tiên')}
            </Button>
          }
        />
      )}
    </div>
  );
};

export default AIAgentsPage;
