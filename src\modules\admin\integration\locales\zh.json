{"integration": {"title": "集成管理", "description": "管理系统集成和连接配置", "email": {"title": "邮件服务器管理", "description": "管理自动邮件发送的邮件服务器配置", "list": {"title": "邮件服务器列表", "empty": "暂无邮件服务器配置", "search": "按名称或主机搜索...", "columns": {"serverName": "服务器名称", "host": "主机", "port": "端口", "username": "用户名", "ssl": "SSL", "status": "状态", "actions": "操作"}}, "form": {"create": "创建邮件服务器", "edit": "编辑邮件服务器", "test": "测试连接", "fields": {"serverName": "服务器名称", "host": "主机", "port": "端口", "username": "用户名", "password": "密码", "useSsl": "使用SSL", "useStartTls": "使用StartTLS", "additionalSettings": "附加设置 (JSON)", "isActive": "激活", "recipientEmail": "测试收件人邮箱", "subject": "邮件主题"}, "placeholders": {"serverName": "输入服务器名称...", "host": "smtp.gmail.com", "port": "587", "username": "<EMAIL>", "password": "输入密码...", "additionalSettings": "{}", "recipientEmail": "<EMAIL>", "subject": "测试邮件"}}, "actions": {"create": "创建", "edit": "编辑", "delete": "删除", "test": "测试", "save": "保存", "cancel": "取消", "close": "关闭"}, "validation": {"serverName": {"required": "服务器名称是必需的", "maxLength": "服务器名称不能超过100个字符"}, "host": {"required": "主机是必需的", "maxLength": "主机不能超过255个字符"}, "port": {"min": "端口必须大于0", "max": "端口必须小于65536"}, "username": {"required": "用户名是必需的", "email": "用户名必须是有效的邮箱"}, "password": {"required": "密码是必需的", "minLength": "密码至少需要6个字符"}, "additionalSettings": {"invalidJson": "附加设置必须是有效的JSON"}, "recipientEmail": {"email": "收件人邮箱必须是有效的邮箱"}, "subject": {"maxLength": "主题不能超过200个字符"}}, "notifications": {"createSuccess": "邮件服务器创建成功", "createError": "创建邮件服务器时出错", "updateSuccess": "邮件服务器更新成功", "updateError": "更新邮件服务器时出错", "deleteSuccess": "邮件服务器删除成功", "deleteError": "删除邮件服务器时出错", "testSuccess": "连接测试成功", "testError": "测试连接时出错"}, "confirmations": {"delete": "您确定要删除此邮件服务器吗？", "deleteTitle": "确认删除"}}}}