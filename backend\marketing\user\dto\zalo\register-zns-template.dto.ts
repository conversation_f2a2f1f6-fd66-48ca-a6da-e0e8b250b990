import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc đăng ký template ZNS
 */
export class RegisterZnsTemplateDto {
  @ApiProperty({
    description: 'Tên của template',
    example: 'Thông báo đơn hàng',
  })
  @IsString()
  @IsNotEmpty()
  templateName: string;

  @ApiProperty({
    description: 'Nội dung của template',
    example: 'Đơn hàng #{orderId} của bạn đã được xác nhận. Cảm ơn bạn đã mua hàng tại {shopName}.',
  })
  @IsString()
  @IsNotEmpty()
  templateContent: string;

  @ApiProperty({
    description: 'C<PERSON>c tham số của template',
    example: ['orderId', 'shopName'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  params: string[];
}
