import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ZaloAutomationLog } from '../entities';

/**
 * Repository cho log tự động hóa Zalo
 */
@Injectable()
export class ZaloAutomationLogRepository {
  constructor(
    @InjectRepository(ZaloAutomationLog)
    private readonly repository: Repository<ZaloAutomationLog>,
  ) {}

  /**
   * Tìm log tự động hóa theo ID
   * @param id ID của log tự động hóa
   * @returns Log tự động hóa
   */
  async findById(id: number): Promise<ZaloAutomationLog | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm danh sách log tự động hóa theo ID tự động hóa
   * @param automationId ID của tự động hóa
   * @returns Danh sách log tự động hóa
   */
  async findByAutomationId(automationId: number): Promise<ZaloAutomationLog[]> {
    return this.repository.find({ where: { automationId } });
  }

  /**
   * Tìm danh sách log tự động hóa theo ID tự động hóa và trạng thái
   * @param automationId ID của tự động hóa
   * @param status Trạng thái
   * @returns Danh sách log tự động hóa
   */
  async findByAutomationIdAndStatus(automationId: number, status: string): Promise<ZaloAutomationLog[]> {
    return this.repository.find({ where: { automationId, status } });
  }

  /**
   * Đếm số lượng log tự động hóa theo ID tự động hóa và trạng thái
   * @param automationId ID của tự động hóa
   * @param status Trạng thái
   * @returns Số lượng log tự động hóa
   */
  async countByAutomationIdAndStatus(automationId: number, status: string): Promise<number> {
    return this.repository.count({ where: { automationId, status } });
  }

  /**
   * Tìm danh sách log tự động hóa với phân trang
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách log tự động hóa và tổng số log tự động hóa
   */
  async findWithPagination(options: any): Promise<[ZaloAutomationLog[], number]> {
    const { where, skip, take, order } = options;
    return this.repository.findAndCount({
      where,
      skip,
      take,
      order,
    });
  }

  /**
   * Tạo log tự động hóa mới
   * @param data Dữ liệu log tự động hóa
   * @returns Log tự động hóa đã tạo
   */
  async create(data: Partial<ZaloAutomationLog>): Promise<ZaloAutomationLog> {
    const log = this.repository.create(data);
    return this.repository.save(log);
  }

  /**
   * Tạo nhiều log tự động hóa mới
   * @param dataList Danh sách dữ liệu log tự động hóa
   * @returns Danh sách log tự động hóa đã tạo
   */
  async createMany(dataList: Partial<ZaloAutomationLog>[]): Promise<ZaloAutomationLog[]> {
    const logs = dataList.map(data => this.repository.create(data));
    return this.repository.save(logs);
  }

  /**
   * Cập nhật log tự động hóa
   * @param id ID của log tự động hóa
   * @param data Dữ liệu cập nhật
   * @returns Log tự động hóa đã cập nhật
   */
  async update(id: number, data: Partial<ZaloAutomationLog>): Promise<ZaloAutomationLog | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }
}
