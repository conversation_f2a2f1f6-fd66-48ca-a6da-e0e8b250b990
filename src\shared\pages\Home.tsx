import { useTranslation } from 'react-i18next';
import { <PERSON> } from 'react-router-dom';
import { Card, Button, ScrollArea, Icon } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';
import { useLanguage } from '@/shared/contexts/language';

// Mock data for recent chats
const recentChats = [
  { id: '1', title: 'How to implement a search algorithm', date: new Date(2024, 4, 1) },
  { id: '2', title: 'Explain quantum computing', date: new Date(2024, 4, 2) },
  { id: '3', title: 'Help with React hooks', date: new Date(2024, 4, 3) },
];

const Home = () => {
  const { t } = useTranslation();
  const { themeMode, setThemeMode } = useTheme();
  const theme = themeMode; // Compatibility
  const toggleTheme = () => setThemeMode(themeMode === 'light' ? 'dark' : 'light');
  const { language, setLanguage, availableLanguages } = useLanguage();

  // Xác định variant cho nút dựa vào theme hiện tại
  const lightBtnVariant = theme === 'light' ? 'primary' : 'outline';
  const darkBtnVariant = theme === 'dark' ? 'primary' : 'outline';

  // Format date
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString();
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div className="md:col-span-2 lg:col-span-3">
        <Card title={t('viewPanel.welcome')} className="mb-4">
          <p className="mb-4">{t('viewPanel.welcome')} - RedAI Frontend Template</p>
          <div className="flex gap-2 flex-wrap mb-4">
            <Button variant="primary">{t('chat.newChat')}</Button>
            <Link to="/ai-agents">
              <Button variant="outline">{t('chat.aiAgents')}</Button>
            </Link>

            <Link to="/components">
              <Button
                variant="primary"
                leftIcon={<Icon name="components" size="md" />}
                className="bg-menu-gradient hover:bg-menu-gradient-hover border-none"
              >
                {t('components.library.title')}
              </Button>
            </Link>

            <Link to="/demo/task-queue">
              <Button
                variant="primary"
                leftIcon={<Icon name="list-todo" size="md" />}
                className="bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 border-none"
              >
                Task Queue Demo
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2">{t('common.theme')}</h3>
              <div className="flex gap-2">
                <Button
                  variant={lightBtnVariant}
                  onClick={() => theme !== 'light' && toggleTheme()}
                >
                  {t('common.light')}
                </Button>
                <Button
                  variant={darkBtnVariant}
                  onClick={() => theme !== 'dark' && toggleTheme()}
                >
                  {t('common.dark')}
                </Button>
              </div>
            </div>

            <div>
              <h3 className="font-medium mb-2">{t('common.language')}</h3>
              <div className="flex gap-2 flex-wrap">
                {availableLanguages.map(lang => (
                  <Button
                    key={lang.code}
                    variant={language === lang.code ? 'primary' : 'outline'}
                    onClick={() => setLanguage(lang.code as 'vi' | 'en' | 'zh')}
                  >
                    {lang.name}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </Card>
      </div>

      <div className="md:col-span-2">
        <Card title={t('viewPanel.recentChats')} className="h-full">
          {recentChats.length > 0 ? (
            <ScrollArea height="300px">
              <div className="divide-y">
                {recentChats.map(chat => (
                  <div key={chat.id} className="py-3 first:pt-0 last:pb-0">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{chat.title}</h3>
                        <p className="text-sm text-gray-500">{formatDate(chat.date)}</p>
                      </div>
                      <Button variant="outline" size="sm">
                        {t('common.open')}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">{t('viewPanel.noData')}</p>
            </div>
          )}
        </Card>
      </div>
      <div>
        <Card title={t('viewPanel.favorites')} className="h-full">
          <div className="text-center py-8">
            <p className="text-gray-500">{t('viewPanel.noData')}</p>
            <Button variant="outline" size="sm" className="mt-2">
              {t('common.add')}
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Home;
