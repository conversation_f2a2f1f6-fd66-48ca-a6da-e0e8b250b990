import { Injectable } from '@nestjs/common';
import { AdminAudienceRepository } from '@modules/marketing/admin/repositories';
import { AdminAudienceCustomFieldRepository } from '@modules/marketing/admin/repositories';
import { AdminTagRepository } from '../repositories/admin-tag.repository';
import { In, Like, FindOptionsWhere } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import {
  CreateAudienceDto,
  UpdateAudienceDto,
  AudienceResponseDto,
  CustomFieldResponseDto,
  AudienceQueryDto,
  AudienceSortField,
  SortOrder,
} from '../dto/audience';
import { TagResponseDto } from '../dto/tag';
import { PaginatedResponseDto, PaginationMetaDto } from '../dto/common';
import { AdminAudience, AdminAudienceCustomField, AdminTag } from '../entities';
import { AppException, ErrorCode } from '@/common';

/**
 * Service xử lý logic liên quan đến audience
 */
@Injectable()
export class AdminAudienceService {
  constructor(
    private readonly adminAudienceRepository: AdminAudienceRepository,
    private readonly adminAudienceCustomFieldRepository: AdminAudienceCustomFieldRepository,
    private readonly adminTagRepository: AdminTagRepository,
  ) {}

  /**
   * Tạo audience mới
   * @param employeeId ID của employee
   * @param createAudienceDto Dữ liệu tạo audience
   * @returns Audience đã tạo
   */
  @Transactional()
  async create(
    createAudienceDto: CreateAudienceDto,
  ): Promise<AudienceResponseDto> {
    // Tạo audience mới
    const audience = new AdminAudience();
    audience.email = createAudienceDto.email;
    audience.phone = createAudienceDto.phone || '';
    audience.createdAt = Math.floor(Date.now() / 1000);
    audience.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu audience
    const savedAudience = await this.adminAudienceRepository.save(audience);

    // Tạo các trường tùy chỉnh nếu có
    let customFields: AdminAudienceCustomField[] = [];
    if (
      createAudienceDto.customFields &&
      createAudienceDto.customFields.length > 0
    ) {
      customFields = createAudienceDto.customFields.map((field) => {
        const customField = new AdminAudienceCustomField();
        customField.audienceId = (savedAudience as AdminAudience).id;
        customField.fieldName = field.fieldName;
        customField.fieldValue = field.fieldValue;
        customField.fieldType = field.fieldType;
        customField.createdAt = Math.floor(Date.now() / 1000);
        customField.updatedAt = Math.floor(Date.now() / 1000);
        return customField;
      });

      customFields =
        await this.adminAudienceCustomFieldRepository.save(customFields);
    }

    // Lấy các tag nếu có
    let tags: AdminTag[] = [];
    if (createAudienceDto.tagIds && createAudienceDto.tagIds.length > 0) {
      tags = await this.adminTagRepository.find({
        where: {
          id: In(createAudienceDto.tagIds),
        },
      });
    }

    // Đảm bảo savedAudience là một đối tượng AdminAudience, không phải mảng
    return this.mapToDto(savedAudience as AdminAudience, customFields, tags);
  }

  /**
   * Cập nhật audience
   * @param id ID của audience
   * @param updateAudienceDto Dữ liệu cập nhật audience
   * @returns Audience đã cập nhật
   */
  @Transactional()
  async update(
    id: number,
    updateAudienceDto: UpdateAudienceDto,
  ): Promise<AudienceResponseDto> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND, `Audience với ID ${id} không tồn tại`);
    }

    // Cập nhật thông tin audience
    if (updateAudienceDto.email) {
      audience.email = updateAudienceDto.email;
    }

    if (updateAudienceDto.phone !== undefined) {
      audience.phone = updateAudienceDto.phone || '';
    }

    audience.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu audience
    const updatedAudience = await this.adminAudienceRepository.save(audience);

    // Cập nhật các trường tùy chỉnh nếu có
    let customFields: AdminAudienceCustomField[] = [];
    if (
      updateAudienceDto.customFields &&
      updateAudienceDto.customFields.length > 0
    ) {
      // Xóa các trường tùy chỉnh cũ
      await this.adminAudienceCustomFieldRepository.delete({ audienceId: id });

      // Tạo các trường tùy chỉnh mới
      customFields = updateAudienceDto.customFields.map((field) => {
        const customField = new AdminAudienceCustomField();
        customField.audienceId = id;
        customField.fieldName = field.fieldName;
        customField.fieldValue = field.fieldValue;
        customField.fieldType = field.fieldType;
        customField.createdAt = Math.floor(Date.now() / 1000);
        customField.updatedAt = Math.floor(Date.now() / 1000);
        return customField;
      });

      const savedCustomFields =
        await this.adminAudienceCustomFieldRepository.save(customFields);
      customFields = Array.isArray(savedCustomFields)
        ? savedCustomFields
        : [savedCustomFields];
    } else {
      // Lấy các trường tùy chỉnh hiện tại
      customFields = await this.adminAudienceCustomFieldRepository.find({
        where: { audienceId: id },
      });
    }

    // Lấy các tag nếu có
    let tags: AdminTag[] = [];
    if (updateAudienceDto.tagIds && updateAudienceDto.tagIds.length > 0) {
      tags = await this.adminTagRepository.find({
        where: {
          id: In(updateAudienceDto.tagIds),
        },
      });
    }

    // Đảm bảo updatedAudience là một đối tượng AdminAudience, không phải mảng
    return this.mapToDto(updatedAudience as AdminAudience, customFields, tags);
  }

  /**
   * Lấy danh sách audience của employee với phân trang và filter
   * @param employeeId ID của employee
   * @param query Tham số truy vấn
   * @returns Danh sách audience với phân trang
   */
  async findAll(
    query: AudienceQueryDto,
  ): Promise<PaginatedResponseDto<AudienceResponseDto>> {
    const {
      page = 1,
      limit = 10,
      email,
      phone,
      tagId,
      customFieldName,
      customFieldValue,
      sortBy = AudienceSortField.CREATED_AT,
      sortOrder = SortOrder.DESC,
    } = query;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo điều kiện cơ bản
    const where: FindOptionsWhere<AdminAudience> = {};

    // Thêm điều kiện tìm kiếm theo email
    if (email) {
      where.email = Like(`%${email}%`);
    }

    // Thêm điều kiện tìm kiếm theo số điện thoại
    if (phone) {
      where.phone = Like(`%${phone}%`);
    }

    // Đếm tổng số audience
    const total = await this.adminAudienceRepository.count({ where });

    // Lấy danh sách audience với phân trang và sắp xếp
    const audiences = await this.adminAudienceRepository.find({
      where,
      order: { [sortBy]: sortOrder },
      skip: offset,
      take: limit,
    });

    // Lấy danh sách ID của audience
    const audienceIds = audiences.map((a) => a.id);

    // Lấy tất cả các trường tùy chỉnh của các audience
    let customFieldsQuery: FindOptionsWhere<AdminAudienceCustomField> = {
      audienceId: In(audienceIds),
    };

    // Thêm điều kiện tìm kiếm theo tên trường tùy chỉnh
    if (customFieldName) {
      customFieldsQuery.fieldName = Like(`%${customFieldName}%`);
    }

    // Thêm điều kiện tìm kiếm theo giá trị trường tùy chỉnh
    if (customFieldValue) {
      customFieldsQuery.fieldValue = Like(`%${customFieldValue}%`);
    }

    const customFields = await this.adminAudienceCustomFieldRepository.find({
      where: customFieldsQuery,
    });

    // Lấy tags nếu có tagId
    let tags: AdminTag[] = [];
    if (tagId) {
      tags = await this.adminTagRepository.find({
        where: { id: tagId },
      });
    }

    // Chuyển đổi kết quả thành DTO
    const data: AudienceResponseDto[] = [];
    for (const audience of audiences) {
      const audienceCustomFields = customFields.filter(
        (cf) => cf.audienceId === audience.id,
      );
      const audienceTags = tagId ? tags : [];
      data.push(this.mapToDto(audience, audienceCustomFields, audienceTags));
    }

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);
    const meta: PaginationMetaDto = {
      total,
      page,
      limit,
      totalPages,
      hasPreviousPage: page > 1,
      hasNextPage: page < totalPages,
    };

    return {
      data,
      meta,
    };
  }

  /**
   * Lấy audience theo ID
   * @param employeeId ID của employee
   * @param id ID của audience
   * @returns Audience
   */
  async findOne(id: number): Promise<AudienceResponseDto> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND, `Audience với ID ${id} không tồn tại`);
    }

    // Lấy các trường tùy chỉnh
    const customFields = await this.adminAudienceCustomFieldRepository.find({
      where: { audienceId: id },
    });

    // Lấy các tag
    const tags: AdminTag[] = []; // TODO: Nếu có bảng liên kết audience-tag, cần lấy tags

    return this.mapToDto(audience, customFields, tags);
  }

  /**
   * Xóa audience
   * @param employeeId ID của employee
   * @param id ID của audience
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async remove(id: number): Promise<boolean> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND);
    }

    // Xóa các trường tùy chỉnh
    await this.adminAudienceCustomFieldRepository.delete({ audienceId: id });

    // Xóa audience
    await this.adminAudienceRepository.remove(audience);

    return true;
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param audience Audience entity
   * @param customFields Các trường tùy chỉnh
   * @param tags Các tag
   * @returns Audience DTO
   */
  private mapToDto(
    audience: AdminAudience,
    customFields: AdminAudienceCustomField[],
    tags: AdminTag[],
  ): AudienceResponseDto {
    const dto = new AudienceResponseDto();
    dto.id = audience.id;
    dto.email = audience.email;
    dto.phone = audience.phone;
    dto.createdAt = audience.createdAt;
    dto.updatedAt = audience.updatedAt;

    // Chuyển đổi các trường tùy chỉnh
    dto.customFields = customFields.map((field) => {
      const fieldDto = new CustomFieldResponseDto();
      fieldDto.id = field.id;
      fieldDto.audienceId = field.audienceId;
      fieldDto.fieldName = field.fieldName;
      fieldDto.fieldValue = field.fieldValue;
      fieldDto.fieldType = field.fieldType as any;
      fieldDto.createdAt = field.createdAt;
      fieldDto.updatedAt = field.updatedAt;
      return fieldDto;
    });

    // Chuyển đổi các tag
    dto.tags = tags.map((tag) => {
      const tagDto = new TagResponseDto();
      tagDto.id = tag.id;
      tagDto.name = tag.name;
      tagDto.color = tag.color;
      tagDto.createdBy = tag.createdBy;
      tagDto.updatedBy = tag.updatedBy;
      tagDto.createdAt = tag.createdAt;
      tagDto.updatedAt = tag.updatedAt;
      return tagDto;
    });

    return dto;
  }
}
