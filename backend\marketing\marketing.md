# Phân tích và Đề xuất Giao diện Frontend cho Module Marketing

## 1. Tổng quan

Module Marketing quản lý các hoạt động tiếp thị, chiến dịch quả<PERSON> cáo, và các chương trình khuyến mãi trong hệ thống. Dựa trên cấu trúc thư mục backend, module này được chia thành các phần chính:

- Admin: Quản lý marketing từ phía admin
- User: Tương tác marketing từ phía người dùng
- Common: <PERSON><PERSON><PERSON> thành phần dùng chung

## 2. C<PERSON>u trúc dữ liệu và chức năng dự kiến

Dựa trên cấu trúc thư mục và README.md, module Marketing có thể bao gồm các chức năng sau:

### 2.1. <PERSON><PERSON><PERSON><PERSON> lý Chiến dịch Marketing
- <PERSON><PERSON><PERSON>, chỉnh sửa, x<PERSON>a chiến dịch
- <PERSON><PERSON><PERSON> lịch chiến dịch
- <PERSON> dõi hiệu quả chiến dịch

### 2.2. <PERSON><PERSON><PERSON><PERSON> lý <PERSON> Khuyến mãi
- Tạo, chỉnh sửa, xóa mã khuyến mãi
- Thiết lập điều kiện sử dụng
- Theo dõi việc sử dụng mã

### 2.3. Quản lý Email Marketing
- Tạo mẫu email
- Gửi email hàng loạt
- Theo dõi tỷ lệ mở email, click

### 2.4. Quản lý Thông báo
- Tạo thông báo
- Gửi thông báo đến người dùng
- Theo dõi tương tác với thông báo

### 2.5. Quản lý Affiliate
- Thiết lập chương trình tiếp thị liên kết
- Quản lý đối tác tiếp thị
- Theo dõi hoa hồng và thanh toán

## 3. Đề xuất Giao diện Frontend

### 3.1. Giao diện Admin

#### 3.1.1. Trang Tổng quan Marketing
- **Thành phần**: Dashboard với các biểu đồ và số liệu
- **Chức năng**:
  - Hiển thị tổng quan về hiệu quả marketing
  - Biểu đồ số lượng người dùng mới theo thời gian
  - Biểu đồ chuyển đổi từ các chiến dịch
  - Tỷ lệ mở email, click
  - Số lượng mã khuyến mãi đã sử dụng
- **Hiển thị**:
  - Các KPI chính (số người dùng mới, tỷ lệ chuyển đổi, doanh thu từ marketing)
  - Biểu đồ theo thời gian
  - Bảng xếp hạng chiến dịch hiệu quả nhất

#### 3.1.2. Trang Quản lý Chiến dịch
- **Thành phần**: Bảng dữ liệu (DataTable)
- **Chức năng**:
  - Tìm kiếm chiến dịch theo tên, loại
  - Lọc theo trạng thái (đang chạy, đã kết thúc, sắp diễn ra)
  - Phân trang
  - Thêm chiến dịch mới
  - Sửa thông tin chiến dịch
  - Xóa chiến dịch
- **Cột hiển thị**:
  - ID
  - Tên chiến dịch
  - Loại chiến dịch
  - Ngày bắt đầu
  - Ngày kết thúc
  - Trạng thái
  - Số lượng chuyển đổi
  - Hành động (Sửa, Xóa, Chi tiết)

#### 3.1.3. Form Thêm/Sửa Chiến dịch
- **Thành phần**: Form với các trường
- **Trường dữ liệu**:
  - Tên chiến dịch (input text)
  - Mô tả (textarea)
  - Loại chiến dịch (select)
  - Ngày bắt đầu (datepicker)
  - Ngày kết thúc (datepicker)
  - Ngân sách (input number)
  - Mục tiêu (input text)
  - Đối tượng mục tiêu (multi-select)
  - Kênh marketing (multi-select)
  - Trạng thái (select)

#### 3.1.4. Trang Quản lý Mã Khuyến mãi
- **Thành phần**: Bảng dữ liệu (DataTable)
- **Chức năng**:
  - Tìm kiếm mã khuyến mãi theo mã, loại
  - Lọc theo trạng thái (đang hoạt động, hết hạn)
  - Phân trang
  - Thêm mã khuyến mãi mới
  - Sửa thông tin mã khuyến mãi
  - Xóa mã khuyến mãi
- **Cột hiển thị**:
  - ID
  - Mã khuyến mãi
  - Loại giảm giá (phần trăm, số tiền cố định)
  - Giá trị giảm
  - Ngày bắt đầu
  - Ngày kết thúc
  - Số lần sử dụng tối đa
  - Số lần đã sử dụng
  - Trạng thái
  - Hành động (Sửa, Xóa, Chi tiết)

#### 3.1.5. Form Thêm/Sửa Mã Khuyến mãi
- **Thành phần**: Form với các trường
- **Trường dữ liệu**:
  - Mã khuyến mãi (input text)
  - Mô tả (textarea)
  - Loại giảm giá (select: phần trăm, số tiền cố định)
  - Giá trị giảm (input number)
  - Ngày bắt đầu (datepicker)
  - Ngày kết thúc (datepicker)
  - Số lần sử dụng tối đa (input number)
  - Số lần sử dụng tối đa cho mỗi người dùng (input number)
  - Điều kiện áp dụng (textarea)
  - Trạng thái (toggle/switch)

#### 3.1.6. Trang Quản lý Email Marketing
- **Thành phần**: Bảng dữ liệu (DataTable) và Form gửi email
- **Chức năng**:
  - Tạo mẫu email
  - Gửi email hàng loạt
  - Xem lịch sử gửi email
  - Theo dõi tỷ lệ mở email, click
- **Cột hiển thị cho lịch sử**:
  - ID
  - Tiêu đề email
  - Ngày gửi
  - Số lượng người nhận
  - Tỷ lệ mở
  - Tỷ lệ click
  - Hành động (Xem chi tiết)

#### 3.1.7. Trang Quản lý Thông báo
- **Thành phần**: Bảng dữ liệu (DataTable) và Form gửi thông báo
- **Chức năng**:
  - Tạo thông báo mới
  - Gửi thông báo đến người dùng
  - Xem lịch sử thông báo
- **Cột hiển thị cho lịch sử**:
  - ID
  - Tiêu đề thông báo
  - Nội dung
  - Ngày gửi
  - Số lượng người nhận
  - Tỷ lệ đọc
  - Hành động (Xem chi tiết)

#### 3.1.8. Trang Quản lý Affiliate
- **Thành phần**: Bảng dữ liệu (DataTable) cho đối tác và giao dịch
- **Chức năng**:
  - Quản lý đối tác tiếp thị
  - Theo dõi hoa hồng
  - Quản lý thanh toán
- **Cột hiển thị cho đối tác**:
  - ID
  - Tên đối tác
  - Email
  - Số điện thoại
  - Ngày tham gia
  - Tổng hoa hồng
  - Trạng thái
  - Hành động (Xem chi tiết, Vô hiệu hóa)

### 3.2. Giao diện Người dùng

#### 3.2.1. Trang Khuyến mãi
- **Thành phần**: Grid/List hiển thị khuyến mãi
- **Chức năng**:
  - Xem danh sách khuyến mãi đang hoạt động
  - Lọc theo loại khuyến mãi
  - Sử dụng mã khuyến mãi
- **Hiển thị cho mỗi khuyến mãi**:
  - Ảnh/Banner
  - Tiêu đề
  - Mô tả
  - Mã khuyến mãi
  - Thời gian hiệu lực
  - Nút "Sử dụng ngay"

#### 3.2.2. Trang Affiliate (cho người dùng)
- **Thành phần**: Dashboard và bảng dữ liệu
- **Chức năng**:
  - Xem thông tin chương trình tiếp thị liên kết
  - Lấy mã giới thiệu
  - Theo dõi hoa hồng
  - Yêu cầu thanh toán
- **Hiển thị**:
  - Tổng quan (tổng hoa hồng, số người đã giới thiệu)
  - Mã giới thiệu và link
  - Lịch sử giao dịch
  - Biểu đồ hoa hồng theo thời gian

## 4. Đề xuất Cấu trúc Component

### 4.1. Components chung
- **Layout**: AdminLayout và UserLayout
- **AuthGuard**: Bảo vệ các route yêu cầu đăng nhập
- **DataTable**: Component bảng dữ liệu với phân trang, sắp xếp, tìm kiếm
- **DateRangePicker**: Component chọn khoảng thời gian
- **Chart**: Component biểu đồ (line, bar, pie)
- **Modal**: Component modal cho các form thêm/sửa
- **Toast/Notification**: Hiển thị thông báo thành công/lỗi

### 4.2. Components cho Admin
- **MarketingDashboard**: Dashboard tổng quan marketing
- **CampaignList**: Danh sách chiến dịch
- **CampaignForm**: Form thêm/sửa chiến dịch
- **CampaignDetail**: Chi tiết chiến dịch
- **CouponList**: Danh sách mã khuyến mãi
- **CouponForm**: Form thêm/sửa mã khuyến mãi
- **CouponDetail**: Chi tiết mã khuyến mãi
- **EmailTemplateList**: Danh sách mẫu email
- **EmailTemplateForm**: Form thêm/sửa mẫu email
- **EmailCampaignForm**: Form gửi email hàng loạt
- **NotificationList**: Danh sách thông báo
- **NotificationForm**: Form gửi thông báo
- **AffiliatePartnerList**: Danh sách đối tác tiếp thị
- **AffiliateTransactionList**: Danh sách giao dịch tiếp thị liên kết

### 4.3. Components cho Người dùng
- **PromotionList**: Danh sách khuyến mãi
- **PromotionCard**: Card hiển thị thông tin khuyến mãi
- **CouponCard**: Card hiển thị mã khuyến mãi
- **AffiliateDashboard**: Dashboard tiếp thị liên kết cho người dùng
- **AffiliateReferralLink**: Component hiển thị và sao chép link giới thiệu
- **AffiliateTransactionHistory**: Lịch sử giao dịch tiếp thị liên kết

### 4.4. Đề xuất Routing

#### Admin Routes
```
/admin/marketing
  /dashboard             # Tổng quan marketing
  /campaigns             # Danh sách chiến dịch
    /create              # Thêm chiến dịch mới
    /:id                 # Chi tiết chiến dịch
    /:id/edit            # Sửa thông tin chiến dịch
  /coupons               # Danh sách mã khuyến mãi
    /create              # Thêm mã khuyến mãi mới
    /:id                 # Chi tiết mã khuyến mãi
    /:id/edit            # Sửa thông tin mã khuyến mãi
  /emails                # Quản lý email marketing
    /templates           # Danh sách mẫu email
    /templates/create    # Thêm mẫu email mới
    /templates/:id/edit  # Sửa mẫu email
    /campaigns           # Chiến dịch email
    /campaigns/create    # Tạo chiến dịch email mới
  /notifications         # Quản lý thông báo
    /create              # Tạo thông báo mới
  /affiliate             # Quản lý affiliate
    /partners            # Danh sách đối tác
    /transactions        # Danh sách giao dịch
```

#### User Routes
```
/promotions              # Danh sách khuyến mãi
  /:id                   # Chi tiết khuyến mãi
/affiliate               # Trang tiếp thị liên kết
  /dashboard             # Tổng quan
  /transactions          # Lịch sử giao dịch
  /withdraw              # Yêu cầu thanh toán
```

## 5. Đề xuất State Management

### 5.1. Redux/Context API Store
- **campaigns**: Danh sách chiến dịch, loading state, error state
- **coupons**: Danh sách mã khuyến mãi, loading state, error state
- **emails**: Danh sách mẫu email, chiến dịch email, loading state, error state
- **notifications**: Danh sách thông báo, loading state, error state
- **affiliate**: Thông tin tiếp thị liên kết, giao dịch, loading state, error state
- **ui**: Trạng thái UI (filters, search, pagination)

### 5.2. Actions/Reducers
- **campaigns**: fetchCampaigns, createCampaign, updateCampaign, deleteCampaign
- **coupons**: fetchCoupons, createCoupon, updateCoupon, deleteCoupon
- **emails**: fetchEmailTemplates, createEmailTemplate, updateEmailTemplate, deleteEmailTemplate, sendEmailCampaign
- **notifications**: fetchNotifications, createNotification, sendNotification
- **affiliate**: fetchAffiliatePartners, fetchAffiliateTransactions, updateAffiliatePartner

## 6. Đề xuất Công nghệ

- **Framework**: React
- **UI Library**: Material-UI hoặc Ant Design
- **State Management**: Redux Toolkit hoặc Context API
- **Form Handling**: Formik hoặc React Hook Form
- **Validation**: Yup
- **HTTP Client**: Axios
- **Charts**: Recharts hoặc Chart.js
- **Date Handling**: date-fns hoặc moment.js
- **Email Editor**: React Email Editor
- **Routing**: React Router
- **Internationalization**: i18next

## 7. Mockups

Dưới đây là đề xuất mockup cho một số màn hình chính:

### 7.1. Trang Tổng quan Marketing (Admin)
```
+----------------------------------+
| [Sidebar] | Header               |
|           |                      |
|           | [Marketing Dashboard]|
|           |                      |
|           | +------+ +------+ +------+ |
|           | | KPI1 | | KPI2 | | KPI3 | |
|           | +------+ +------+ +------+ |
|           |                      |
|           | +------------------+ |
|           | | Biểu đồ người    | |
|           | | dùng mới theo    | |
|           | | thời gian        | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Biểu đồ chuyển   | |
|           | | đổi từ các       | |
|           | | chiến dịch       | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Bảng xếp hạng    | |
|           | | chiến dịch       | |
|           | +------------------+ |
+----------------------------------+
```

### 7.2. Trang Quản lý Mã Khuyến mãi (Admin)
```
+----------------------------------+
| [Sidebar] | Header               |
|           | [Thêm mới] [Tìm kiếm]|
|           |                      |
|           | Lọc: [Trạng thái] [Loại] |
|           |                      |
|           | +------------------+ |
|           | | Danh sách mã     | |
|           | | khuyến mãi       | |
|           | | (DataTable)      | |
|           | |                  | |
|           | |                  | |
|           | |                  | |
|           | +------------------+ |
|           | [Pagination]         |
+----------------------------------+
```

### 7.3. Form Thêm/Sửa Mã Khuyến mãi (Admin)
```
+----------------------------------+
| [Sidebar] | Header               |
|           |                      |
|           | [Thêm mã khuyến mãi] |
|           |                      |
|           | +------------------+ |
|           | | Mã khuyến mãi    | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Mô tả            | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Loại giảm giá    | |
|           | | [Select]         | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Giá trị giảm     | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Ngày bắt đầu     | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Ngày kết thúc    | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Số lần sử dụng   | |
|           | | tối đa           | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Số lần sử dụng   | |
|           | | tối đa cho mỗi   | |
|           | | người dùng       | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Điều kiện áp dụng| |
|           | +------------------+ |
|           |                      |
|           | | Trạng thái [On/Off]|
|           | +------------------+ |
|           |                      |
|           | [Lưu] [Hủy]         |
+----------------------------------+
```

### 7.4. Trang Khuyến mãi (Người dùng)
```
+----------------------------------+
| [Header]                         |
| [Banner/Search]                  |
|                                  |
| [Filters/Categories]             |
|                                  |
| +--------+ +--------+ +--------+ |
| | Promo  | | Promo  | | Promo  | |
| | Card   | | Card   | | Card   | |
| |        | |        | |        | |
| | [Dùng] | | [Dùng] | | [Dùng] | |
| +--------+ +--------+ +--------+ |
|                                  |
| +--------+ +--------+ +--------+ |
| | Promo  | | Promo  | | Promo  | |
| | Card   | | Card   | | Card   | |
| |        | |        | |        | |
| | [Dùng] | | [Dùng] | | [Dùng] | |
| +--------+ +--------+ +--------+ |
|                                  |
| [Pagination]                     |
|                                  |
| [Footer]                         |
+----------------------------------+
```

### 7.5. Trang Affiliate Dashboard (Người dùng)
```
+----------------------------------+
| [Header]                         |
|                                  |
| [Affiliate Dashboard]            |
|                                  |
| +------+ +------+ +------+       |
| | Tổng | | Số   | | Hoa  |       |
| | hoa  | | người| | hồng |       |
| | hồng | | giới | | chờ  |       |
| +------+ +------+ +------+       |
|                                  |
| [Link giới thiệu của bạn]        |
| [Copy] [Chia sẻ]                 |
|                                  |
| +----------------------------+   |
| | Biểu đồ hoa hồng theo     |   |
| | thời gian                 |   |
| |                           |   |
| +----------------------------+   |
|                                  |
| [Lịch sử giao dịch]             |
| +----------------------------+   |
| | Bảng lịch sử giao dịch    |   |
| |                           |   |
| |                           |   |
| +----------------------------+   |
|                                  |
| [Footer]                         |
+----------------------------------+
```

## 8. Kết luận

Dựa trên phân tích cấu trúc thư mục backend, module Marketing cần một giao diện frontend đầy đủ để quản lý các hoạt động tiếp thị, chiến dịch quảng cáo, và các chương trình khuyến mãi. Giao diện đề xuất tập trung vào việc cung cấp công cụ quản lý hiệu quả cho admin và trải nghiệm người dùng tốt cho người dùng cuối.

Việc triển khai nên được thực hiện theo từng giai đoạn, bắt đầu từ các chức năng cơ bản như quản lý mã khuyến mãi, sau đó mở rộng đến chiến dịch marketing, email marketing và affiliate.
