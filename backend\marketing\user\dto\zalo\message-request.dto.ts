import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, ValidateIf } from 'class-validator';

/**
 * Enum cho loại tin nhắn
 */
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  TEMPLATE = 'template',
}

/**
 * DTO cho việc gửi tin nhắn
 */
export class MessageRequestDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Loại tin nhắn',
    enum: MessageType,
    example: MessageType.TEXT,
  })
  @IsEnum(MessageType)
  @IsNotEmpty()
  messageType: MessageType;

  @ApiProperty({
    description: 'Nội dung tin nhắn văn bản',
    example: 'Xin chào! Cảm ơn bạn đã quan tâm đến sản phẩm của chúng tôi.',
    required: false,
  })
  @ValidateIf(o => o.messageType === MessageType.TEXT)
  @IsString()
  @IsNotEmpty()
  message?: string;

  @ApiProperty({
    description: 'URL của hình ảnh',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @ValidateIf(o => o.messageType === MessageType.IMAGE)
  @IsString()
  @IsNotEmpty()
  imageUrl?: string;

  @ApiProperty({
    description: 'URL của file',
    example: 'https://example.com/document.pdf',
    required: false,
  })
  @ValidateIf(o => o.messageType === MessageType.FILE)
  @IsString()
  @IsNotEmpty()
  fileUrl?: string;

  @ApiProperty({
    description: 'ID của template',
    example: 'template123',
    required: false,
  })
  @ValidateIf(o => o.messageType === MessageType.TEMPLATE)
  @IsString()
  @IsNotEmpty()
  templateId?: string;

  @ApiProperty({
    description: 'Dữ liệu cho template',
    example: { name: 'Nguyễn Văn A', product: 'RedAI Pro' },
    required: false,
  })
  @ValidateIf(o => o.messageType === MessageType.TEMPLATE)
  @IsOptional()
  templateData?: Record<string, any>;
}
