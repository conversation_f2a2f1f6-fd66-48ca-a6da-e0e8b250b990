import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Employee, EmployeeRole } from '@modules/employee/entities';

/**
 * Repository cho quan hệ giữa Employee và Role
 */
@Injectable()
export class EmployeeHasRoleRepository {
  constructor(
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
  ) {}

  /**
   * Thêm vai trò cho nhân viên
   * @param employeeId ID của nhân viên
   * @param roleIds Danh sách ID của các vai trò
   * @returns Nhân viên đã được cập nhật với các vai trò mới
   */
  async assignRolesToEmployee(employeeId: number, roleIds: number[]): Promise<Employee> {
    // Tìm nhân viên theo ID và load các vai trò hiện tại
    const employee = await this.employeeRepository.findOne({
      where: { id: employeeId },
      relations: ['roles'],
    });

    if (!employee) {
      throw new Error(`Nhân viên với ID "${employeeId}" không tồn tại`);
    }

    // Tạo mảng các vai trò mới
    const roles = roleIds.map(id => {
      const role = new EmployeeRole();
      role.id = id;
      return role;
    });

    // Gán vai trò mới cho nhân viên
    employee.roles = roles;
    employee.updatedAt = Date.now();

    // Lưu thay đổi vào cơ sở dữ liệu
    return this.employeeRepository.save(employee);
  }

  /**
   * Kiểm tra nhân viên đã có vai trò chưa
   * @param employeeId ID của nhân viên
   * @param roleId ID của vai trò
   * @returns true nếu nhân viên đã có vai trò, ngược lại false
   */
  async hasRole(employeeId: number, roleId: number): Promise<boolean> {
    const employee = await this.employeeRepository.findOne({
      where: { id: employeeId },
      relations: ['roles'],
    });

    if (!employee) {
      return false;
    }

    return employee.roles.some(role => role.id === roleId);
  }

  /**
   * Lấy tất cả vai trò của nhân viên
   * @param employeeId ID của nhân viên
   * @returns Danh sách các vai trò của nhân viên
   */
  async findRolesByEmployeeId(employeeId: number): Promise<EmployeeRole[]> {
    const employee = await this.employeeRepository.findOne({
      where: { id: employeeId },
      relations: ['roles', 'roles.permissions'],
    });

    if (!employee) {
      throw new Error(`Nhân viên với ID "${employeeId}" không tồn tại`);
    }

    return employee.roles;
  }
}
