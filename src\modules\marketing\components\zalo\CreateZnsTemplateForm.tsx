import { useState } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Plus, Trash2, Info } from 'lucide-react';
import { Button } from '@/shared/components/common';
import { Input } from '@/shared/components/common';
import { Alert } from '@/shared/components/common';
import { FormItem } from '@/shared/components/common';
import { createZnsTemplateSchema, type CreateZnsTemplateFormData } from '../../schemas/zalo.schema';

interface CreateZnsTemplateFormProps {
  onSuccess?: () => void;
}

/**
 * Form tạo ZNS Template
 */
export function CreateZnsTemplateForm({ onSuccess }: CreateZnsTemplateFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<CreateZnsTemplateFormData>({
    resolver: zodResolver(createZnsTemplateSchema),
    defaultValues: {
      oaId: 0,
      name: '',
      content: '',
    },
  });

  // Quản lý params manually vì params là optional array
  const [params, setParams] = useState<string[]>([]);

  const onSubmit = async (data: CreateZnsTemplateFormData) => {
    try {
      setIsSubmitting(true);

      // Thêm params vào data trước khi submit
      const submitData = {
        ...data,
        params: params.length > 0 ? params : undefined,
      };

      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Creating ZNS template:', submitData);

      onSuccess?.();
    } catch (error) {
      console.error('Error creating template:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const addParameter = () => {
    setParams([...params, '']);
  };

  const removeParameter = (index: number) => {
    setParams(params.filter((_, i) => i !== index));
  };

  const updateParameter = (index: number, value: string) => {
    const newParams = [...params];
    newParams[index] = value;
    setParams(newParams);
  };



  return (
    <div className="space-y-6">
      {/* Instructions */}
      <Alert
        type="info"
        message="Hướng dẫn tạo ZNS Template"
        description="Template sẽ được gửi đến Zalo để duyệt trước khi sử dụng. Nội dung phải tuân thủ quy định của Zalo về ZNS. Sử dụng {param_name} để đánh dấu tham số động."
      />

      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* OA Selection */}
          <FormItem
            name="oaId"
            label="Chọn Official Account"
            helpText="Chọn OA để tạo template"
            required
          >
            <select className="w-full p-2 border rounded-md">
              <option value="">Chọn Official Account...</option>
              <option value="1">Shop ABC - OA001</option>
              <option value="2">Công ty XYZ - OA002</option>
              <option value="3">Dịch vụ 123 - OA003</option>
            </select>
          </FormItem>

          {/* Template Name */}
          <FormItem
            name="name"
            label="Tên Template"
            helpText="Tên mô tả cho template (chỉ sử dụng nội bộ)"
            required
          >
            <Input
              placeholder="Ví dụ: Xác nhận đơn hàng, Thông báo khuyến mãi..."
            />
          </FormItem>

          {/* Template Content */}
          <FormItem
            name="content"
            label="Nội dung Template"
            helpText="Nội dung tin nhắn ZNS. Sử dụng {param_name} cho tham số động"
            required
          >
            <textarea
              className="w-full p-3 border rounded-md min-h-[120px] resize-y"
              placeholder="Ví dụ: Xin chào {customer_name}, đơn hàng #{order_id} của bạn đã được xác nhận với tổng giá trị {total_amount} VNĐ. Cảm ơn bạn đã mua hàng!"
            />
          </FormItem>

          {/* Parameters */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Tham số động</h4>
                <p className="text-sm text-muted-foreground">
                  Định nghĩa các tham số sẽ được thay thế khi gửi tin nhắn
                </p>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addParameter}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Thêm tham số
              </Button>
            </div>

            {params.length === 0 ? (
              <div className="text-center py-8 border-2 border-dashed border-muted rounded-lg">
                <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Chưa có tham số nào</p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addParameter}
                  className="mt-2"
                >
                  Thêm tham số đầu tiên
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                {params.map((param, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                    <div className="flex-1">
                      <label className="text-sm font-medium">Tham số {index + 1}</label>
                      <Input
                        placeholder="Ví dụ: customer_name, order_id, total_amount..."
                        value={param}
                        onChange={(e) => updateParameter(index, e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeParameter(index)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Template Preview */}
          <div className="space-y-2">
            <h4 className="font-medium">Xem trước</h4>
            <div className="p-4 bg-muted rounded-lg">
              <div className="text-sm">
                {form.watch('content') || 'Nhập nội dung template để xem trước...'}
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onSuccess?.()}
              disabled={isSubmitting}
            >
              Hủy
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Tạo Template
            </Button>
          </div>
        </form>
      </FormProvider>

      {/* Guidelines */}
      <div className="border-t pt-4">
        <h4 className="text-sm font-medium mb-2">Quy định về ZNS Template</h4>
        <div className="text-sm text-muted-foreground space-y-1">
          <p>• Nội dung phải rõ ràng, không gây hiểu lầm</p>
          <p>• Không chứa nội dung quảng cáo trực tiếp</p>
          <p>• Phải có mục đích thông báo rõ ràng (OTP, xác nhận, nhắc nhở...)</p>
          <p>• Độ dài tối đa 2000 ký tự</p>
          <p>• Thời gian duyệt: 1-3 ngày làm việc</p>
        </div>
      </div>
    </div>
  );
}
