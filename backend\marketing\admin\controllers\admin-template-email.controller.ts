import { Body, Controller, Delete, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AdminTemplateEmailService } from '../services/admin-template-email.service';
import { AdminTemplateEmail } from '../entities/admin-template-email.entity';
import { Employee } from '@modules/employee/entities/employee.entity';
import { CategoryTemplateAutoEnum } from '@modules/email/interface/category-template-auto.enum';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { TemplateEmailQueryDto, TemplateEmailResponseDto } from '../dto/template-email';
import { PaginatedResponseDto } from '../dto/common';
import { ApiResponseDto as AppApiResponse } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý API liên quan đến template email bên admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_TEMPLATE_EMAIL)
@Controller('admin/template-email')
@UseGuards(JwtEmployeeGuard)
export class AdminTemplateEmailController {
  constructor(
    private readonly adminTemplateEmailService: AdminTemplateEmailService,
  ) {}

  /**
   * Lấy danh sách template email với phân trang và filter
   */
  @ApiOperation({ summary: 'Lấy danh sách template email với phân trang và filter' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách template email với phân trang',
    type: PaginatedResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/PaginatedResponseDto' },
        {
          properties: {
            data: {
              type: 'array',
              items: { $ref: '#/components/schemas/TemplateEmailResponseDto' }
            }
          }
        }
      ]
    }
  })
  @Get()
  async findAll(@Query() query: TemplateEmailQueryDto): Promise<AppApiResponse<PaginatedResponseDto<TemplateEmailResponseDto>>> {
    const result = await this.adminTemplateEmailService.findAll(query);
    return wrapResponse(result, 'Danh sách template email');
  }

  /**
   * Lấy tất cả template email (không phân trang)
   */
  @ApiOperation({ summary: 'Lấy tất cả template email (không phân trang)' })
  @ApiResponse({ status: 200, description: 'Danh sách template email' })
  @Get('all')
  async findAllTemplates(): Promise<AppApiResponse<AdminTemplateEmail[]>> {
    const result = await this.adminTemplateEmailService.findAllTemplates();
    return wrapResponse(result, 'Danh sách tất cả template email');
  }

  /**
   * Lấy template email theo ID
   */
  @ApiOperation({ summary: 'Lấy template email theo ID' })
  @ApiParam({ name: 'id', description: 'ID của template email' })
  @ApiResponse({ status: 200, description: 'Template email' })
  @ApiResponse({ status: 404, description: 'Template email không tồn tại' })
  @Get(':id')
  async findById(@Param('id') id: number): Promise<AppApiResponse<AdminTemplateEmail>> {
    const result = await this.adminTemplateEmailService.findById(id);
    return wrapResponse(result, 'Thông tin template email');
  }

  /**
   * Lấy template email theo category
   */
  @ApiOperation({ summary: 'Lấy template email theo category' })
  @ApiQuery({ name: 'category', description: 'Category của template email' })
  @ApiResponse({ status: 200, description: 'Template email' })
  @ApiResponse({ status: 404, description: 'Template email không tồn tại' })
  @Get('category/:category')
  async findByCategory(@Param('category') category: string): Promise<AppApiResponse<AdminTemplateEmail>> {
    const result = await this.adminTemplateEmailService.findByCategory(category);
    return wrapResponse(result, 'Template email theo category');
  }

  /**
   * Lấy template email theo category enum
   */
  @ApiOperation({ summary: 'Lấy template email theo category enum' })
  @ApiQuery({
    name: 'category',
    description: 'Category enum của template email',
    enum: CategoryTemplateAutoEnum
  })
  @ApiResponse({ status: 200, description: 'Template email' })
  @ApiResponse({ status: 404, description: 'Template email không tồn tại' })
  @Get('category-enum/:category')
  async findTemplateAutoByCategory(
    @Param('category') category: CategoryTemplateAutoEnum
  ): Promise<AppApiResponse<AdminTemplateEmail>> {
    const result = await this.adminTemplateEmailService.findTemplateAutoByCategory(category);
    return wrapResponse(result, 'Template email theo category enum');
  }

  /**
   * Tìm template email theo danh sách category
   */
  @ApiOperation({ summary: 'Tìm template email theo danh sách category' })
  @ApiQuery({
    name: 'categories',
    description: 'Danh sách category của template email, phân cách bởi dấu phẩy',
    type: String
  })
  @ApiResponse({ status: 200, description: 'Danh sách template email' })
  @Get('categories')
  async findByCategories(@Query('categories') categoriesStr: string): Promise<AppApiResponse<AdminTemplateEmail[]>> {
    const categories = categoriesStr.split(',').map(cat => cat.trim());
    const result = await this.adminTemplateEmailService.findByCategories(categories);
    return wrapResponse(result, 'Danh sách template email theo categories');
  }

  /**
   * Tạo mới template email
   */
  @ApiOperation({ summary: 'Tạo mới template email' })
  @ApiResponse({ status: 201, description: 'Template email đã được tạo' })
  @Post()
  async create(
    @Body() data: Partial<AdminTemplateEmail>,
    @CurrentEmployee() employee: Employee
  ): Promise<AppApiResponse<AdminTemplateEmail>> {
    const result = await this.adminTemplateEmailService.create(data, employee.id);
    return wrapResponse(result, 'Template email đã được tạo thành công');
  }

  /**
   * Cập nhật template email
   */
  @ApiOperation({ summary: 'Cập nhật template email' })
  @ApiParam({ name: 'id', description: 'ID của template email' })
  @ApiResponse({ status: 200, description: 'Template email đã được cập nhật' })
  @ApiResponse({ status: 404, description: 'Template email không tồn tại' })
  @Put(':id')
  async update(
    @Param('id') id: number,
    @Body() data: Partial<AdminTemplateEmail>,
    @CurrentEmployee() employee: Employee
  ): Promise<AppApiResponse<AdminTemplateEmail>> {
    const result = await this.adminTemplateEmailService.update(id, data, employee.id);
    return wrapResponse(result, 'Template email đã được cập nhật thành công');
  }

  /**
   * Xóa template email
   */
  @ApiOperation({ summary: 'Xóa template email' })
  @ApiParam({ name: 'id', description: 'ID của template email' })
  @ApiResponse({ status: 200, description: 'Template email đã được xóa' })
  @ApiResponse({ status: 404, description: 'Template email không tồn tại' })
  @Delete(':id')
  async delete(@Param('id') id: number): Promise<AppApiResponse<{ success: boolean }>> {
    const success = await this.adminTemplateEmailService.delete(id);
    return wrapResponse({ success }, 'Template email đã được xóa thành công');
  }
}
