import { ApiProperty } from '@nestjs/swagger';
import { CustomFieldType } from './create-custom-field.dto';

/**
 * DTO cho phản hồi thông tin trường tùy chỉnh
 */
export class CustomFieldResponseDto {
  /**
   * ID của trường tùy chỉnh
   * @example 1
   */
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 1,
  })
  id: number;

  /**
   * ID của audience
   * @example 1
   */
  @ApiProperty({
    description: 'ID của audience',
    example: 1,
  })
  audienceId: number;

  /**
   * Tên trường
   * @example "Địa chỉ"
   */
  @ApiProperty({
    description: 'Tên trường',
    example: 'Địa chỉ',
  })
  fieldName: string;

  /**
   * Giá trị trường
   * @example "Hà Nội, Việt Nam"
   */
  @ApiProperty({
    description: 'Gi<PERSON> trị trường',
    example: '<PERSON><PERSON>ộ<PERSON>, Vi<PERSON>t Nam',
  })
  fieldValue: any;

  /**
   * <PERSON>ể<PERSON> dữ liệu của trường
   * @example "text"
   */
  @ApiProperty({
    description: 'Kiểu dữ liệu của trường',
    enum: CustomFieldType,
    example: CustomFieldType.TEXT,
  })
  fieldType: CustomFieldType;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1619171200,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}
