import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Table,
  Chip,
  ActionMenu,
  ConfirmDeleteModal,
} from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { NotificationUtil } from '@/shared/utils/notification';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';

import {
  useUrls,
  useCreateUrl,
  useDeleteUrl,
  useDeleteMultipleUrls,
} from '@/modules/data/url/hooks/useUrlQuery';
import { UrlDto, FindAllUrlDto, CreateUrlDto, CrawlDto } from '@/modules/data/url/types/url.types';
import UrlForm from '@/modules/data/components/forms/UrlForm';
import { CrawlUrlForm } from '@/modules/data/components/forms';
import { CrawlUrlFormValues } from '@/modules/data/components/forms/CrawlUrlForm';
import useCrawlUrlWithQueue from '@/modules/data/hooks/useCrawlUrlWithQueue';

/**
 * Trang quản lý URL
 */
const UrlPage: React.FC<Record<string, never>> = () => {
  const { t } = useTranslation(['data', 'common']);

  // State cho form và modal
  const [selectedUrl, setSelectedUrl] = useState<UrlDto | null>(null);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [urlToDelete, setUrlToDelete] = useState<UrlDto | null>(null);
  const [isCrawling, setIsCrawling] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([
    { id: 'title', label: t('data:url.table.title', 'Tiêu đề'), visible: true },
    { id: 'type', label: t('data:url.table.type', 'Loại'), visible: true },
    { id: 'tags', label: t('data:url.table.tags', 'Tags'), visible: true },
    { id: 'createdAt', label: t('data:url.table.createdAt', 'Ngày tạo'), visible: true },
    { id: 'actions', label: t('common:actions', 'Hành động'), visible: true },
  ]);

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form crawl URL
  const {
    isVisible: isCrawlFormVisible,
    showForm: showCrawlForm,
    hideForm: hideCrawlForm,
  } = useSlideForm();

  // Định nghĩa các cột cho bảng
  // Xử lý chỉnh sửa
  const handleEdit = useCallback(
    (url: UrlDto) => {
      setSelectedUrl(url);
      showEditForm();
    },
    [showEditForm]
  );

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((url: UrlDto) => {
    setUrlToDelete(url);
    setShowDeleteConfirm(true);
  }, []);

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<UrlDto>[]>(
    () => [
      {
        key: 'title',
        title: t('data:url.table.title', 'Tiêu đề'),
        dataIndex: 'title',
        render: (_, record: UrlDto) => (
          <div className="flex items-center">
            <div className="flex-1 min-w-0">
              <Typography variant="body2" className="font-medium truncate">
                {record.title}
              </Typography>
              <Typography variant="caption" className="text-gray-500 truncate">
                {record.url}
              </Typography>
            </div>
          </div>
        ),
        sortable: true,
      },
      {
        key: 'type',
        title: t('data:url.table.type', 'Loại'),
        dataIndex: 'type',
        render: (value: unknown) => <span>{String(value || '-')}</span>,
        sortable: true,
      },
      {
        key: 'tags',
        title: t('data:url.table.tags', 'Tags'),
        dataIndex: 'tags',
        render: (value: unknown) => {
          const tags = value as string[];
          return (
            <div className="flex flex-wrap gap-1">
              {tags?.length > 0
                ? tags.map((tag, index) => (
                    <Chip key={index} size="sm">
                      {tag}
                    </Chip>
                  ))
                : '-'}
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('data:url.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        render: (value: unknown) => {
          // Kiểm tra và chuyển đổi timestamp thành Date
          if (value) {
            const timestamp = typeof value === 'string' ? parseInt(value, 10) : (value as number);
            if (!isNaN(timestamp)) {
              return new Date(timestamp).toLocaleDateString('vi-VN');
            }
          }
          return '-';
        },
        sortable: true,
      },
      {
        key: 'actions',
        title: t('common:actions', 'Hành động'),
        width: 80,
        render: (_, record: UrlDto) => (
          <ActionMenu
            items={[
              {
                id: 'edit',
                label: t('common:edit', 'Chỉnh sửa'),
                icon: 'edit',
                onClick: () => handleEdit(record),
              },
              {
                id: 'delete',
                label: t('common:delete', 'Xóa'),
                icon: 'trash',
                onClick: () => handleShowDeleteConfirm(record),
              },
            ]}
            menuTooltip={t('common:moreActions', 'Thêm hành động')}
            iconSize="sm"
            iconVariant="default"
            placement="bottom"
            menuWidth="180px"
            menuIcon="more-horizontal"
            showAllInMenu={true}
            preferRight={true}
          />
        ),
      },
    ],
    [t, handleEdit, handleShowDeleteConfirm]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [{ id: 'all', label: t('common:all', 'Tất cả'), icon: 'list', value: 'all' }],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): FindAllUrlDto => {
    const queryParams: FindAllUrlDto = {
      page: params.page,
      limit: params.pageSize,
      keyword: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<UrlDto, FindAllUrlDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Lấy hook từ useUrls
  const { data: urlsData, isLoading } = useUrls(dataTable.queryParams);

  // Mutation để tạo/cập nhật URL
  const { mutateAsync: createUrl } = useCreateUrl();

  // Mutation để xóa URL
  const { mutateAsync: deleteUrl } = useDeleteUrl();

  // Mutation để xóa nhiều URL cùng lúc
  const { mutateAsync: deleteMultipleUrls } = useDeleteMultipleUrls();

  // Hook để crawl URL với TaskQueue
  const { crawlUrlWithQueue } = useCrawlUrlWithQueue();

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setUrlToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!urlToDelete) return;

    try {
      await deleteUrl(urlToDelete.id);
      setShowDeleteConfirm(false);
      setUrlToDelete(null);
      NotificationUtil.success({
        message: t('data:url.deleteSuccess', 'Xóa URL thành công'),
      });
    } catch (error) {
      console.error('Error deleting URL:', error);
      NotificationUtil.error({
        message: t('data:url.deleteError', 'Lỗi khi xóa URL'),
      });
    }
  }, [urlToDelete, deleteUrl, t]);

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    // Kiểm tra cả số lượng mục đã chọn và dữ liệu có tồn tại không
    if (selectedRows.length === 0 || (urlsData?.items?.length ?? 0) === 0) {
      NotificationUtil.warning({
        message: t('data:url.selectToDelete', 'Vui lòng chọn ít nhất một URL để xóa'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRows.length, urlsData?.items?.length, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRows.length === 0) return;

    try {
      // Lưu số lượng URL đã chọn để hiển thị thông báo
      const deletedCount = selectedRows.length;

      // Gọi API xóa nhiều URL cùng lúc thay vì xóa từng URL một
      await deleteMultipleUrls({ ids: selectedRows });

      // Đảm bảo đặt lại selectedRows thành mảng rỗng
      setSelectedRows([]);
      setShowBulkDeleteConfirm(false);

      NotificationUtil.success({
        message: t('data:url.bulkDeleteSuccess', `Xóa ${deletedCount} URL đã chọn thành công`),
      });
    } catch (error) {
      console.error('Error deleting multiple URLs:', error);
      NotificationUtil.error({
        message: t('data:url.bulkDeleteError', 'Lỗi khi xóa các URL đã chọn'),
      });
    }
  }, [selectedRows, deleteMultipleUrls, t]);

  // Xử lý submit form thêm mới
  const handleSubmitForm = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        setIsSubmitting(true);

        // Chuẩn bị dữ liệu cho API
        const urlData: CreateUrlDto = {
          url: values.url as string,
          title: values.title as string,
          content: (values.description as string) || '',
          type: values.type as string,
          tags: values.tags ? (values.tags as string).split(',').map(tag => tag.trim()) : undefined,
        };

        if (selectedUrl?.id) {
          // Cập nhật URL
          await createUrl({
            ...urlData,
            id: selectedUrl.id, // id đã được thêm vào type CreateUrlDto
          });
          NotificationUtil.success({
            message: t('data:url.updateSuccess', 'Cập nhật URL thành công'),
          });
        } else {
          // Tạo mới URL
          await createUrl(urlData);
          NotificationUtil.success({
            message: t('data:url.createSuccess', 'Tạo URL mới thành công'),
          });
        }

        hideAddForm();
        hideEditForm();
        setSelectedUrl(null);
      } catch (error) {
        console.error('Error submitting URL form:', error);
        NotificationUtil.error({
          message: t('data:url.formError', 'Lỗi khi lưu URL'),
        });
      } finally {
        setIsSubmitting(false);
      }
    },
    [selectedUrl, createUrl, hideAddForm, hideEditForm, t]
  );

  // Xử lý submit form crawl URL
  const handleSubmitCrawlUrl = useCallback(
    async (values: CrawlUrlFormValues) => {
      try {
        setIsCrawling(true);

        // Chuẩn bị dữ liệu cho API
        const crawlData: CrawlDto = {
          url: values.url,
          depth: values.depth,
          maxUrls: values.maxUrls,
          ignoreRobotsTxt: values.ignoreRobotsTxt,
        };

        // Gọi API crawl URL với TaskQueue
        await crawlUrlWithQueue(crawlData);

        // Đóng form sau khi thêm vào queue
        hideCrawlForm();
      } catch (error) {
        console.error('Error crawling URL:', error);
        NotificationUtil.error({
          message: t('data:url.crawlError', 'Lỗi khi crawl URL'),
        });
      } finally {
        setIsCrawling(false);
      }
    },
    [crawlUrlWithQueue, hideCrawlForm, t]
  );

  // Xử lý hủy form crawl URL
  const handleCancelCrawlForm = useCallback(() => {
    setIsCrawling(false);
    hideCrawlForm();
  }, [hideCrawlForm]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: handleSortChangeWrapper,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {},
      t,
    });

  return (
    <div>
      <div className="space-y-4">
        <div>
          {/* Thêm MenuIconBar */}
          <MenuIconBar
            onSearch={dataTable.tableData.handleSearch}
            onAdd={showAddForm}
            items={[
              {
                id: 'all',
                label: t('common:all', 'Tất cả'),
                icon: 'list',
                onClick: () => '',
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
            additionalIcons={[
              {
                icon: 'crawl',
                tooltip: t('data:url.crawl', 'Crawl URL'),
                variant: 'primary',
                onClick: showCrawlForm,
              },
              {
                icon: 'trash',
                tooltip: t('common:bulkDelete', 'Xóa nhiều'),
                variant: 'primary',
                onClick: () => {
                  // Kiểm tra lại số lượng mục đã chọn và dữ liệu có tồn tại không
                  if (selectedRows.length > 0 && (urlsData?.items?.length ?? 0) > 0) {
                    handleShowBulkDeleteConfirm();
                  } else {
                    NotificationUtil.info({
                      message: t('data:url.selectToDelete', 'Vui lòng chọn ít nhất một URL để xóa'),
                      duration: 3000,
                    });
                  }
                },
                className: 'text-red-500',
                // Chỉ hiển thị khi có dữ liệu và có mục được chọn
                condition: selectedRows.length > 0 && (urlsData?.items?.length ?? 0) > 0,
              },
            ]}
          />

          {/* Hiển thị ActiveFilters */}
          <ActiveFilters
            searchTerm={dataTable.tableData.searchTerm}
            onClearSearch={handleClearSearch}
            filterValue={dataTable.filter.selectedValue}
            filterLabel={getFilterLabel()}
            onClearFilter={handleClearFilter}
            sortBy={dataTable.tableData.sortBy}
            sortDirection={dataTable.tableData.sortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        {/* SlideInForm cho form thêm mới */}
        <SlideInForm isVisible={isAddFormVisible}>
          <UrlForm onSubmit={handleSubmitForm} onCancel={hideAddForm} isSubmitting={isSubmitting} />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {selectedUrl && (
            <UrlForm
              initialValues={selectedUrl}
              onSubmit={handleSubmitForm}
              onCancel={hideEditForm}
              isSubmitting={isSubmitting}
            />
          )}
        </SlideInForm>

        {/* SlideInForm cho form crawl URL */}
        <SlideInForm isVisible={isCrawlFormVisible}>
          <CrawlUrlForm
            onSubmit={handleSubmitCrawlUrl}
            onCancel={handleCancelCrawlForm}
            isLoading={isCrawling}
          />
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table
            columns={columns.filter(col => {
              // Nếu không có visibleColumns, hiển thị tất cả
              if (visibleColumns.length === 0) return true;

              // Nếu "Tất cả" được chọn, hiển thị tất cả
              const allSelected = visibleColumns.find(vc => vc.id === 'all')?.visible;
              if (allSelected) return true;

              // Hiển thị cột nếu được chọn hoặc là cột actions
              return col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible;
            })}
            data={urlsData?.items || []}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={{
              current: urlsData?.meta.currentPage || 1,
              pageSize: dataTable.tableData.pageSize,
              total: urlsData?.meta.totalItems || 0,
              onChange: dataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
            rowSelection={{
              selectedRowKeys: selectedRows,
              onChange: keys => setSelectedRows(keys as string[]),
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t('data:url.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa URL này?')}
        itemName={urlToDelete?.title}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={
          selectedRows.length > 0
            ? t(
                'data:url.confirmBulkDeleteMessage',
                `Bạn có chắc chắn muốn xóa ${selectedRows.length} URL đã chọn?`
              )
            : t('data:url.noItemsSelected', 'Không có URL nào được chọn')
        }
      />
    </div>
  );
};

export default UrlPage;
