import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_audience trong cơ sở dữ liệu
 * Bảng khách hàng của admin
 */
@Entity('admin_audience')
export class AdminAudience {
  /**
   * ID của audience
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Email của khách hàng
   */
  @Column({ name: 'email', length: 255, nullable: true, comment: 'Email người dùng' })
  email: string;

  /**
   * Số điện thoại của khách hàng
   */
  @Column({ name: 'phone', length: 20, nullable: true, comment: 'Số điện thoại' })
  phone: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', comment: '<PERSON><PERSON><PERSON> tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: '<PERSON><PERSON><PERSON> cập nhật' })
  updatedAt: number;
}
