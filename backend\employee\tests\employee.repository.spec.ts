import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeRepository } from '../repositories/employee.repository';
import { Repository } from 'typeorm';
import { Employee } from '../entities/employee.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { NotFoundException } from '@nestjs/common';
import { CreateEmployeeDto } from '../dto/create-employee.dto';

describe('EmployeeRepository', () => {
  let repository: EmployeeRepository;
  let typeormRepository: jest.Mocked<Repository<Employee>>;

  const mockEmployee: Employee = {
    id: 1,
    fullName: 'Test Employee',
    email: '<EMAIL>',
    phoneNumber: '**********',
    password: 'hashedPassword',
    address: 'Test Address',
    createdAt: Date.now(),
    updatedAt: Date.now(),
    enable: true,
    avatar: 'avatar-url',
    roles: []
  };

  beforeEach(async () => {
    // Create mock implementation for TypeORM repository
    const typeormRepositoryMock = {
      findOne: jest.fn(),
      save: jest.fn(),
      create: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmployeeRepository,
        {
          provide: getRepositoryToken(Employee),
          useValue: typeormRepositoryMock,
        },
      ],
    }).compile();

    repository = module.get<EmployeeRepository>(EmployeeRepository);
    typeormRepository = module.get(getRepositoryToken(Employee)) as jest.Mocked<Repository<Employee>>;
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  describe('findById', () => {
    it('should find employee by id successfully', async () => {
      // Arrange
      const employeeId = 1;
      typeormRepository.findOne.mockResolvedValue(mockEmployee);

      // Act
      const result = await repository.findById(employeeId);

      // Assert
      expect(typeormRepository.findOne).toHaveBeenCalledWith({
        where: { id: employeeId },
      });
      expect(result).toEqual(mockEmployee);
    });

    it('should throw NotFoundException when employee is not found', async () => {
      // Arrange
      const employeeId = 999;
      typeormRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(repository.findById(employeeId)).rejects.toThrow(NotFoundException);
      expect(typeormRepository.findOne).toHaveBeenCalledWith({
        where: { id: employeeId },
      });
    });
  });

  describe('findByEmail', () => {
    it('should find employee by email successfully', async () => {
      // Arrange
      const email = '<EMAIL>';
      typeormRepository.findOne.mockResolvedValue(mockEmployee);

      // Act
      const result = await repository.findByEmail(email);

      // Assert
      expect(typeormRepository.findOne).toHaveBeenCalledWith({
        where: { email },
      });
      expect(result).toEqual(mockEmployee);
    });

    it('should return null when employee is not found by email', async () => {
      // Arrange
      const email = '<EMAIL>';
      typeormRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await repository.findByEmail(email);

      // Assert
      expect(typeormRepository.findOne).toHaveBeenCalledWith({
        where: { email },
      });
      expect(result).toBeNull();
    });
  });

  describe('createEmployee', () => {
    it('should create employee successfully', async () => {
      // Arrange
      const createEmployeeDto: CreateEmployeeDto = {
        fullName: 'New Employee',
        email: '<EMAIL>',
        phoneNumber: '0123456789',
        password: 'Password123!',
        address: 'New Address',
        roleIds: [1, 2]
      };
      const hashedPassword = 'hashedPassword';

      const newEmployee = {
        ...createEmployeeDto,
        password: hashedPassword,
        createdAt: expect.any(Number),
        enable: true,
      };

      typeormRepository.create.mockReturnValue(newEmployee as Employee);
      typeormRepository.save.mockResolvedValue({ ...newEmployee, id: 1 } as Employee);

      // Act
      const result = await repository.createEmployee(createEmployeeDto, hashedPassword);

      // Assert
      // Sử dụng expect.objectContaining để kiểm tra một phần của đối tượng
      expect(typeormRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          fullName: createEmployeeDto.fullName,
          email: createEmployeeDto.email,
          phoneNumber: createEmployeeDto.phoneNumber,
          password: hashedPassword,
          address: createEmployeeDto.address,
        })
      );
      expect(typeormRepository.save).toHaveBeenCalledWith(newEmployee);
      expect(result).toEqual({ ...newEmployee, id: 1 });
    });
  });

  describe('updateAvatar', () => {
    it('should update employee avatar successfully', async () => {
      // Arrange
      const employeeId = 1;
      const avatarKey = 'new-avatar-key';

      const updatedEmployee = { ...mockEmployee, avatar: avatarKey, updatedAt: expect.any(Number) };

      typeormRepository.findOne.mockResolvedValue(mockEmployee);
      typeormRepository.save.mockResolvedValue(updatedEmployee);

      // Act
      const result = await repository.updateAvatar(employeeId, avatarKey);

      // Assert
      expect(typeormRepository.findOne).toHaveBeenCalledWith({
        where: { id: employeeId },
      });
      expect(typeormRepository.save).toHaveBeenCalledWith({
        ...mockEmployee,
        avatar: avatarKey,
        updatedAt: expect.any(Number),
      });
      expect(result).toEqual(updatedEmployee);
    });

    it('should throw NotFoundException when employee is not found', async () => {
      // Arrange
      const employeeId = 999;
      const avatarKey = 'new-avatar-key';

      typeormRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(repository.updateAvatar(employeeId, avatarKey)).rejects.toThrow(NotFoundException);
      expect(typeormRepository.findOne).toHaveBeenCalledWith({
        where: { id: employeeId },
      });
    });
  });

  describe('changePassword', () => {
    it('should change employee password successfully', async () => {
      // Arrange
      const employeeId = 1;
      const hashedPassword = 'new-hashed-password';

      const updatedEmployee = { ...mockEmployee, password: hashedPassword, updatedAt: expect.any(Number) };

      typeormRepository.findOne.mockResolvedValue(mockEmployee);
      typeormRepository.save.mockResolvedValue(updatedEmployee);

      // Act
      const result = await repository.changePassword(employeeId, hashedPassword);

      // Assert
      expect(typeormRepository.findOne).toHaveBeenCalledWith({
        where: { id: employeeId },
      });
      expect(typeormRepository.save).toHaveBeenCalledWith({
        ...mockEmployee,
        password: hashedPassword,
        updatedAt: expect.any(Number),
      });
      expect(result).toEqual(updatedEmployee);
    });

    it('should throw NotFoundException when employee is not found', async () => {
      // Arrange
      const employeeId = 999;
      const hashedPassword = 'new-hashed-password';

      typeormRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(repository.changePassword(employeeId, hashedPassword)).rejects.toThrow(NotFoundException);
      expect(typeormRepository.findOne).toHaveBeenCalledWith({
        where: { id: employeeId },
      });
    });
  });
});
