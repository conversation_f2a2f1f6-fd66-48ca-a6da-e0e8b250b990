import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thống kê segment
 */
export class SegmentStatsDto {
  /**
   * ID của segment
   * @example 1
   */
  @ApiProperty({
    description: 'ID của segment',
    example: 1,
  })
  segmentId: number;

  /**
   * Tên segment
   * @example "Khách hàng tiềm năng"
   */
  @ApiProperty({
    description: 'Tên segment',
    example: 'Khách hàng tiềm năng',
  })
  segmentName: string;

  /**
   * Tổng số khách hàng trong segment
   * @example 150
   */
  @ApiProperty({
    description: 'Tổng số khách hàng trong segment',
    example: 150,
  })
  totalAudiences: number;

  /**
   * Tỷ lệ khách hàng trong segment so với tổng số khách hàng
   * @example 0.25
   */
  @ApiProperty({
    description: 'Tỷ lệ khách hàng trong segment so với tổng số khách hàng',
    example: 0.25,
  })
  percentageOfTotal: number;

  /**
   * Thời gian cập nhật thống kê (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật thống kê (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}
