import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Textarea, Select, FormItem } from '@/shared/components/common';
import { ToolDetail } from '@/modules/admin/tool/types/tool.types';
import JsonEditor from '@/modules/admin/tool/components/JsonEditor';

interface IntegrationFormProps {
  tool: ToolDetail;
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  isLoading?: boolean;
  readOnly?: boolean;
}

interface JsonSchema {
  type: string;
  title?: string;
  description?: string;
  format?: string;
  enum?: string[];
  properties?: Record<string, JsonSchema>;
  required?: string[];
}

interface ToolParameters {
  properties?: Record<string, JsonSchema>;
  required?: string[];
}

const IntegrationForm: React.FC<IntegrationFormProps> = ({
  tool,
  onSubmit,
  onCancel,
  isLoading = false,
  readOnly = false,
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<Record<string, unknown>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    const parameters = tool.defaultVersion?.parameters as ToolParameters;
    const requiredFields = parameters?.required || [];

    requiredFields.forEach((field: string) => {
      if (!formData[field]) {
        newErrors[field] = t('validation.required', 'This field is required');
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const renderField = (key: string, schema: JsonSchema) => {
    if (schema.type === 'object') {
      return (
        <JsonEditor
          value={formData[key] as Record<string, unknown> || {}}
          onChange={value => handleInputChange(key, value)}
          placeholder={schema.description || `Enter ${schema.title || key}`}
          disabled={readOnly || isLoading}
          minHeight={150}
        />
      );
    }
    if (schema.type === 'array') {
      return (
        <JsonEditor
          value={formData[key] as Record<string, unknown> || {}}
          onChange={value => handleInputChange(key, value)}
          placeholder={schema.description || `Enter ${schema.title || key}`}
          disabled={readOnly || isLoading}
          minHeight={150}
        />
      );
    }
    if (schema.enum) {
      return (
        <Select
          value={formData[key] as string}
          onChange={value => handleInputChange(key, value)}
          options={schema.enum.map(option => ({
            value: option,
            label: option,
          }))}
          disabled={readOnly || isLoading}
          placeholder={schema.description || `Select ${schema.title || key}`}
        />
      );
    }
    if (schema.type === 'string' && schema.format === 'multiline') {
      return (
        <Textarea
          value={(formData[key] as string) || ''}
          onChange={e => handleInputChange(key, e.target.value)}
          placeholder={schema.description || `Enter ${schema.title || key}`}
          disabled={readOnly || isLoading}
          rows={4}
        />
      );
    }
    return (
      <Input
        type={schema.type === 'number' ? 'number' : 'text'}
        value={(formData[key] as string) || ''}
        onChange={e => handleInputChange(key, e.target.value)}
        placeholder={schema.description || `Enter ${schema.title || key}`}
        disabled={readOnly || isLoading}
      />
    );
  };

  const parameters = tool.defaultVersion?.parameters as ToolParameters;
  const properties = parameters?.properties || {};

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <Typography variant="h6">
          {t('tools.integration.title', 'Tool Integration')}
        </Typography>

        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <Typography variant="subtitle1" className="mb-2">
            {tool.defaultVersion?.toolName || tool.name}
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
            {tool.defaultVersion?.toolDescription || tool.description}
          </Typography>
        </div>

        <div className="space-y-4">
          {Object.entries(properties).map(([key, schema]) => (
            <FormItem
              key={key}
              label={schema.title || key}
              helpText={errors[key]}
              required={parameters?.required?.includes(key)}
            >
              {renderField(key, schema)}
              {schema.description && (
                <Typography variant="caption" className="text-gray-500 dark:text-gray-400 mt-1">
                  {schema.description}
                </Typography>
              )}
            </FormItem>
          ))}
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button variant="outline" onClick={onCancel} disabled={isLoading}>
          {t('common.cancel', 'Cancel')}
        </Button>
        {!readOnly && (
          <Button type="submit" variant="primary" disabled={isLoading}>
            {isLoading
              ? t('tools.integration.submitting', 'Submitting...')
              : t('tools.integration.submit', 'Submit')}
          </Button>
        )}
      </div>
    </form>
  );
};

export default IntegrationForm;
