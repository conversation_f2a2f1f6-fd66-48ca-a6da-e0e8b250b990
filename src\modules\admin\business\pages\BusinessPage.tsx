import React from 'react';
import { ResponsiveGrid } from '@/shared/components/common';
import { ModuleCard } from '@/modules/components/card';

/**
 * Trang tổng quan về module Business cho Admin
 */
const BusinessPage: React.FC = () => {
  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Quản lý sản phẩm người dùng */}
        <ModuleCard
          title="Quản lý sản phẩm"
          description="Quản lý và theo dõi sản phẩm của người dùng"
          icon="box"
          linkTo="/admin/business/product"
        />

        {/* Quản lý chuyển đổi */}
        <ModuleCard
          title="Quản lý chuyển đổi"
          description="<PERSON> dõi và quản lý các bản ghi chuyển đổi"
          icon="refresh-cw"
          linkTo="/admin/business/conversion"
        />

        {/* Quản lý đơn hàng */}
        <ModuleCard
          title="Quản lý đơn hàng"
          description="Quản lý và theo dõi đơn hàng của người dùng"
          icon="shopping-cart"
          linkTo="/admin/business/order"
        />

        {/* Quản lý kho */}
        <ModuleCard
          title="Quản lý kho"
          description="Quản lý kho vật lý và kho ảo của người dùng"
          icon="package"
          linkTo="/admin/business/warehouse"
        />

        {/* Trường tùy chỉnh */}
        <ModuleCard
          title="Trường tùy chỉnh"
          description="Quản lý các trường tùy chỉnh của hệ thống"
          icon="database"
          linkTo="/admin/business/custom-field"
        />

        {/* Trường tùy chỉnh kho */}
        <ModuleCard
          title="Trường tùy chỉnh kho"
          description="Quản lý các trường tùy chỉnh của kho"
          icon="settings"
          linkTo="/admin/business/warehouse-custom-field"
        />

        {/* File quản lý */}
        <ModuleCard
          title="Quản lý file"
          description="Quản lý file và tài liệu của hệ thống"
          icon="file"
          linkTo="/admin/business/file"
        />

        {/* Thư mục */}
        <ModuleCard
          title="Quản lý thư mục"
          description="Quản lý cấu trúc thư mục của hệ thống"
          icon="folder"
          linkTo="/admin/business/folder"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default BusinessPage;
