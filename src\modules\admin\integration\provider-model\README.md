# Provider Model Module

<PERSON>u<PERSON>n lý các nhà cung cấp AI và cấu hình API key trong hệ thống admin.

## Cấu trúc

```
provider-model/
├── types/           # TypeScript interfaces và types
├── schemas/         # Zod validation schemas
├── services/        # API services
├── hooks/           # React Query hooks
└── README.md        # Tài liệu này
```

## Tính năng

- ✅ Quản lý danh sách provider models
- ✅ Tạo mới provider model
- ✅ Chỉnh sửa provider model
- ✅ Xem chi tiết provider model
- ✅ Xóa provider model
- ✅ Tìm kiếm và phân trang
- ✅ Đa ngôn ngữ (VI/EN/ZH)
- ✅ Form validation với Zod
- ✅ API integration với React Query

## Các Provider được hỗ trợ

- OpenAI
- Anthropic
- Google
- Meta
- DeepSeek
- XAI

## API Endpoints

- `GET /admin/provider-model` - L<PERSON>y danh sách provider models
- `GET /admin/provider-model/:id` - <PERSON><PERSON><PERSON> chi tiết provider model
- `POST /admin/provider-model` - <PERSON><PERSON><PERSON> mới provider model
- `PATCH /admin/provider-model/:id` - Cập nhật provider model
- `DELETE /admin/provider-model/:id` - Xóa provider model

## Sử dụng

### Import hooks

```typescript
import {
  useProviderModels,
  useProviderModel,
  useCreateProviderModel,
  useUpdateProviderModel,
  useDeleteProviderModel,
} from '@/modules/admin/integration/provider-model/hooks';
```

### Import types

```typescript
import {
  ProviderModel,
  CreateProviderModelDto,
  UpdateProviderModelDto,
} from '@/modules/admin/integration/provider-model/types';
```

### Sử dụng form

```typescript
import ProviderModelForm from '@/modules/admin/integration/components/ProviderModelForm';

<ProviderModelForm
  initialData={providerModel}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
  isSubmitting={isSubmitting}
  readOnly={false}
/>
```

## Routes

- `/admin/integration/provider-model` - Trang quản lý provider models

## Internationalization

Các key translation được định nghĩa trong:
- `src/modules/admin/integration/locales/vi.json`
- `src/modules/admin/integration/locales/en.json`
- `src/modules/admin/integration/locales/zh.json`

Namespace: `admin:integration.providerModel`
