import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeleteResult, FindManyOptions, FindOneOptions, FindOptionsWhere, Repository } from 'typeorm';
import { UserCampaign } from '../entities/user-campaign.entity';

/**
 * Repository cho UserCampaign
 */
@Injectable()
export class UserCampaignRepository {
  constructor(
    @InjectRepository(UserCampaign)
    private readonly repository: Repository<UserCampaign>,
  ) {}

  /**
   * Tìm kiếm nhiều campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách campaign
   */
  async find(options?: FindManyOptions<UserCampaign>): Promise<UserCampaign[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Campaign hoặc null
   */
  async findOne(options?: FindOneOptions<UserCampaign>): Promise<UserCampaign | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Đếm số lượng campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng campaign
   */
  async count(options?: FindManyOptions<UserCampaign>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }

  /**
   * Lưu campaign
   * @param campaign Campaign cần lưu
   * @returns Campaign đã lưu
   */
  async save(campaign: UserCampaign): Promise<UserCampaign>;
  async save(campaign: UserCampaign[]): Promise<UserCampaign[]>;
  async save(campaign: UserCampaign | UserCampaign[]): Promise<UserCampaign | UserCampaign[]> {
    return this.repository.save(campaign as any);
  }

  /**
   * Xóa campaign
   * @param criteria Điều kiện xóa
   * @returns Kết quả xóa
   */
  async delete(criteria: string | number | string[] | number[] | FindOptionsWhere<UserCampaign>): Promise<DeleteResult> {
    return this.repository.delete(criteria);
  }

  /**
   * Xóa campaign
   * @param campaign Campaign cần xóa
   * @returns Campaign đã xóa
   */
  async remove(campaign: UserCampaign): Promise<UserCampaign>;
  async remove(campaign: UserCampaign[]): Promise<UserCampaign[]>;
  async remove(campaign: UserCampaign | UserCampaign[]): Promise<UserCampaign | UserCampaign[]> {
    return this.repository.remove(campaign as any);
  }
}
