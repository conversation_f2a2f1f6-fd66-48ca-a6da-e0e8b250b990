import { ApiProperty } from '@nestjs/swagger';
import { CustomFieldResponseDto } from './custom-field-response.dto';
import { TagResponseDto } from '../tag/tag-response.dto';

/**
 * DTO cho phản hồi thông tin audience
 */
export class AudienceResponseDto {
  /**
   * ID của audience
   * @example 1
   */
  @ApiProperty({
    description: 'ID của audience',
    example: 1,
  })
  id: number;

  /**
   * Email của khách hàng
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Email của khách hàng',
    example: '<EMAIL>',
  })
  email: string;

  /**
   * Số điện thoại của khách hàng
   * @example "+84912345678"
   */
  @ApiProperty({
    description: 'Số điện thoại của khách hàng',
    example: '+84912345678',
    nullable: true,
  })
  phone: string | null;

  /**
   * <PERSON>h sách các trường tùy chỉnh
   */
  @ApiProperty({
    description: 'Danh sách các trường tùy chỉnh',
    type: [CustomFieldResponseDto],
  })
  customFields: CustomFieldResponseDto[];

  /**
   * Danh sách các tag
   */
  @ApiProperty({
    description: 'Danh sách các tag',
    type: [TagResponseDto],
  })
  tags: TagResponseDto[];

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1619171200,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}
