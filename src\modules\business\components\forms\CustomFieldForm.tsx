import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Toggle,
  Card,
  Textarea,
  Chip,
  ConditionalField,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { z } from 'zod';
import { useCreateCustomField, useUpdateCustomField } from '../../hooks/useCustomFieldQuery';
import { CustomFieldDetail, CreateCustomFieldData } from '../../services/custom-field.service';
import { ConditionType } from '@/shared/hooks/useFieldCondition';

interface CustomFieldFormProps {
  onSubmit: () => void;
  onCancel: () => void;
  initialData?: CustomFieldDetail;
  isSubmitting?: boolean;
}

// 30 mẫu pattern phổ biến
const COMMON_PATTERNS = [
  { label: 'Email', value: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$' },
  { label: 'Số điện thoại VN', value: '^(\\+84|0)[0-9]{9,10}$' },
  { label: 'Số điện thoại quốc tế', value: '^\\+[1-9]\\d{1,14}$' },
  { label: 'Mã bưu chính VN', value: '^[0-9]{5,6}$' },
  { label: 'Chỉ chữ cái', value: '^[a-zA-Z]+$' },
  { label: 'Chỉ số', value: '^[0-9]+$' },
  { label: 'Chữ và số', value: '^[a-zA-Z0-9]+$' },
  { label: 'Không có ký tự đặc biệt', value: '^[a-zA-Z0-9\\s]+$' },
  { label: 'URL', value: '^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$' },
  { label: 'IPv4', value: '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$' },
  { label: 'Mật khẩu mạnh', value: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$' },
  { label: 'Tên người (có dấu)', value: '^[a-zA-ZÀ-ỹ\\s]+$' },
  { label: 'Mã sinh viên', value: '^[A-Z]{2}[0-9]{6}$' },
  { label: 'CMND/CCCD', value: '^[0-9]{9,12}$' },
  { label: 'Mã số thuế', value: '^[0-9]{10,13}$' },
  { label: 'Ngày (dd/mm/yyyy)', value: '^(0[1-9]|[12][0-9]|3[01])\\/(0[1-9]|1[012])\\/(19|20)\\d\\d$' },
  { label: 'Giờ (hh:mm)', value: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' },
  { label: 'Hex color', value: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$' },
  { label: 'Base64', value: '^[A-Za-z0-9+\\/]*={0,2}$' },
  { label: 'UUID', value: '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$' },
  { label: 'Tên file', value: '^[^<>:"/\\\\|?*]+\\.[a-zA-Z0-9]+$' },
  { label: 'Slug URL', value: '^[a-z0-9]+(?:-[a-z0-9]+)*$' },
  { label: 'Tên biến', value: '^[a-zA-Z_$][a-zA-Z0-9_$]*$' },
  { label: 'Số thẻ tín dụng', value: '^[0-9]{13,19}$' },
  { label: 'Mã QR', value: '^[A-Z0-9 $%*+\\-./:]+$' },
  { label: 'Tọa độ GPS', value: '^-?([1-8]?[1-9]|[1-9]0)\\.{1}\\d{1,6}$' },
  { label: 'Mã màu RGB', value: '^rgb\\(([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5]),\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5]),\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\\)$' },
  { label: 'Tên miền', value: '^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$' },
  { label: 'Số thập phân', value: '^\\d+(\\.\\d{1,2})?$' },
  { label: 'Mã vạch', value: '^[0-9]{8,14}$' },
];

/**
 * Form tạo và chỉnh sửa trường tùy chỉnh
 */
const CustomFieldForm: React.FC<CustomFieldFormProps> = ({
  onSubmit,
  onCancel,
  initialData,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const { mutateAsync: createCustomField, isPending: isCreating } = useCreateCustomField();
  const { mutateAsync: updateCustomField, isPending: isUpdating } = useUpdateCustomField();

  // State cho tags
  const [tempTags, setTempTags] = useState<Record<string, string[]>>({});
  const formRef = useRef<FormRef<Record<string, unknown>> | null>(null);

  // Custom validation cho tags
  const validateTags = () => {
    const labelTags = tempTags['labels'] || [];
    return labelTags.length > 0; // Có ít nhất 1 tag
  };

  // Schema cho form
  const customFieldSchema = z.object({
    id: z.string().min(1, t('business:customField.form.idRequired')),
    component: z.string().min(1, t('business:customField.form.componentRequired')),
    label: z.string().optional(), // Tag không bắt buộc trong schema vì validate riêng
    type: z.string().min(1, t('business:customField.form.typeRequired')),
    required: z.boolean().optional(),
    placeholder: z.string().optional(),
    defaultValue: z.string().optional(),
    description: z.string().optional(),
    validation: z.object({
      minLength: z.string().optional(),
      maxLength: z.string().optional(),
      pattern: z.string().optional(),
    }).optional(),
    options: z.string().optional(),
  }).refine(() => validateTags(), {
    message: "Vui lòng thêm ít nhất một nhãn",
    path: ["label"], // Gán lỗi cho trường label
  });

  // Giá trị mặc định cho form
  const defaultValues = {
    id: '',
    component: 'input',
    label: '',
    type: 'text',
    required: false,
    placeholder: '',
    defaultValue: '',
    description: '',
    validation: {
      minLength: '',
      maxLength: '',
      pattern: '',
    },
    options: '',
  };

  // Định nghĩa kiểu dữ liệu cho form values
  type CustomFieldFormValues = z.infer<typeof customFieldSchema>;

  // Xử lý khi submit form
  const handleSubmit = async (values: CustomFieldFormValues) => {
    try {
      // Lấy tags từ tempTags
      const labelTags = tempTags['labels'] || [];
      const finalLabel = labelTags.length > 0 ? labelTags.join(', ') : String(values.label);

      // Chuẩn bị dữ liệu cho API
      const formData: CreateCustomFieldData = {
        component: String(values.component),
        config: {
          id: String(values.id),
          label: finalLabel,
          type: String(values.type),
          required: Boolean(values.required),
          placeholder: values.placeholder ? String(values.placeholder) : undefined,
          defaultValue: values.defaultValue ? String(values.defaultValue) : undefined,
          description: values.description ? String(values.description) : undefined,
          validation: {
            minLength: values.validation && values.validation.minLength ? Number(values.validation.minLength) : undefined,
            maxLength: values.validation && values.validation.maxLength ? Number(values.validation.maxLength) : undefined,
            pattern: values.validation && values.validation.pattern ? String(values.validation.pattern) : undefined,
          },
        },
      };

      // Thêm options nếu có
      if (values.options && ['select', 'radio', 'checkbox', 'multi-select'].includes(String(values.component))) {
        try {
          // Parse options từ chuỗi JSON hoặc từ danh sách các giá trị ngăn cách bởi dấu phẩy
          const optionsString = String(values.options);
          const optionsArray = optionsString.includes('{')
            ? JSON.parse(optionsString)
            : optionsString.split(',').map(option => ({
                label: option.trim(),
                value: option.trim().toLowerCase().replace(/\s+/g, '_'),
              }));

          formData.config.options = optionsArray;
        } catch (error) {
          console.error('Error parsing options:', error);
        }
      }

      if (initialData) {
        // Cập nhật trường tùy chỉnh
        await updateCustomField({
          id: initialData.id,
          data: formData,
        });
      } else {
        // Tạo trường tùy chỉnh mới
        await createCustomField(formData);
      }

      // Gọi callback onSubmit
      onSubmit();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  // Chuẩn bị giá trị mặc định từ dữ liệu ban đầu
  const getInitialValues = () => {
    if (!initialData) return defaultValues;

    // Lấy dữ liệu validation từ configJson
    const validation = initialData.configJson?.validation as Record<string, unknown> | undefined;

    // Load tags từ label nếu có
    if (initialData.label && initialData.label.includes(',')) {
      const labelTags = initialData.label.split(',').map(tag => tag.trim());
      setTempTags(prev => ({
        ...prev,
        labels: labelTags,
      }));
    }

    return {
      id: initialData.configJson?.id as string || '',
      component: initialData.component,
      label: initialData.label,
      type: initialData.type,
      required: initialData.required,
      placeholder: initialData.configJson?.placeholder as string || '',
      defaultValue: initialData.configJson?.defaultValue as string || '',
      description: initialData.configJson?.description as string || '',
      validation: {
        minLength: validation?.minLength ? String(validation.minLength) : '',
        maxLength: validation?.maxLength ? String(validation.maxLength) : '',
        pattern: validation?.pattern ? String(validation.pattern) : '',
      },
      options: initialData.configJson?.options
        ? JSON.stringify(initialData.configJson.options)
        : '',
    };
  };

  return (
    <Card title={initialData ? t('business:customField.edit') : t('business:customField.add')}>
      <Form
        ref={formRef}
        schema={customFieldSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        defaultValues={getInitialValues()}
        className="p-4 space-y-4"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem
            name="type"
            label={t('business:customField.type')}
            required
          >
            <Select
              fullWidth
              options={[
                { value: 'text', label: t('business:customField.types.text') },
                { value: 'number', label: t('business:customField.types.number') },
                { value: 'boolean', label: t('business:customField.types.boolean') },
                { value: 'date', label: t('business:customField.types.date') },
                { value: 'object', label: t('business:customField.types.object') },
                { value: 'array', label: t('business:customField.types.array') },
              ]}
            />
          </FormItem>

          <ConditionalField
            condition={{
              field: 'type',
              type: ConditionType.IN,
              value: ['text', 'number', 'boolean', 'date'],
            }}
          >
            <FormItem
              name="component"
              label={t('business:customField.component')}
              required
            >
              <Select
                fullWidth
                options={[
                  { value: 'input', label: t('business:customField.components.input') },
                  { value: 'textarea', label: t('business:customField.components.textarea') },
                  { value: 'select', label: t('business:customField.components.select') },
                  { value: 'checkbox', label: t('business:customField.components.checkbox') },
                  { value: 'radio', label: t('business:customField.components.radio') },
                  { value: 'date', label: t('business:customField.components.date') },
                  { value: 'number', label: t('business:customField.components.number') },
                  { value: 'file', label: t('business:customField.components.file') },
                  { value: 'multi-select', label: t('business:customField.components.multiSelect') },
                ]}
              />
            </FormItem>
          </ConditionalField>
        </div>

        <FormItem
          name="id"
          label="Tên trường định danh"
          required
        >
          <Input
            fullWidth
            placeholder="text-input-001"
            pattern="^[a-zA-Z0-9_-]+$"
          />
        </FormItem>

        <FormItem name="label" label={t('business:customField.label')} required>
          <div className="space-y-2">
            <Input
              fullWidth
              placeholder="Nhập nhãn và nhấn Enter"
              onKeyDown={e => {
                if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                  e.preventDefault();

                  // Lấy tags hiện tại từ state
                  const fieldId = 'labels'; // ID cố định cho trường labels

                  // Lấy tags hiện tại hoặc tạo mảng rỗng
                  const currentTempTags = tempTags[fieldId] || [];
                  const newTag = e.currentTarget.value.trim();

                  // Thêm tag mới nếu chưa tồn tại
                  if (!currentTempTags.includes(newTag)) {
                    setTempTags(prev => ({
                      ...prev,
                      [fieldId]: [...currentTempTags, newTag],
                    }));
                  }

                  e.currentTarget.value = '';
                }
              }}
            />
            <div className="flex flex-wrap gap-1 mt-2">
              {(() => {
                const fieldId = 'labels';
                const currentTempTags = tempTags[fieldId] || [];
                return currentTempTags.map((tag, tagIndex) => (
                  <Chip
                    key={`${fieldId}-${tagIndex}-${tag}`}
                    size="sm"
                    closable
                    onClose={() => {
                      setTempTags(prev => ({
                        ...prev,
                        [fieldId]: prev[fieldId].filter(t => t !== tag),
                      }));
                    }}
                  >
                    {tag}
                  </Chip>
                ));
              })()}
            </div>
            {/* Hiển thị số lượng tags */}
            <div className="text-xs text-gray-500">
              {tempTags['labels']?.length || 0} nhãn đã thêm
            </div>
          </div>
        </FormItem>

        <FormItem
          name="description"
          label={t('business:customField.form.description')}
        >
          <Textarea
            fullWidth
            rows={3}
            placeholder={t('business:customField.form.descriptionPlaceholder')}
          />
        </FormItem>

        <FormItem
          name="placeholder"
          label={t('business:customField.form.placeholder')}
        >
          <Input fullWidth placeholder={t('business:customField.form.placeholderPlaceholder')} />
        </FormItem>

        <FormItem
          name="defaultValue"
          label={t('business:customField.form.defaultValue')}
        >
          <Input fullWidth placeholder={t('business:customField.form.defaultValuePlaceholder')} />
        </FormItem>

        <FormItem
          name="options"
          label={t('business:customField.form.options')}
        >
          <Textarea
            fullWidth
            rows={3}
            placeholder={t('business:customField.form.optionsPlaceholder')}
          />
        </FormItem>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <ConditionalField
            condition={{
              field: 'type',
              type: ConditionType.IN,
              value: ['text', 'number'],
            }}
          >
            <FormItem
              name="validation.minLength"
              label={t('business:customField.validation.minLength')}
            >
              <Input fullWidth type="number" min="0" placeholder="0" />
            </FormItem>
          </ConditionalField>

          <ConditionalField
            condition={{
              field: 'type',
              type: ConditionType.IN,
              value: ['text', 'number'],
            }}
          >
            <FormItem
              name="validation.maxLength"
              label={t('business:customField.validation.maxLength')}
            >
              <Input fullWidth type="number" min="0" placeholder="100" />
            </FormItem>
          </ConditionalField>

          <ConditionalField
            condition={{
              field: 'type',
              type: ConditionType.EQUALS,
              value: 'text',
            }}
          >
            <FormItem
              name="validation.pattern"
              label={t('business:customField.validation.pattern')}
            >
              <div className="space-y-2">
                <Input fullWidth placeholder="^[A-Za-z0-9]+$" />
                <div className="text-sm text-gray-600">
                  <p className="font-medium mb-2">Gợi ý pattern phổ biến:</p>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 max-h-32 overflow-y-auto">
                    {COMMON_PATTERNS.map((pattern, index) => (
                      <button
                        key={index}
                        type="button"
                        className="text-left text-xs p-1 hover:bg-gray-100 rounded truncate"
                        title={pattern.value}
                        onClick={() => {
                          const input = document.querySelector('input[name="validation.pattern"]') as HTMLInputElement;
                          if (input) {
                            input.value = pattern.value;
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                          }
                        }}
                      >
                        <span className="font-medium text-blue-600">{pattern.label}:</span>
                        <span className="ml-1 text-gray-500 font-mono">{pattern.value.substring(0, 20)}...</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </FormItem>
          </ConditionalField>
        </div>

        <FormItem
          name="required"
          label={t('business:customField.required')}
        >
          <Toggle />
        </FormItem>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting || isCreating || isUpdating}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            isLoading={isSubmitting || isCreating || isUpdating}
          >
            {t('common:save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default CustomFieldForm;
