import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, Icon, Badge, Card, Table } from '@/shared/components/common';
import { CustomerDetailData, CustomerInteraction } from './types';
import { TableColumn } from '@/shared/components/common/Table/types';

interface CustomerInteractionsProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị tương tác của khách hàng
 */
const CustomerInteractions: React.FC<CustomerInteractionsProps> = ({ customer }) => {
  const { t } = useTranslation('business');

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get interaction icon
  const getInteractionIcon = (type: string) => {
    switch (type) {
      case 'email':
        return 'mail';
      case 'phone':
        return 'phone';
      case 'chat':
        return 'message-circle';
      case 'social':
        return 'users';
      case 'meeting':
        return 'calendar';
      default:
        return 'message-square';
    }
  };

  // Get interaction color
  const getInteractionColor = (type: string) => {
    switch (type) {
      case 'email':
        return 'text-blue-600';
      case 'phone':
        return 'text-green-600';
      case 'chat':
        return 'text-purple-600';
      case 'social':
        return 'text-pink-600';
      case 'meeting':
        return 'text-orange-600';
      default:
        return 'text-gray-600';
    }
  };

  // Get interaction background color
  const getInteractionBgColor = (type: string) => {
    switch (type) {
      case 'email':
        return 'bg-blue-50';
      case 'phone':
        return 'bg-green-50';
      case 'chat':
        return 'bg-purple-50';
      case 'social':
        return 'bg-pink-50';
      case 'meeting':
        return 'bg-orange-50';
      default:
        return 'bg-gray-50';
    }
  };

  // Get status variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'danger';
      default:
        return 'info';
    }
  };

  // Get status text
  const getStatusText = (status: string) => {
    return t(`customer.interaction.status.${status}`, status);
  };

  // Check if customer has interactions
  const hasInteractions = customer.interactions && customer.interactions.length > 0;

  // Group interactions by type
  const interactionsByType = hasInteractions && customer.interactions
    ? customer.interactions.reduce((acc, interaction) => {
        if (!acc[interaction.type]) {
          acc[interaction.type] = [];
        }
        acc[interaction.type].push(interaction);
        return acc;
      }, {} as Record<string, CustomerInteraction[]>)
    : {};

  // Get channel statistics
  const channelStats = Object.entries(interactionsByType).map(([type, interactions]) => ({
    type,
    count: interactions.length,
    lastInteraction: interactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0],
  }));

  // Sort interactions by date (newest first)
  const sortedInteractions = hasInteractions && customer.interactions
    ? [...customer.interactions].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    : [];

  // Table columns for interactions
  const columns: TableColumn<CustomerInteraction>[] = [
    {
      key: 'type',
      title: t('customer.interaction.type'),
      dataIndex: 'type',
      render: (value: unknown) => {
        const typeValue = String(value);
        return (
          <div className="flex items-center space-x-2">
            <div className={`p-2 rounded-full ${getInteractionBgColor(typeValue)}`}>
              <Icon name={getInteractionIcon(typeValue)} size="sm" className={getInteractionColor(typeValue)} />
            </div>
            <Typography variant="body2" className="text-foreground capitalize">
              {t(`customer.interaction.type.${typeValue}`, typeValue)}
            </Typography>
          </div>
        );
      },
    },
    {
      key: 'title',
      title: t('customer.interaction.title'),
      dataIndex: 'title',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-foreground font-medium">
          {String(value)}
        </Typography>
      ),
    },
    {
      key: 'channel',
      title: t('customer.interaction.channel'),
      dataIndex: 'channel',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-muted">
          {String(value)}
        </Typography>
      ),
    },
    {
      key: 'status',
      title: t('customer.interaction.status'),
      dataIndex: 'status',
      render: (value: unknown) => {
        const statusValue = String(value);
        return (
          <Badge variant={getStatusVariant(statusValue)} size="sm">
            {getStatusText(statusValue)}
          </Badge>
        );
      },
    },
    {
      key: 'date',
      title: t('customer.interaction.date'),
      dataIndex: 'date',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-muted">
          {formatDate(String(value))}
        </Typography>
      ),
    },
  ];

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="text-foreground">
            {t('customer.detail.interactions')}
          </Typography>
          <div className="flex items-center space-x-4">
            <Typography variant="body2" className="text-muted">
              {customer.interactions?.length || 0} {t('customer.detail.interactions').toLowerCase()}
            </Typography>
            {hasInteractions && (
              <Typography variant="body2" className="text-primary font-medium">
                {Object.keys(interactionsByType).length} {t('customer.detail.channels')}
              </Typography>
            )}
          </div>
        </div>
      }
      defaultOpen={false}
    >
      {hasInteractions ? (
        <div className="space-y-6">
          {/* Channel Statistics */}
          <div>
            <Typography variant="subtitle1" className="text-foreground mb-4">
              {t('customer.detail.channelStats')}
            </Typography>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {channelStats.map(({ type, count, lastInteraction }) => (
                <Card key={type} className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-3 rounded-full ${getInteractionBgColor(type)}`}>
                      <Icon
                        name={getInteractionIcon(type)}
                        size="md"
                        className={getInteractionColor(type)}
                      />
                    </div>
                    <div className="flex-1">
                      <Typography variant="h5" className="text-foreground">
                        {count}
                      </Typography>
                      <Typography variant="body2" className="text-muted capitalize">
                        {t(`customer.interaction.type.${type}`, type)}
                      </Typography>
                      <Typography variant="caption" className="text-muted">
                        {t('customer.detail.lastInteraction')}: {formatDate(lastInteraction.date)}
                      </Typography>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Interactions Table */}
          <div>
            <Typography variant="subtitle1" className="text-foreground mb-4">
              {t('customer.detail.allInteractions')}
            </Typography>
            <Table
              data={sortedInteractions}
              columns={columns}
              rowKey="id"
              pagination={sortedInteractions.length > 10}
              size="sm"
              className="border border-border rounded-lg"
            />
          </div>

          {/* Interaction Summary */}
          <div className="p-4 bg-muted/20 rounded-lg">
            <Typography variant="body2" className="text-muted text-center">
              {t('customer.detail.interactionSummary', {
                total: customer.interactions?.length || 0,
                channels: Object.keys(interactionsByType).length,
                completed: sortedInteractions.filter(i => i.status === 'completed').length
              })}
            </Typography>
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <div className="mb-4">
            <Icon name="message-circle" size="lg" className="text-muted mx-auto" />
          </div>
          <Typography variant="body1" className="text-muted mb-2">
            {t('customer.detail.noInteractions')}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {t('customer.detail.noInteractionsDesc')}
          </Typography>
        </div>
      )}
    </CollapsibleCard>
  );
};

export default CustomerInteractions;
