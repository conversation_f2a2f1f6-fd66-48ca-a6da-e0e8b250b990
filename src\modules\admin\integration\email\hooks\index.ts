import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { EmailServerService } from '../services';
import {
  EmailServerQueryParams,
  CreateEmailServerDto,
  UpdateEmailServerDto,
  TestEmailServerDto,
} from '../types';
import { useNotification } from '@/shared/hooks/useNotification';
import { useTranslation } from 'react-i18next';

/**
 * Query Keys
 */
export const emailServerQueryKeys = {
  all: ['admin', 'integration', 'email-servers'] as const,
  lists: () => [...emailServerQueryKeys.all, 'list'] as const,
  list: (params?: EmailServerQueryParams) => [...emailServerQueryKeys.lists(), params] as const,
  details: () => [...emailServerQueryKeys.all, 'detail'] as const,
  detail: (id: number) => [...emailServerQueryKeys.details(), id] as const,
};

/**
 * Hook để lấy danh sách email servers
 */
export const useEmailServers = (params?: EmailServerQueryParams) => {
  return useQuery({
    queryKey: emailServerQueryKeys.list(params),
    queryFn: () => EmailServerService.getEmailServers(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết email server
 */
export const useEmailServer = (id: number) => {
  return useQuery({
    queryKey: emailServerQueryKeys.detail(id),
    queryFn: () => EmailServerService.getEmailServer(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo email server
 */
export const useCreateEmailServer = () => {
  const queryClient = useQueryClient();
  const { showNotification } = useNotification();
  const { t } = useTranslation(['admin', 'common']);

  return useMutation({
    mutationFn: (data: CreateEmailServerDto) => EmailServerService.createEmailServer(data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: emailServerQueryKeys.lists() });
      showNotification({
        type: 'success',
        message: response.message || t('admin:integration.email.notifications.createSuccess'),
      });
    },
    onError: (error: any) => {
      showNotification({
        type: 'error',
        message: error.response?.data?.message || t('admin:integration.email.notifications.createError'),
      });
    },
  });
};

/**
 * Hook để cập nhật email server
 */
export const useUpdateEmailServer = () => {
  const queryClient = useQueryClient();
  const { showNotification } = useNotification();
  const { t } = useTranslation(['admin', 'common']);

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateEmailServerDto }) =>
      EmailServerService.updateEmailServer(id, data),
    onSuccess: (response, { id }) => {
      queryClient.invalidateQueries({ queryKey: emailServerQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: emailServerQueryKeys.detail(id) });
      showNotification({
        type: 'success',
        message: response.message || t('admin:integration.email.notifications.updateSuccess'),
      });
    },
    onError: (error: any) => {
      showNotification({
        type: 'error',
        message: error.response?.data?.message || t('admin:integration.email.notifications.updateError'),
      });
    },
  });
};

/**
 * Hook để xóa email server
 */
export const useDeleteEmailServer = () => {
  const queryClient = useQueryClient();
  const { showNotification } = useNotification();
  const { t } = useTranslation(['admin', 'common']);

  return useMutation({
    mutationFn: (id: number) => EmailServerService.deleteEmailServer(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: emailServerQueryKeys.lists() });
      showNotification({
        type: 'success',
        message: response.message || t('admin:integration.email.notifications.deleteSuccess'),
      });
    },
    onError: (error: any) => {
      showNotification({
        type: 'error',
        message: error.response?.data?.message || t('admin:integration.email.notifications.deleteError'),
      });
    },
  });
};

/**
 * Hook để test kết nối email server
 */
export const useTestEmailServer = () => {
  const { showNotification } = useNotification();
  const { t } = useTranslation(['admin', 'common']);

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: TestEmailServerDto }) =>
      EmailServerService.testEmailServerConnection(id, data),
    onSuccess: (response) => {
      const result = response.data;
      showNotification({
        type: result.success ? 'success' : 'error',
        message: result.message,
      });
    },
    onError: (error: any) => {
      showNotification({
        type: 'error',
        message: error.response?.data?.message || t('admin:integration.email.notifications.testError'),
      });
    },
  });
};
