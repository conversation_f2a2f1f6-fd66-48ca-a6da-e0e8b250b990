/**
 * <PERSON><PERSON><PERSON> nghĩa các types cho Products for Sale
 */

export enum ProductStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

export enum ProductCategory {
  AGENT = 'AGENT',
  KNOWLEDGE_FILE = 'KNOWLEDGE_FILE',
  FUNCTION = 'FUNCTION',
  FINETUNE = 'FINETUNE',
  STRATEGY = 'STRATEGY',
}

export interface ProductForSale {
  id: string;
  name: string;
  description: string;
  image: string;
  quantity: number;
  price: number;
  category: ProductCategory;
  status: ProductStatus;
  createdAt: string;
  updatedAt: string;
}

export interface ProductForSaleListResponse {
  code: number;
  message: string;
  result: {
    items: ProductForSale[];
    meta: {
      totalItems: number;
      itemCount: number;
      itemsPerPage: number;
      totalPages: number;
      currentPage: number;
    };
  };
}

export interface ProductForSaleFilterParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: ProductStatus | string;
  category?: ProductCategory | string;
  [key: string]: unknown;
}
