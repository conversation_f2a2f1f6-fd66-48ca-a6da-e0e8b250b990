import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, CollapsibleCard, Icon, ProgressBar } from '@/shared/components/common';
import { CustomerDetailData } from './types';

interface CustomerOverviewProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị tổng quan về khách hàng
 */
const CustomerOverview: React.FC<CustomerOverviewProps> = ({ customer }) => {
  const { t } = useTranslation('business');

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  // Get device icon
  const getDeviceIcon = (deviceName: string) => {
    const name = deviceName.toLowerCase();
    if (name.includes('mobile') || name.includes('phone')) return 'smartphone';
    if (name.includes('tablet') || name.includes('ipad')) return 'tablet';
    if (name.includes('desktop') || name.includes('computer')) return 'monitor';
    return 'device-mobile';
  };

  // Get channel icon
  const getChannelIcon = (channelName: string) => {
    const name = channelName.toLowerCase();
    if (name.includes('email')) return 'mail';
    if (name.includes('sms')) return 'message-square';
    if (name.includes('facebook')) return 'facebook';
    if (name.includes('zalo')) return 'message-circle';
    if (name.includes('website')) return 'globe';
    return 'message-square';
  };

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="text-foreground">
          {t('customer.detail.overview')}
        </Typography>
      }
      defaultOpen={true}
    >
      <div className="space-y-8">
        {/* Mục Doanh thu */}
        <div>
          <Typography variant="subtitle1" className="text-foreground mb-4">
            {t('customer.overview.revenue')}
          </Typography>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <Icon name="shopping-cart" size="md" className="text-primary mr-2" />
                <Typography variant="h4" className="text-primary">
                  {customer.totalOrders}
                </Typography>
              </div>
              <Typography variant="body2" className="text-muted">
                {t('customer.detail.totalOrders')}
              </Typography>
            </Card>
            <Card className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <Icon name="trending-up" size="md" className="text-success mr-2" />
                <Typography variant="h4" className="text-success">
                  {formatCurrency(customer.averageOrderValue)}
                </Typography>
              </div>
              <Typography variant="body2" className="text-muted">
                {t('customer.detail.averageOrderValue')}
              </Typography>
            </Card>
            <Card className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <Icon name="dollar-sign" size="md" className="text-info mr-2" />
                <Typography variant="h4" className="text-info">
                  {formatCurrency(customer.totalSpent)}
                </Typography>
              </div>
              <Typography variant="body2" className="text-muted">
                {t('customer.detail.totalSpent')}
              </Typography>
            </Card>
          </div>
        </div>

        {/* Mục lượt tương tác */}
        <div>
          <Typography variant="subtitle1" className="text-foreground mb-4">
            {t('customer.overview.interactions')}
          </Typography>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <Icon name="git-branch" size="md" className="text-purple-600 mr-2" />
                <Typography variant="h4" className="text-purple-600">
                  {customer.flowCount || 0}
                </Typography>
              </div>
              <Typography variant="body2" className="text-muted">
                {t('customer.overview.flows')}
              </Typography>
            </Card>
            <Card className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <Icon name="megaphone" size="md" className="text-orange-600 mr-2" />
                <Typography variant="h4" className="text-orange-600">
                  {customer.campaignCount || 0}
                </Typography>
              </div>
              <Typography variant="body2" className="text-muted">
                {t('customer.overview.campaigns')}
              </Typography>
            </Card>
            <Card className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <Icon name="list" size="md" className="text-blue-600 mr-2" />
                <Typography variant="h4" className="text-blue-600">
                  {customer.sequenceCount || 0}
                </Typography>
              </div>
              <Typography variant="body2" className="text-muted">
                {t('customer.overview.sequences')}
              </Typography>
            </Card>
          </div>
        </div>

        {/* Top kênh nhận tin nhắn */}
        <div>
          <Typography variant="subtitle1" className="text-foreground mb-4">
            {t('customer.overview.topChannels')}
          </Typography>
          {customer.topChannels && customer.topChannels.length > 0 ? (
            <div className="space-y-3">
              {customer.topChannels.slice(0, 5).map((channel) => (
                <div key={channel.id} className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-full bg-primary/10">
                      <Icon name={getChannelIcon(channel.name)} size="sm" className="text-primary" />
                    </div>
                    <div>
                      <Typography variant="body1" className="text-foreground font-medium">
                        {channel.name}
                      </Typography>
                      <Typography variant="body2" className="text-muted">
                        {channel.count} {t('customer.overview.messages')}
                      </Typography>
                    </div>
                  </div>
                  <div className="text-right">
                    <Typography variant="body1" className="text-foreground font-medium">
                      {channel.percentage}%
                    </Typography>
                    <div className="w-20 mt-1">
                      <ProgressBar value={channel.percentage} size="sm" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 bg-muted/20 rounded-lg">
              <Icon name="message-circle" size="lg" className="text-muted mx-auto mb-2" />
              <Typography variant="body2" className="text-muted">
                {t('customer.overview.noChannelData')}
              </Typography>
            </div>
          )}
        </div>

        {/* Top thiết bị khách hàng sử dụng */}
        <div>
          <Typography variant="subtitle1" className="text-foreground mb-4">
            {t('customer.overview.topDevices')}
          </Typography>
          {customer.topDevices && customer.topDevices.length > 0 ? (
            <div className="space-y-3">
              {customer.topDevices.slice(0, 5).map((device) => (
                <div key={device.id} className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-full bg-info/10">
                      <Icon name={getDeviceIcon(device.name)} size="sm" className="text-info" />
                    </div>
                    <div>
                      <Typography variant="body1" className="text-foreground font-medium">
                        {device.name}
                      </Typography>
                      <Typography variant="body2" className="text-muted">
                        {device.count} {t('customer.overview.sessions')}
                      </Typography>
                    </div>
                  </div>
                  <div className="text-right">
                    <Typography variant="body1" className="text-foreground font-medium">
                      {device.percentage}%
                    </Typography>
                    <div className="w-20 mt-1">
                      <ProgressBar value={device.percentage} size="sm" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 bg-muted/20 rounded-lg">
              <Icon name="monitor" size="lg" className="text-muted mx-auto mb-2" />
              <Typography variant="body2" className="text-muted">
                {t('customer.overview.noDeviceData')}
              </Typography>
            </div>
          )}
        </div>
      </div>
    </CollapsibleCard>
  );
};

export default CustomerOverview;
