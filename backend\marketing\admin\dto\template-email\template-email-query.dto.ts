import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Enum cho các trường sắp xếp
 */
export enum TemplateEmailSortField {
  ID = 'id',
  CATEGORY = 'category',
  SUBJECT = 'subject',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho thứ tự sắp xếp
 */
export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * DTO cho query parameters khi lấy danh sách template email
 */
export class TemplateEmailQueryDto {
  /**
   * Trang hiện tại (bắt đầu từ 1)
   * @example 1
   */
  @ApiProperty({
    description: 'Trang hiện tại (bắt đầu từ 1)',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Trang phải là số nguyên' })
  @Min(1, { message: 'Trang phải lớn hơn hoặc bằng 1' })
  @Type(() => Number)
  page?: number = 1;

  /**
   * Số lượng item trên mỗi trang
   * @example 10
   */
  @ApiProperty({
    description: 'Số lượng item trên mỗi trang',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Số lượng item phải là số nguyên' })
  @Min(1, { message: 'Số lượng item phải lớn hơn hoặc bằng 1' })
  @Type(() => Number)
  limit?: number = 10;

  /**
   * Tìm kiếm theo category
   * @example "ACCOUNT_VERIFICATION"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo category',
    example: 'ACCOUNT_VERIFICATION',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Category phải là chuỗi' })
  category?: string;

  /**
   * Tìm kiếm theo subject
   * @example "Xác thực tài khoản"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo subject',
    example: 'Xác thực tài khoản',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Subject phải là chuỗi' })
  subject?: string;

  /**
   * Sắp xếp theo trường
   * @example "createdAt"
   */
  @ApiProperty({
    description: 'Sắp xếp theo trường',
    enum: TemplateEmailSortField,
    example: TemplateEmailSortField.CREATED_AT,
    default: TemplateEmailSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(TemplateEmailSortField, {
    message: `Trường sắp xếp phải là một trong các giá trị: ${Object.values(TemplateEmailSortField).join(', ')}`,
  })
  sortBy?: TemplateEmailSortField = TemplateEmailSortField.CREATED_AT;

  /**
   * Thứ tự sắp xếp
   * @example "DESC"
   */
  @ApiProperty({
    description: 'Thứ tự sắp xếp',
    enum: SortOrder,
    example: SortOrder.DESC,
    default: SortOrder.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortOrder, {
    message: `Thứ tự sắp xếp phải là một trong các giá trị: ${Object.values(SortOrder).join(', ')}`,
  })
  sortOrder?: SortOrder = SortOrder.DESC;
}
