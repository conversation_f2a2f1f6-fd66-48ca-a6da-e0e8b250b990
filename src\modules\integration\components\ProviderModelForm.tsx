import React from 'react';
import { useTranslation } from 'react-i18next';
import {  Button, Input, FormItem, Form, Select } from '@/shared/components/common';
import { providerModelSchema } from '../provider-model/schemas';
import { ProviderModel, CreateProviderModelDto, UpdateProviderModelDto } from '../provider-model/types';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { getProviderOptions } from '../provider-model/types';

interface ProviderModelFormProps {
  /**
   * Dữ liệu ban đầu cho form (khi chỉnh sửa)
   */
  initialData?: ProviderModel | null;

  /**
   * Hàm xử lý khi submit form
   */
  onSubmit: (values: Record<string, unknown>) => void;

  /**
   * Hàm xử lý khi hủy form
   */
  onCancel: () => void;

  /**
   * Trạng thái đang submit form
   */
  isSubmitting?: boolean;

  /**
   * Ch<PERSON> độ chỉ đọc
   */
  readOnly?: boolean;
}

/**
 * Form tạo/chỉnh sửa Provider Model
 */
const ProviderModelForm: React.FC<ProviderModelFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  readOnly = false,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const isEditMode = !!initialData;

  // Chuẩn bị giá trị mặc định cho form
  const defaultValues: Partial<CreateProviderModelDto | UpdateProviderModelDto> = initialData
    ? {
        name: initialData.name,
        type: initialData.type,
        apiKey: '', // Không hiển thị API key cũ vì lý do bảo mật
      }
    : {
        name: '',
        type: TypeProviderEnum.OPENAI,
        apiKey: '',
      };

  // Xử lý submit form
  const handleFormSubmit = (values: Record<string, unknown>) => {
    onSubmit(values);
  };

  // Lấy danh sách provider options
  const providerOptions = getProviderOptions();

  return (
    <div
      title={readOnly
        ? t('admin:integration.providerModel.form.view', 'Xem Provider Model')
        : isEditMode
        ? t('admin:integration.providerModel.form.edit', 'Chỉnh sửa Provider Model')
        : t('admin:integration.providerModel.form.create', 'Tạo Provider Model')}
     
    >
      <Form
        schema={providerModelSchema}
        onSubmit={handleFormSubmit}
        className="space-y-6"
        defaultValues={defaultValues}
      >
        <div className="space-y-6">
          {/* Provider Name */}
          <FormItem
            name="name"
            label={t('admin:integration.providerModel.form.fields.name')}
            required
          >
            <Input
              placeholder={t('admin:integration.providerModel.form.placeholders.name')}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          {/* Provider Type */}
          <FormItem
            name="type"
            label={t('admin:integration.providerModel.form.fields.type')}
            required
          >
            <Select
              placeholder="Chọn loại provider..."
              disabled={readOnly || isSubmitting || isEditMode} // Không cho phép thay đổi type khi edit
              fullWidth
              options={providerOptions.map(option => ({
                value: option.value,
                label: option.label,
                searchLabel: option.label,
              }))}
            />
          </FormItem>

          {/* API Key */}
          <FormItem
            name="apiKey"
            label={t('admin:integration.providerModel.form.fields.apiKey')}
            required={!isEditMode} // Không bắt buộc khi edit (có thể giữ nguyên API key cũ)
          >
            <Input
              type="password"
              placeholder={
                isEditMode
                  ? 'Để trống nếu không muốn thay đổi API key...'
                  : t('admin:integration.providerModel.form.placeholders.apiKey')
              }
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          {/* Hiển thị thông tin bổ sung khi ở chế độ xem */}
          {readOnly && initialData && (
            <div className="space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-500 dark:text-gray-400">
                    {t('common:createdAt')}:
                  </span>
                  <div className="mt-1">
                    {new Date(initialData.createdAt).toLocaleString()}
                  </div>
                </div>
                <div>
                  <span className="font-medium text-gray-500 dark:text-gray-400">
                    {t('common:createdBy')}:
                  </span>
                  <div className="mt-1">
                    {initialData.createdBy.name}
                  </div>
                </div>
                {initialData.updatedAt && (
                  <>
                    <div>
                      <span className="font-medium text-gray-500 dark:text-gray-400">
                        {t('common:updatedAt')}:
                      </span>
                      <div className="mt-1">
                        {new Date(initialData.updatedAt).toLocaleString()}
                      </div>
                    </div>
                    {initialData.updatedBy && (
                      <div>
                        <span className="font-medium text-gray-500 dark:text-gray-400">
                          {t('common:updatedBy')}:
                        </span>
                        <div className="mt-1">
                          {initialData.updatedBy.name}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('admin:integration.providerModel.actions.cancel')}
          </Button>
          {!readOnly && (
            <Button type="submit" variant="primary" isLoading={isSubmitting}>
              {t('admin:integration.providerModel.actions.save')}
            </Button>
          )}
        </div>
      </Form>
    </div>
  );
};

export default ProviderModelForm;
