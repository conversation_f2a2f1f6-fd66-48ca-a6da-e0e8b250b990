import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Icon, Card } from '@/shared/components/common';
import { formatPrice } from '../utils/price-formatter';

interface CartSummaryProps {
  totalItems: number;
  subtotal: number;
  discount: number;
  total: number;
  onCheckout: () => void;
  isLoading?: boolean;
  selectedCount?: number;
}

/**
 * Component hiển thị tổng kết giỏ hàng và nút thanh toán
 */
const CartSummary: React.FC<CartSummaryProps> = ({
  totalItems,
  subtotal,
  discount,
  total,
  onCheckout,
  isLoading = false,
  selectedCount = 0,
}) => {
  const { t } = useTranslation(['marketplace', 'common']);

  return (
    <Card className="p-6 sticky top-4">
      <Typography variant="h6" className="mb-6">
        {t('marketplace:cart.summary', 'Thanh toán')}
      </Typography>

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Typography variant="body2" color="muted">
            {t('marketplace:cart.totalItems', 'Tổng số lượng sản phẩm')}
          </Typography>
          <Typography variant="body1" weight="medium">
            {totalItems}
          </Typography>
        </div>

        <div className="flex justify-between items-center">
          <Typography variant="body2" color="muted">
            {t('marketplace:cart.subtotal', 'Tổng giá trị đơn hàng')}
          </Typography>
          <div className="flex items-center">
            <Typography variant="body2" weight="medium" className="mr-1">
              {formatPrice(subtotal)}
            </Typography>
            <Icon name="rpoint" size="sm" className="text-primary" />
          </div>
        </div>

        <div className="flex justify-between items-center">
          <Typography variant="body2" color="muted">
            {t('marketplace:cart.discount', 'Khuyến mãi')}
          </Typography>
          <div className="flex items-center ">
            <Typography variant="body2" weight="medium" className="mr-1">
              - {formatPrice(discount)}
            </Typography>
            <Icon name="rpoint" size="sm" className="text-red-600" />
          </div>
        </div>

        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center">
            <Typography variant="body1" weight="bold">
              {t('marketplace:cart.total', 'Tổng thanh toán')}
            </Typography>
            <div className="flex items-center text-red-600">
              <Typography variant="body1" weight="bold" className="mr-1" color="error">
                {formatPrice(total)}
              </Typography>
              <Icon name="rpoint" size="md" className="text-red-600" />
            </div>
          </div>
        </div>
      </div>

      <Button
        variant="primary"
        fullWidth
        className="mt-6 bg-red-600 hover:bg-red-700"
        onClick={onCheckout}
        isLoading={isLoading}
        disabled={selectedCount === 0 || total === 0}
      >
        {selectedCount === 0
          ? t('marketplace:cart.selectItems', 'Chọn sản phẩm để thanh toán')
          : t('marketplace:cart.checkout', 'Thanh toán')
        }
      </Button>
    </Card>
  );
};

export default CartSummary;
