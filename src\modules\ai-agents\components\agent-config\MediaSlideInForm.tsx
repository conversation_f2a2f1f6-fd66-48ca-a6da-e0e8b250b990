import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  Button,
  Card,
  EmptyState,
  Icon,
  Pagination,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Interface cho item Media
 */
interface Media {
  id: string;
  name: string;
  type: 'image' | 'video' | 'audio' | 'document';
  url: string;
  thumbnailUrl?: string;
  fileSize?: number;
  duration?: number;
  createdAt: string;
  format?: string;
}

/**
 * Props cho component MediaSlideInForm
 */
interface MediaSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi chọn các media
   */
  onSelect: (selectedMedia: Media[]) => void;

  /**
   * <PERSON>h sách ID của các media đã chọn
   */
  selectedMediaIds?: string[];
}

/**
 * Component form trượt để chọn các media
 */
const MediaSlideInForm: React.FC<MediaSlideInFormProps> = ({
  isVisible,
  onClose,
  onSelect,
  selectedMediaIds = [],
}) => {
  // State cho dữ liệu và UI
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');
  const [mediaItems, setMediaItems] = useState<Media[]>([]);
  const [selectedIds, setSelectedIds] = useState<string[]>(selectedMediaIds);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // State cho phân trang
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);

  // State cho sắp xếp và lọc
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'ASC' | 'DESC'>('ASC');
  const [filterType, setFilterType] = useState<string>('');

  // Cấu hình cột cho bảng
  const columns: TableColumn<Media>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'media',
      title: 'Media',
      dataIndex: 'name',
      width: '40%',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="w-12 h-12 rounded-md bg-gray-100 flex items-center justify-center mr-3 overflow-hidden">
            {record.thumbnailUrl ? (
              <img
                src={record.thumbnailUrl}
                alt={record.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <Icon 
                name={
                  record.type === 'image' ? 'image' : 
                  record.type === 'video' ? 'video' : 
                  record.type === 'audio' ? 'music' : 'file'
                } 
                size="md" 
                className="text-gray-500" 
              />
            )}
          </div>
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              {record.format && `${record.format.toUpperCase()} • `}
              {record.fileSize && `${(record.fileSize / 1024 / 1024).toFixed(2)} MB`}
              {record.duration && ` • ${Math.floor(record.duration / 60)}:${String(record.duration % 60).padStart(2, '0')}`}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'type',
      title: 'Loại',
      dataIndex: 'type',
      width: '20%',
      render: (_, record) => (
        <span className="capitalize">{record.type}</span>
      ),
    },
    {
      key: 'createdAt',
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      width: '20%',
      render: (_, record) => (
        <span>{new Date(record.createdAt).toLocaleDateString('vi-VN')}</span>
      ),
    },
    {
      key: 'preview',
      title: 'Xem trước',
      width: '20%',
      render: (_, record) => (
        <Button
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            window.open(record.url, '_blank');
          }}
        >
          <Icon name="eye" size="sm" className="mr-1" />
          Xem
        </Button>
      ),
    },
  ];

  // Giả lập dữ liệu media - sử dụng useMemo để tránh re-render không cần thiết
  const mockMediaItems = useMemo<Media[]>(() => [
    { id: 'media-1', name: 'Banner trang chủ.jpg', type: 'image', url: 'https://example.com/images/banner.jpg', thumbnailUrl: 'https://via.placeholder.com/150x150', fileSize: 1024 * 1024 * 2.5, createdAt: '2023-05-15', format: 'jpg' },
    { id: 'media-2', name: 'Video giới thiệu sản phẩm.mp4', type: 'video', url: 'https://example.com/videos/intro.mp4', thumbnailUrl: 'https://via.placeholder.com/150x150', fileSize: 1024 * 1024 * 15, duration: 125, createdAt: '2023-06-20', format: 'mp4' },
    { id: 'media-3', name: 'Hướng dẫn sử dụng.pdf', type: 'document', url: 'https://example.com/docs/guide.pdf', fileSize: 1024 * 1024 * 1.2, createdAt: '2023-07-05', format: 'pdf' },
    { id: 'media-4', name: 'Nhạc nền.mp3', type: 'audio', url: 'https://example.com/audio/background.mp3', fileSize: 1024 * 1024 * 3.5, duration: 180, createdAt: '2023-08-10', format: 'mp3' },
    { id: 'media-5', name: 'Logo công ty.png', type: 'image', url: 'https://example.com/images/logo.png', thumbnailUrl: 'https://via.placeholder.com/150x150', fileSize: 1024 * 512, createdAt: '2023-04-25', format: 'png' },
    { id: 'media-6', name: 'Bảng giá 2023.xlsx', type: 'document', url: 'https://example.com/docs/price.xlsx', fileSize: 1024 * 1024 * 0.8, createdAt: '2023-01-15', format: 'xlsx' },
    { id: 'media-7', name: 'Ảnh sản phẩm 1.jpg', type: 'image', url: 'https://example.com/images/product1.jpg', thumbnailUrl: 'https://via.placeholder.com/150x150', fileSize: 1024 * 1024 * 1.5, createdAt: '2023-09-05', format: 'jpg' },
    { id: 'media-8', name: 'Ảnh sản phẩm 2.jpg', type: 'image', url: 'https://example.com/images/product2.jpg', thumbnailUrl: 'https://via.placeholder.com/150x150', fileSize: 1024 * 1024 * 1.7, createdAt: '2023-09-06', format: 'jpg' },
    { id: 'media-9', name: 'Ảnh sản phẩm 3.jpg', type: 'image', url: 'https://example.com/images/product3.jpg', thumbnailUrl: 'https://via.placeholder.com/150x150', fileSize: 1024 * 1024 * 1.6, createdAt: '2023-09-07', format: 'jpg' },
    { id: 'media-10', name: 'Video demo.mp4', type: 'video', url: 'https://example.com/videos/demo.mp4', thumbnailUrl: 'https://via.placeholder.com/150x150', fileSize: 1024 * 1024 * 25, duration: 300, createdAt: '2023-10-01', format: 'mp4' },
  ], []);

  // Giả lập việc tải dữ liệu
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Giả lập API call
        await new Promise(resolve => setTimeout(resolve, 500));

        // Lọc dữ liệu theo tìm kiếm và loại
        let filteredData = [...mockMediaItems];

        if (search) {
          filteredData = filteredData.filter(media =>
            media.name.toLowerCase().includes(search.toLowerCase())
          );
        }

        if (filterType) {
          filteredData = filteredData.filter(media => media.type === filterType);
        }

        // Sắp xếp dữ liệu
        filteredData.sort((a, b) => {
          if (sortBy === 'name') {
            return sortDirection === 'ASC'
              ? a.name.localeCompare(b.name)
              : b.name.localeCompare(a.name);
          } else if (sortBy === 'createdAt') {
            return sortDirection === 'ASC'
              ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
              : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
          } else if (sortBy === 'fileSize') {
            const aSize = a.fileSize || 0;
            const bSize = b.fileSize || 0;
            return sortDirection === 'ASC' ? aSize - bSize : bSize - aSize;
          }
          return 0;
        });

        // Phân trang
        const startIndex = (currentPage - 1) * itemsPerPage;
        const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage);

        setMediaItems(paginatedData);
        setTotalItems(filteredData.length);
      } catch (error) {
        console.error('Error fetching media:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, currentPage, itemsPerPage, sortBy, sortDirection, filterType]);

  // Kiểm tra có thay đổi chưa lưu không
  useEffect(() => {
    const hasUnsavedChanges =
      selectedIds.length !== selectedMediaIds.length ||
      selectedIds.some(id => !selectedMediaIds.includes(id)) ||
      selectedMediaIds.some(id => !selectedIds.includes(id));

    setHasChanges(hasUnsavedChanges);
  }, [selectedIds, selectedMediaIds]);

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setSearch(term);
    setCurrentPage(1);
  };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = (column: string, direction: 'ASC' | 'DESC') => {
    setSortBy(column);
    setSortDirection(direction);
  };

  // Xử lý lưu
  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      // Giả lập API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Lấy thông tin đầy đủ của các media đã chọn
      const selectedMedia = mockMediaItems.filter(media =>
        selectedIds.includes(media.id)
      );

      onSelect(selectedMedia);
      onClose();
    } catch (error) {
      console.error('Error saving selected media:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    if (hasChanges) {
      const confirmed = window.confirm(
        'Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn đóng form?'
      );
      if (!confirmed) return;
    }

    setSearch('');
    onClose();
  }, [hasChanges, onClose]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'sort',
      label: 'Sắp xếp theo',
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-name',
      label: 'Tên',
      onClick: () => handleSortChange('name', sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-date',
      label: 'Ngày tạo',
      onClick: () => handleSortChange('createdAt', sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-size',
      label: 'Kích thước',
      onClick: () => handleSortChange('fileSize', sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'divider',
      divider: true,
    },
    {
      id: 'filter',
      label: 'Lọc theo',
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'filter-all',
      label: 'Tất cả',
      onClick: () => setFilterType(''),
    },
    {
      id: 'filter-image',
      label: 'Hình ảnh',
      onClick: () => setFilterType('image'),
    },
    {
      id: 'filter-video',
      label: 'Video',
      onClick: () => setFilterType('video'),
    },
    {
      id: 'filter-audio',
      label: 'Âm thanh',
      onClick: () => setFilterType('audio'),
    },
    {
      id: 'filter-document',
      label: 'Tài liệu',
      onClick: () => setFilterType('document'),
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-6xl">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">Chọn media</Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            Đóng
          </Button>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Bảng dữ liệu */}
        <div className="mb-4 w-full overflow-x-auto">
          <Table
            data={mediaItems}
            columns={columns}
            loading={isLoading}
            rowKey="id"
            size="lg"
            hoverable
            bordered={false}
            selectable={true}
            className="w-full table-fixed"
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
          />

          {/* Hiển thị khi không có dữ liệu */}
          {!isLoading && mediaItems.length === 0 && (
            <EmptyState
              icon="search"
              title="Không có kết quả"
              description="Không tìm thấy media nào phù hợp với tìm kiếm của bạn."
              className="py-8"
            />
          )}
        </div>

        {/* Phân trang */}
        <div className="mb-4">
          <Pagination
            currentPage={currentPage}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPageOptions={[5, 10, 20, 50]}
            showItemsPerPageSelector={true}
            showPageInfo={true}
          />
        </div>

        {/* Nút lưu */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            className="mr-2"
            disabled={isSubmitting}
          >
            Hủy
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            isLoading={isSubmitting}
            disabled={isLoading || !hasChanges}
          >
            Lưu
          </Button>
        </div>
      </Card>
    </SlideInForm>
  );
};

export default MediaSlideInForm;
