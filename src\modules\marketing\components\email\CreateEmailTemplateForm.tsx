import { useState } from 'react';
import { useForm, FormProvider, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Plus, Trash2, Eye, Code } from 'lucide-react';
import { But<PERSON> } from '@/shared/components/common';
import { Input } from '@/shared/components/common';
import { Alert } from '@/shared/components/common';
import { FormItem } from '@/shared/components/common';
import { createEmailTemplateSchema, type CreateEmailTemplateFormData } from '../../schemas/email.schema';
import { useCreateEmailTemplate } from '../../hooks/email/useEmailTemplates';
import { EmailTemplateType } from '../../types/email.types';

interface CreateEmailTemplateFormProps {
  onSuccess?: () => void;
}

/**
 * Form tạo Email Template
 */
export function CreateEmailTemplateForm({ onSuccess }: CreateEmailTemplateFormProps) {
  const [previewMode, setPreviewMode] = useState<'design' | 'code'>('design');
  const createTemplate = useCreateEmailTemplate();

  const form = useForm<CreateEmailTemplateFormData>({
    resolver: zodResolver(createEmailTemplateSchema),
    defaultValues: {
      name: '',
      subject: '',
      htmlContent: '',
      textContent: '',
      type: EmailTemplateType.NEWSLETTER,
      previewText: '',
    },
  });

  const { fields: variableFields, append: appendVariable, remove: removeVariable } = useFieldArray({
    control: form.control,
    name: 'variables',
  });

  // Quản lý tags manually vì tags là optional array
  const [tags, setTags] = useState<string[]>([]);

  const onSubmit = async (data: CreateEmailTemplateFormData) => {
    try {
      // Thêm tags vào data trước khi submit
      const submitData = {
        ...data,
        tags: tags.length > 0 ? tags : undefined,
      };
      await createTemplate.mutateAsync(submitData);
      onSuccess?.();
    } catch {
      // Error được handle trong hook
    }
  };

  const addVariable = () => {
    appendVariable({
      name: '',
      type: 'TEXT' as const,
      defaultValue: '',
      required: false,
      description: '',
    });
  };

  const addTag = () => {
    setTags([...tags, '']);
  };

  const removeTag = (index: number) => {
    setTags(tags.filter((_, i) => i !== index));
  };

  const updateTag = (index: number, value: string) => {
    const newTags = [...tags];
    newTags[index] = value;
    setTags(newTags);
  };

  const templateTypes = [
    { value: EmailTemplateType.NEWSLETTER, label: 'Newsletter' },
    { value: EmailTemplateType.PROMOTIONAL, label: 'Khuyến mãi' },
    { value: EmailTemplateType.TRANSACTIONAL, label: 'Giao dịch' },
    { value: EmailTemplateType.WELCOME, label: 'Chào mừng' },
    { value: EmailTemplateType.ABANDONED_CART, label: 'Giỏ hàng bỏ quên' },
    { value: EmailTemplateType.FOLLOW_UP, label: 'Theo dõi' },
  ];

  const variableTypes = [
    { value: 'TEXT', label: 'Văn bản' },
    { value: 'NUMBER', label: 'Số' },
    { value: 'DATE', label: 'Ngày tháng' },
    { value: 'URL', label: 'Đường dẫn' },
    { value: 'IMAGE', label: 'Hình ảnh' },
  ];

  return (
    <div className="space-y-6">
      {/* Instructions */}
      <Alert
        type="info"
        message="Hướng dẫn tạo Email Template"
        description="Sử dụng HTML để tạo nội dung email đẹp mắt. Thêm biến động bằng cú pháp {variable_name}. Preview text sẽ hiển thị trong inbox preview."
      />

      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem
              name="name"
              label="Tên Template"
              helpText="Tên mô tả cho template"
              required
            >
              <Input placeholder="Ví dụ: Newsletter tháng 1, Khuyến mãi Black Friday..." />
            </FormItem>

            <FormItem
              name="type"
              label="Loại Template"
              helpText="Phân loại template"
              required
            >
              <select className="w-full p-2 border rounded-md">
                {templateTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </FormItem>
          </div>

          <FormItem
            name="subject"
            label="Tiêu đề Email"
            helpText="Subject line của email"
            required
          >
            <Input placeholder="Ví dụ: 🎉 Khuyến mãi đặc biệt dành cho bạn!" />
          </FormItem>

          <FormItem
            name="previewText"
            label="Preview Text"
            helpText="Văn bản hiển thị trong inbox preview (tối đa 150 ký tự)"
          >
            <Input placeholder="Ví dụ: Đừng bỏ lỡ ưu đãi lên đến 50% cho tất cả sản phẩm..." />
          </FormItem>

          {/* Content Editor */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Nội dung Email</h4>
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant={previewMode === 'design' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setPreviewMode('design')}
                >
                  <Eye className="h-4 w-4 mr-1" />
                  Design
                </Button>
                <Button
                  type="button"
                  variant={previewMode === 'code' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setPreviewMode('code')}
                >
                  <Code className="h-4 w-4 mr-1" />
                  HTML
                </Button>
              </div>
            </div>

            {previewMode === 'code' ? (
              <FormItem
                name="htmlContent"
                label="HTML Content"
                required
              >
                <textarea
                  className="w-full p-3 border rounded-md min-h-[300px] font-mono text-sm"
                  placeholder="<!DOCTYPE html>
<html>
<head>
  <title>Email Template</title>
</head>
<body>
  <h1>Xin chào {customer_name}!</h1>
  <p>Cảm ơn bạn đã đăng ký newsletter của chúng tôi.</p>
</body>
</html>"
                />
              </FormItem>
            ) : (
              <div className="border rounded-lg p-4 min-h-[300px] bg-white">
                <div className="text-center text-muted-foreground py-12">
                  <Eye className="h-12 w-12 mx-auto mb-4" />
                  <p>Visual editor sẽ được triển khai trong phase tiếp theo</p>
                  <p className="text-sm">Hiện tại vui lòng sử dụng HTML editor</p>
                </div>
              </div>
            )}
          </div>

          <FormItem
            name="textContent"
            label="Text Content (Tùy chọn)"
            helpText="Phiên bản text thuần cho email clients không hỗ trợ HTML"
          >
            <textarea
              className="w-full p-3 border rounded-md min-h-[120px]"
              placeholder="Xin chào {customer_name}!

Cảm ơn bạn đã đăng ký newsletter của chúng tôi..."
            />
          </FormItem>

          {/* Variables */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Biến động</h4>
                <p className="text-sm text-muted-foreground">
                  Định nghĩa các biến sẽ được thay thế khi gửi email
                </p>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addVariable}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Thêm biến
              </Button>
            </div>

            {variableFields.length > 0 && (
              <div className="space-y-3">
                {variableFields.map((field, index) => (
                  <div key={field.id} className="grid grid-cols-12 gap-3 p-3 border rounded-lg">
                    <div className="col-span-3">
                      <FormItem
                        name={`variables.${index}.name`}
                        label="Tên biến"
                        required
                      >
                        <Input placeholder="customer_name" />
                      </FormItem>
                    </div>
                    <div className="col-span-2">
                      <FormItem
                        name={`variables.${index}.type`}
                        label="Loại"
                        required
                      >
                        <select className="w-full p-2 border rounded-md">
                          {variableTypes.map((type) => (
                            <option key={type.value} value={type.value}>
                              {type.label}
                            </option>
                          ))}
                        </select>
                      </FormItem>
                    </div>
                    <div className="col-span-3">
                      <FormItem
                        name={`variables.${index}.defaultValue`}
                        label="Giá trị mặc định"
                      >
                        <Input placeholder="Khách hàng" />
                      </FormItem>
                    </div>
                    <div className="col-span-3">
                      <FormItem
                        name={`variables.${index}.description`}
                        label="Mô tả"
                      >
                        <Input placeholder="Tên khách hàng" />
                      </FormItem>
                    </div>
                    <div className="col-span-1 flex items-end">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeVariable(index)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Tags */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Tags</h4>
                <p className="text-sm text-muted-foreground">
                  Thêm tags để phân loại template
                </p>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addTag}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Thêm tag
              </Button>
            </div>

            {tags.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {tags.map((tag, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="flex-1">
                      <label className="text-sm font-medium">Tag {index + 1}</label>
                      <Input
                        placeholder="newsletter, promotion..."
                        value={tag}
                        onChange={(e) => updateTag(index, e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeTag(index)}
                      className="text-destructive mt-6"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onSuccess?.()}
              disabled={createTemplate.isPending}
            >
              Hủy
            </Button>
            <Button
              type="submit"
              disabled={createTemplate.isPending}
            >
              {createTemplate.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Tạo Template
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
}
