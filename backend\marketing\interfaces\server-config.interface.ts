import { Platform } from '../enums';

/**
 * Interface cho cấu hình máy chủ email
 */
export interface EmailServerConfig {
  type: Platform.EMAIL;
  host: string;
  port: number;
  username: string;
  password: string;
  useSsl: boolean;
  fromName?: string;
  fromEmail?: string;
  additionalSettings?: Record<string, any>;
}

/**
 * Interface cho cấu hình máy chủ SMS
 */
export interface SmsServerConfig {
  type: Platform.SMS;
  provider: string; // Ví dụ: 'twilio', 'vonage', 'viettel', 'mobifone', v.v.
  apiKey: string;
  apiSecret?: string;
  accountSid?: string; // Cho Twilio
  authToken?: string; // Cho Twilio
  fromPhone?: string;
  additionalSettings?: Record<string, any>;
}

/**
 * Interface cho cấu hình Zalo OA
 */
export interface ZaloServerConfig {
  type: Platform.ZALO;
  oaId: string; // ID của Official Account
  secretKey: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt?: number;
  additionalSettings?: Record<string, any>;
}

/**
 * Interface cho cấu hình Facebook
 */
export interface FacebookServerConfig {
  type: Platform.FACEBOOK;
  pageId: string;
  accessToken: string;
  appId?: string;
  appSecret?: string;
  expiresAt?: number;
  additionalSettings?: Record<string, any>;
}

/**
 * Union type cho tất cả các loại cấu hình máy chủ
 */
export type ServerConfig = 
  | EmailServerConfig 
  | SmsServerConfig 
  | ZaloServerConfig 
  | FacebookServerConfig;
