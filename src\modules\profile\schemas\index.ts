// Export schemas from user.schema
export {
  personalInfoSchema,
  bankInfoSchema,
  securityInfoSchema,
  notificationSettingsSchema,
} from './user.schema';

// Export types from profile.schema
export type {
  UserProfileSchema,
  PersonalInfoSchema as ProfilePersonalInfoSchema,
  SecurityInfoSchema as ProfileSecurityInfoSchema,
  NotificationSettingsSchema,
  BankInfoSchema as ProfileBankInfoSchema,
} from './profile.schema';

// Export schemas from profile.schema
export { userProfileSchema } from './profile.schema';

// Export types and schemas from business-info.schema
export type { BusinessInfoSchema } from './business-info.schema';
export { createBusinessInfoSchema, businessInfoSchema } from './business-info.schema';

// Export types and schemas from personal-info.schema
export { createPersonalInfoSchema } from './personal-info.schema';
export type { PersonalInfoSchema } from './personal-info.schema';

// Export types and schemas from bank-info.schema
export { createBankInfoSchema } from './bank-info.schema';
export type { BankInfoSchema } from './bank-info.schema';

// Export types and schemas from security-info.schema
export { createSecurityInfoSchema, createPasswordChangeSchema } from './security-info.schema';
export type { SecurityInfoSchema, PasswordChangeSchema } from './security-info.schema';
