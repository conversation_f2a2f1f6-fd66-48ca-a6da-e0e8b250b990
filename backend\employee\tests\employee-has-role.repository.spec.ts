import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeHasRoleRepository } from '../repositories/employee-has-role.repository';
import { Repository } from 'typeorm';
import { Employee } from '../entities/employee.entity';
import { EmployeeRole } from '../entities/employee-role.entity';
import { getRepositoryToken } from '@nestjs/typeorm';

describe('EmployeeHasRoleRepository', () => {
  let repository: EmployeeHasRoleRepository;
  let employeeRepository: jest.Mocked<Repository<Employee>>;

  const mockEmployee: Employee = {
    id: 1,
    fullName: 'Test Employee',
    email: '<EMAIL>',
    phoneNumber: '0987654321',
    password: 'hashedPassword',
    address: 'Test Address',
    createdAt: Date.now(),
    updatedAt: Date.now(),
    enable: true,
    avatar: 'avatar-url',
    roles: []
  };

  const mockRoles: EmployeeRole[] = [
    {
      id: 1,
      name: 'Admin',
      description: 'Administrator role',
      permissions: []
    },
    {
      id: 2,
      name: 'Editor',
      description: 'Content editor role',
      permissions: []
    }
  ];

  beforeEach(async () => {
    // Create mock implementation for TypeORM repository
    const employeeRepositoryMock = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmployeeHasRoleRepository,
        {
          provide: getRepositoryToken(Employee),
          useValue: employeeRepositoryMock,
        },
      ],
    }).compile();

    repository = module.get<EmployeeHasRoleRepository>(EmployeeHasRoleRepository);
    employeeRepository = module.get(getRepositoryToken(Employee)) as jest.Mocked<Repository<Employee>>;
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  describe('assignRolesToEmployee', () => {
    it('should assign roles to employee successfully', async () => {
      // Arrange
      const employeeId = 1;
      const roleIds = [1, 2];

      const employeeWithRoles = {
        ...mockEmployee,
        roles: roleIds.map(id => ({ id, name: 'Role ' + id, description: 'Description ' + id, permissions: [] })),
        updatedAt: expect.any(Number)
      };

      employeeRepository.findOne.mockResolvedValue(mockEmployee);
      employeeRepository.save.mockResolvedValue(employeeWithRoles);

      // Act
      const result = await repository.assignRolesToEmployee(employeeId, roleIds);

      // Assert
      expect(employeeRepository.findOne).toHaveBeenCalledWith({
        where: { id: employeeId },
        relations: ['roles'],
      });
      expect(employeeRepository.save).toHaveBeenCalledWith(expect.objectContaining({
        ...mockEmployee,
        roles: expect.any(Array),
        updatedAt: expect.any(Number),
      }));
      expect(result).toEqual(employeeWithRoles);
    });

    it('should throw Error when employee is not found', async () => {
      // Arrange
      const employeeId = 999;
      const roleIds = [1, 2];

      employeeRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(repository.assignRolesToEmployee(employeeId, roleIds)).rejects.toThrow(Error);
      expect(employeeRepository.findOne).toHaveBeenCalledWith({
        where: { id: employeeId },
        relations: ['roles'],
      });
    });
  });

  describe('hasRole', () => {
    it('should return true when employee has the role', async () => {
      // Arrange
      const employeeId = 1;
      const roleId = 1;

      const employeeWithRoles = {
        ...mockEmployee,
        roles: [{ id: roleId, name: 'Role ' + roleId, description: 'Description ' + roleId, permissions: [] }],
      };

      employeeRepository.findOne.mockResolvedValue(employeeWithRoles);

      // Act
      const result = await repository.hasRole(employeeId, roleId);

      // Assert
      expect(employeeRepository.findOne).toHaveBeenCalledWith({
        where: { id: employeeId },
        relations: ['roles'],
      });
      expect(result).toBe(true);
    });

    it('should return false when employee does not have the role', async () => {
      // Arrange
      const employeeId = 1;
      const roleId = 3;

      const employeeWithRoles = {
        ...mockEmployee,
        roles: [
          { id: 1, name: 'Role 1', description: 'Description 1', permissions: [] },
          { id: 2, name: 'Role 2', description: 'Description 2', permissions: [] }
        ],
      };

      employeeRepository.findOne.mockResolvedValue(employeeWithRoles);

      // Act
      const result = await repository.hasRole(employeeId, roleId);

      // Assert
      expect(employeeRepository.findOne).toHaveBeenCalledWith({
        where: { id: employeeId },
        relations: ['roles'],
      });
      expect(result).toBe(false);
    });

    it('should return false when employee is not found', async () => {
      // Arrange
      const employeeId = 999;
      const roleId = 1;

      employeeRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await repository.hasRole(employeeId, roleId);

      // Assert
      expect(employeeRepository.findOne).toHaveBeenCalledWith({
        where: { id: employeeId },
        relations: ['roles'],
      });
      expect(result).toBe(false);
    });
  });

  describe('findRolesByEmployeeId', () => {
    it('should find roles by employee id successfully', async () => {
      // Arrange
      const employeeId = 1;

      const employeeWithRoles = {
        ...mockEmployee,
        roles: mockRoles,
      };

      employeeRepository.findOne.mockResolvedValue(employeeWithRoles);

      // Act
      const result = await repository.findRolesByEmployeeId(employeeId);

      // Assert
      expect(employeeRepository.findOne).toHaveBeenCalledWith({
        where: { id: employeeId },
        relations: ['roles', 'roles.permissions'],
      });
      expect(result).toEqual(mockRoles);
    });

    it('should throw Error when employee is not found', async () => {
      // Arrange
      const employeeId = 999;

      employeeRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(repository.findRolesByEmployeeId(employeeId)).rejects.toThrow(Error);
      expect(employeeRepository.findOne).toHaveBeenCalledWith({
        where: { id: employeeId },
        relations: ['roles', 'roles.permissions'],
      });
    });
  });
});
