import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Trang tổng quan quản lý dữ liệu
 */
const DataManagementPage: React.FC = () => {
  const { t } = useTranslation(['data', 'common']);

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Media Card */}
        <ModuleCard
          title={t('data:media.title', 'Thư viện Media')}
          description={t(
            'data:media.description',
            'Quản lý các tệp tin media như hình ảnh, video, âm thanh và tài liệu.'
          )}
          icon="file-media"
          linkTo="/data/files"
        />

        {/* Knowledge Files Card */}
        <ModuleCard
          title={t('data:knowledgeFiles.title', 'File tri thức')}
          description={t(
            'data:knowledgeFiles.description',
            'Quản lý các tệp tin tri thức được sử dụng cho AI và vector store.'
          )}
          icon="file-text"
          linkTo="/data/knowledge-files"
        />

        {/* URL Card */}
        <ModuleCard
          title={t('data:url.title', 'Quản lý URL')}
          description={t(
            'data:url.description',
            'Quản lý các URL và tài nguyên web được sử dụng trong hệ thống.'
          )}
          icon="link"
          linkTo="/data/url"
        />

        {/* Vector Store Card */}
        <ModuleCard
          title={t('data:vectorStore.title', 'Vector Store')}
          description={t(
            'data:vectorStore.description',
            'Quản lý các vector store và embedding cho các ứng dụng AI và tìm kiếm ngữ nghĩa.'
          )}
          icon="database"
          linkTo="/data/vector-store"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default DataManagementPage;
