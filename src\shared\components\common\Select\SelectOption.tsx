import React, { ReactNode } from 'react';

export interface SelectOptionProps {
  /**
   * Giá trị của option
   */
  value: string | number;

  /**
   * Label hiển thị
   */
  label: string;

  /**
   * Icon hiển thị bên trái label
   */
  icon?: ReactNode;

  /**
   * Trạng thái disabled
   */
  disabled?: boolean;

  /**
   * Trạng thái đã chọn
   */
  selected?: boolean;

  /**
   * Callback khi click vào option
   */
  onClick?: () => void;

  /**
   * Dữ liệu tùy chỉnh
   */
  data?: Record<string, unknown>;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị một option trong Select
 */
const SelectOption: React.FC<SelectOptionProps> = ({
  label,
  icon,
  disabled = false,
  selected = false,
  onClick,
  className = '',
}) => {
  // Base classes
  const baseClasses = 'flex items-center px-4 py-2 text-sm cursor-pointer text-foreground';

  // Selected classes - sử dụng theme colors thay vì màu đỏ
  const selectedClasses = selected
    ? 'bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-light'
    : 'hover:bg-muted/50 dark:hover:bg-muted/20';

  // Disabled classes
  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';

  // Combine all classes
  const optionClasses = [baseClasses, selectedClasses, disabledClasses, className].join(' ');

  return (
    <div
      className={optionClasses}
      onClick={disabled ? undefined : onClick}
      role="option"
      aria-selected={selected}
      aria-disabled={disabled}
    >
      {icon && <span className="mr-2">{icon}</span>}
      <span>{label}</span>
      {selected && (
        <svg className="w-4 h-4 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      )}
    </div>
  );
};

export default SelectOption;
