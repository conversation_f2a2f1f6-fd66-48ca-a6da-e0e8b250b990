{"integration": {"title": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> h<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tích hợp hệ thống và cấu hình kết nối", "email": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý cấu hình máy chủ email cho hệ thống g<PERSON> email tự động", "list": {"title": "<PERSON><PERSON>", "empty": "Chưa có cấu hình email server nào", "search": "<PERSON><PERSON><PERSON> kiếm theo tên hoặc host...", "columns": {"serverName": "<PERSON><PERSON><PERSON>", "host": "Host", "port": "Port", "username": "Username", "ssl": "SSL", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON>"}}, "form": {"create": "Tạo Email Server", "edit": "Chỉnh s<PERSON>a <PERSON>", "test": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "fields": {"serverName": "<PERSON><PERSON><PERSON>", "host": "Host", "port": "Port", "username": "Username", "password": "Password", "useSsl": "Sử dụng SSL", "useStartTls": "<PERSON>ử dụng StartTLS", "additionalSettings": "<PERSON><PERSON><PERSON> đặt b<PERSON> sung (JSON)", "isActive": "<PERSON><PERSON><PERSON>", "recipientEmail": "<PERSON><PERSON>n thử nghiệm", "subject": "Ti<PERSON><PERSON> đ<PERSON> email"}, "placeholders": {"serverName": "<PERSON><PERSON><PERSON><PERSON> tên server...", "host": "smtp.gmail.com", "port": "587", "username": "<EMAIL>", "password": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>...", "additionalSettings": "{}", "recipientEmail": "<EMAIL>", "subject": "Test Email"}}, "actions": {"create": "<PERSON><PERSON><PERSON> mới", "edit": "Chỉnh sửa", "delete": "Xóa", "test": "<PERSON><PERSON><PERSON> tra", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "close": "Đ<PERSON><PERSON>"}, "validation": {"serverName": {"required": "Tên server l<PERSON> b<PERSON><PERSON> bu<PERSON>", "maxLength": "Tên server kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 100 ký tự"}, "host": {"required": "<PERSON> <PERSON><PERSON> b<PERSON><PERSON> b<PERSON>", "maxLength": "Host kh<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 255 ký tự"}, "port": {"min": "Port phải lớn hơn 0", "max": "Port phải nhỏ hơn 65536"}, "username": {"required": "<PERSON><PERSON><PERSON> l<PERSON> b<PERSON> bu<PERSON>c", "email": "Username p<PERSON>i là email hợp lệ"}, "password": {"required": "Password là b<PERSON><PERSON> buộc", "minLength": "Password phải có ít nhất 6 ký tự"}, "additionalSettings": {"invalidJson": "<PERSON><PERSON><PERSON> đặt b<PERSON> sung phải là JSON hợp lệ"}, "recipientEmail": {"email": "Email nh<PERSON>n ph<PERSON><PERSON> là email hợp lệ"}, "subject": {"maxLength": "Tiêu đề không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 200 ký tự"}}, "notifications": {"createSuccess": "Tạo email server thành công", "createError": "Lỗi khi tạo email server", "updateSuccess": "<PERSON><PERSON>p nhật email server thành công", "updateError": "Lỗi khi cập nhật email server", "deleteSuccess": "Xóa email server thành công", "deleteError": "Lỗi khi xóa email server", "testSuccess": "<PERSON><PERSON><PERSON> tra kết nối thành công", "testError": "Lỗi khi kiểm tra kết nối"}, "confirmations": {"delete": "Bạn có chắc chắn muốn xóa email server này?", "deleteTitle": "<PERSON><PERSON><PERSON>n x<PERSON>a"}}, "providerModel": {"title": "<PERSON><PERSON><PERSON><PERSON> lý Provider Model", "description": "<PERSON><PERSON><PERSON><PERSON> lý các nhà cung cấp AI và cấu hình API key", "list": {"title": "<PERSON><PERSON> Provider Model", "empty": "Chưa có provider model nào", "search": "T<PERSON>m kiếm theo tên provider...", "columns": {"name": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}}, "form": {"create": "Tạo Provider Model", "edit": "Chỉnh sửa Provider Model", "view": "Xem Provider Model", "fields": {"name": "<PERSON><PERSON><PERSON>", "type": "Loại Provider", "apiKey": "API Key"}, "placeholders": {"name": "Nhập tên provider...", "apiKey": "Nhập API key..."}}, "actions": {"create": "<PERSON><PERSON><PERSON> mới", "edit": "Chỉnh sửa", "view": "<PERSON>em chi tiết", "delete": "Xóa", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}, "validation": {"name": {"required": "Tên provider là bắt buộc", "maxLength": "Tên provider kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 255 ký tự"}, "type": {"required": "Loại provider là bắt buộc"}, "apiKey": {"required": "API key l<PERSON> b<PERSON><PERSON> buộc", "minLength": "API key ph<PERSON>i có ít nhất 10 ký tự"}}, "notifications": {"createSuccess": "Tạo provider model thành công", "createError": "Lỗi khi tạo provider model", "updateSuccess": "Cập nhật provider model thành công", "updateError": "Lỗi khi cập nhật provider model", "deleteSuccess": "Xóa provider model thành công", "deleteError": "Lỗi khi xóa provider model"}, "confirmations": {"delete": "Bạn có chắc chắn muốn xóa provider model này?", "deleteTitle": "<PERSON><PERSON><PERSON>n x<PERSON>a"}, "providers": {"OPENAI": "OpenAI", "ANTHROPIC": "Anthropic", "GOOGLE": "Google", "META": "Meta", "DEEPSEEK": "DeepSeek", "XAI": "XAI"}}}}