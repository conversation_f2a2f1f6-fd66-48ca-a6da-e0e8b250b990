import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { ZaloZnsMessage } from '../entities/zalo-zns-message.entity';

/**
 * Repository cho ZaloZnsMessage
 */
@Injectable()
export class ZaloZnsMessageRepository {
  constructor(
    @InjectRepository(ZaloZnsMessage)
    private readonly repository: Repository<ZaloZnsMessage>,
  ) {}

  /**
   * Tìm kiếm nhiều tin nhắn ZNS
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách tin nhắn ZNS
   */
  async find(options?: FindManyOptions<ZaloZnsMessage>): Promise<ZaloZnsMessage[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một tin nhắn ZNS
   * @param options Tùy chọn tìm kiếm
   * @returns Tin nhắn ZNS hoặc null
   */
  async findOne(options?: FindOneOptions<ZaloZnsMessage>): Promise<ZaloZnsMessage | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Tìm tin nhắn ZNS theo ID
   * @param id ID của tin nhắn ZNS
   * @returns Tin nhắn ZNS hoặc null
   */
  async findById(id: number): Promise<ZaloZnsMessage | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm tin nhắn ZNS theo ID tin nhắn trên Zalo
   * @param messageId ID của tin nhắn trên Zalo
   * @returns Tin nhắn ZNS hoặc null
   */
  async findByMessageId(messageId: string): Promise<ZaloZnsMessage | null> {
    return this.repository.findOne({ where: { messageId } });
  }

  /**
   * Tìm tin nhắn ZNS theo ID giao dịch
   * @param trackingId ID giao dịch
   * @returns Tin nhắn ZNS hoặc null
   */
  async findByTrackingId(trackingId: string): Promise<ZaloZnsMessage | null> {
    return this.repository.findOne({ where: { trackingId } });
  }

  /**
   * Tìm tất cả tin nhắn ZNS của một người dùng
   * @param userId ID của người dùng
   * @param options Tùy chọn tìm kiếm bổ sung
   * @returns Danh sách tin nhắn ZNS
   */
  async findByUserId(userId: number, options?: FindManyOptions<ZaloZnsMessage>): Promise<ZaloZnsMessage[]> {
    const findOptions: FindManyOptions<ZaloZnsMessage> = {
      where: { userId },
      ...options,
    };
    return this.repository.find(findOptions);
  }

  /**
   * Tìm tất cả tin nhắn ZNS của một Official Account
   * @param oaId ID của Official Account
   * @param options Tùy chọn tìm kiếm bổ sung
   * @returns Danh sách tin nhắn ZNS
   */
  async findByOaId(oaId: string, options?: FindManyOptions<ZaloZnsMessage>): Promise<ZaloZnsMessage[]> {
    const findOptions: FindManyOptions<ZaloZnsMessage> = {
      where: { oaId },
      ...options,
    };
    return this.repository.find(findOptions);
  }

  /**
   * Tìm tất cả tin nhắn ZNS theo số điện thoại
   * @param phone Số điện thoại
   * @param options Tùy chọn tìm kiếm bổ sung
   * @returns Danh sách tin nhắn ZNS
   */
  async findByPhone(phone: string, options?: FindManyOptions<ZaloZnsMessage>): Promise<ZaloZnsMessage[]> {
    const findOptions: FindManyOptions<ZaloZnsMessage> = {
      where: { phone },
      ...options,
    };
    return this.repository.find(findOptions);
  }

  /**
   * Tìm tất cả tin nhắn ZNS theo trạng thái
   * @param status Trạng thái tin nhắn
   * @param options Tùy chọn tìm kiếm bổ sung
   * @returns Danh sách tin nhắn ZNS
   */
  async findByStatus(status: string, options?: FindManyOptions<ZaloZnsMessage>): Promise<ZaloZnsMessage[]> {
    const findOptions: FindManyOptions<ZaloZnsMessage> = {
      where: { status },
      ...options,
    };
    return this.repository.find(findOptions);
  }

  /**
   * Tạo mới tin nhắn ZNS
   * @param data Dữ liệu tin nhắn ZNS
   * @returns Tin nhắn ZNS đã tạo
   */
  async create(data: Partial<ZaloZnsMessage>): Promise<ZaloZnsMessage> {
    const message = this.repository.create(data);
    return this.repository.save(message);
  }

  /**
   * Tạo nhiều tin nhắn ZNS
   * @param dataArray Mảng dữ liệu tin nhắn ZNS
   * @returns Danh sách tin nhắn ZNS đã tạo
   */
  async createMany(dataArray: Partial<ZaloZnsMessage>[]): Promise<ZaloZnsMessage[]> {
    const messages = this.repository.create(dataArray);
    return this.repository.save(messages);
  }

  /**
   * Cập nhật tin nhắn ZNS
   * @param id ID của tin nhắn ZNS
   * @param data Dữ liệu cập nhật
   * @returns Tin nhắn ZNS đã cập nhật
   */
  async update(id: number, data: Partial<ZaloZnsMessage>): Promise<ZaloZnsMessage | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Cập nhật tin nhắn ZNS theo ID giao dịch
   * @param trackingId ID giao dịch
   * @param data Dữ liệu cập nhật
   * @returns Tin nhắn ZNS đã cập nhật
   */
  async updateByTrackingId(trackingId: string, data: Partial<ZaloZnsMessage>): Promise<ZaloZnsMessage | null> {
    await this.repository.update({ trackingId }, data);
    return this.findByTrackingId(trackingId);
  }

  /**
   * Xóa tin nhắn ZNS
   * @param id ID của tin nhắn ZNS
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Đếm số lượng tin nhắn ZNS
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng tin nhắn ZNS
   */
  async count(options?: FindManyOptions<ZaloZnsMessage>): Promise<number> {
    return this.repository.count(options);
  }
}
