import { Controller, UseGuards } from '@nestjs/common';
import { ApiB<PERSON>erAuth, ApiTags } from '@nestjs/swagger';
import { UserKeyAdminService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { JwtEmployeeGuard } from '@/modules/auth/guards';

@ApiTags(SWAGGER_API_TAGS.INTEGRATION_ADMIN)
@Controller('admin/integration/user-key')
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtEmployeeGuard)
export class UserKeyAdminController {
  constructor(private readonly userKeyAdminService: UserKeyAdminService) {}
}
