import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, Icon, Badge, Button, Input, FormItem, Select, Modal } from '@/shared/components/common';
import { CustomerDetailData } from './types';

interface CustomerCustomFieldsProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị trường tùy chỉnh của khách hàng
 */
const CustomerCustomFields: React.FC<CustomerCustomFieldsProps> = ({ customer }) => {
  const { t } = useTranslation('business');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newField, setNewField] = useState({
    key: '',
    value: '',
    type: 'string',
  });

  // Check if customer has custom fields
  const hasCustomFields = customer.customFields &&
    Object.keys(customer.customFields).length > 0;

  // Format field value based on type
  const formatFieldValue = (value: unknown): { displayValue: string; type: string } => {
    if (value === null || value === undefined) {
      return { displayValue: t('customer.detail.noValue'), type: 'null' };
    }

    if (typeof value === 'boolean') {
      return {
        displayValue: value ? t('common.yes') : t('common.no'),
        type: 'boolean'
      };
    }

    if (typeof value === 'number') {
      return { displayValue: value.toString(), type: 'number' };
    }

    if (typeof value === 'string') {
      // Check if it's a date
      if (value.match(/^\d{4}-\d{2}-\d{2}/)) {
        try {
          const date = new Date(value);
          return {
            displayValue: date.toLocaleDateString('vi-VN'),
            type: 'date'
          };
        } catch {
          return { displayValue: value, type: 'string' };
        }
      }

      // Check if it's an email
      if (value.includes('@')) {
        return { displayValue: value, type: 'email' };
      }

      // Check if it's a URL
      if (value.startsWith('http')) {
        return { displayValue: value, type: 'url' };
      }

      return { displayValue: value, type: 'string' };
    }

    if (Array.isArray(value)) {
      return {
        displayValue: value.join(', '),
        type: 'array'
      };
    }

    if (typeof value === 'object') {
      return {
        displayValue: JSON.stringify(value, null, 2),
        type: 'object'
      };
    }

    return { displayValue: String(value), type: 'unknown' };
  };

  // Get icon for field type
  const getFieldIcon = (type: string) => {
    switch (type) {
      case 'boolean':
        return 'check-circle';
      case 'number':
        return 'hash';
      case 'date':
        return 'calendar';
      case 'email':
        return 'mail';
      case 'url':
        return 'link';
      case 'array':
        return 'list';
      case 'object':
        return 'code';
      default:
        return 'type';
    }
  };

  // Get badge variant for field type
  const getFieldBadgeVariant = (type: string) => {
    switch (type) {
      case 'boolean':
        return 'success';
      case 'number':
        return 'info';
      case 'date':
        return 'warning';
      case 'email':
      case 'url':
        return 'primary';
      default:
        return 'info';
    }
  };

  // Field type options
  const fieldTypeOptions = [
    { value: 'string', label: t('customer.customField.types.string') },
    { value: 'number', label: t('customer.customField.types.number') },
    { value: 'boolean', label: t('customer.customField.types.boolean') },
    { value: 'date', label: t('customer.customField.types.date') },
    { value: 'email', label: t('customer.customField.types.email') },
    { value: 'url', label: t('customer.customField.types.url') },
  ];

  // Handle add new field
  const handleAddField = () => {
    if (!newField.key.trim()) return;

    // Convert value based on type
    let convertedValue: string | number | boolean = newField.value;
    switch (newField.type) {
      case 'number':
        convertedValue = parseFloat(newField.value) || 0;
        break;
      case 'boolean':
        convertedValue = newField.value.toLowerCase() === 'true';
        break;
      case 'date':
        convertedValue = newField.value;
        break;
      default:
        convertedValue = newField.value;
    }

    // TODO: Implement API call to save new field
    console.log('Add new field:', {
      key: newField.key,
      value: convertedValue,
      type: newField.type,
    });

    // Reset form and close modal
    setNewField({ key: '', value: '', type: 'string' });
    setIsAddModalOpen(false);
  };

  // Handle delete field
  const handleDeleteField = (key: string) => {
    // TODO: Implement API call to delete field
    console.log('Delete field:', key);
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="text-foreground">
            {t('customer.detail.customFields')}
          </Typography>
          <div className="flex items-center space-x-2">
            {hasCustomFields && (
              <Typography variant="body2" className="text-muted">
                {Object.keys(customer.customFields || {}).length} {t('customer.detail.fields')}
              </Typography>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAddModalOpen(true)}
            >
              <Icon name="plus" size="sm" className="mr-1" />
              {t('customer.customField.add')}
            </Button>
          </div>
        </div>
      }
      defaultOpen={false}
    >
      {hasCustomFields ? (
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            {Object.entries(customer.customFields || {}).map(([key, value]) => {
              const { displayValue, type } = formatFieldValue(value);

              return (
                <div
                  key={key}
                  className="flex items-start justify-between p-4 border border-border rounded-lg hover:bg-muted/20 transition-colors"
                >
                  <div className="flex items-start space-x-3 flex-1">
                    <div className="p-2 rounded-full bg-muted/30">
                      <Icon name={getFieldIcon(type)} size="sm" className="text-muted" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <Typography variant="body1" className="text-foreground font-medium">
                          {key}
                        </Typography>
                        <Badge variant={getFieldBadgeVariant(type)} size="sm">
                          {type}
                        </Badge>
                      </div>
                      <Typography
                        variant="body2"
                        className={`text-muted ${type === 'object' ? 'font-mono text-xs' : ''}`}
                      >
                        {displayValue}
                      </Typography>
                    </div>
                  </div>

                  <div className="flex items-center space-x-1">
                    {(type === 'email' || type === 'url') && (
                      <button
                        onClick={() => {
                          if (type === 'email') {
                            window.location.href = `mailto:${displayValue}`;
                          } else {
                            window.open(displayValue, '_blank');
                          }
                        }}
                        className="p-1 rounded hover:bg-muted/30 transition-colors"
                        title={t('common.open')}
                      >
                        <Icon name="external-link" size="sm" className="text-muted" />
                      </button>
                    )}
                    <button
                      onClick={() => handleDeleteField(key)}
                      className="p-1 rounded hover:bg-red-100 transition-colors"
                      title={t('common.delete')}
                    >
                      <Icon name="trash-2" size="sm" className="text-red-500" />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Summary */}
          <div className="mt-6 p-4 bg-muted/20 rounded-lg">
            <Typography variant="body2" className="text-muted text-center">
              {t('customer.detail.customFieldsSummary', {
                count: Object.keys(customer.customFields || {}).length
              })}
            </Typography>
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <div className="mb-4">
            <Icon name="settings" size="lg" className="text-muted mx-auto" />
          </div>
          <Typography variant="body1" className="text-muted mb-2">
            {t('customer.detail.noCustomFields')}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {t('customer.detail.noCustomFieldsDesc')}
          </Typography>
        </div>
      )}

      {/* Add Custom Field Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title={t('customer.customField.addTitle')}
        size="md"
      >
        <div className="space-y-4">
          <FormItem label={t('customer.customField.fieldName')} required>
            <Input
              value={newField.key}
              onChange={(e) => setNewField(prev => ({ ...prev, key: e.target.value }))}
              placeholder={t('customer.customField.fieldNamePlaceholder')}
            />
          </FormItem>

          <FormItem label={t('customer.customField.fieldType')} required>
            <Select
              value={newField.type}
              onChange={(value) => setNewField(prev => ({ ...prev, type: String(value) }))}
              options={fieldTypeOptions}
            />
          </FormItem>

          <FormItem label={t('customer.customField.fieldValue')} required>
            {newField.type === 'boolean' ? (
              <Select
                value={newField.value}
                onChange={(value) => setNewField(prev => ({ ...prev, value: String(value) }))}
                options={[
                  { value: 'true', label: t('common.yes') },
                  { value: 'false', label: t('common.no') },
                ]}
              />
            ) : (
              <Input
                value={newField.value}
                onChange={(e) => setNewField(prev => ({ ...prev, value: e.target.value }))}
                placeholder={t('customer.customField.fieldValuePlaceholder')}
                type={newField.type === 'number' ? 'number' : newField.type === 'date' ? 'date' : 'text'}
              />
            )}
          </FormItem>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsAddModalOpen(false)}
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="primary"
              onClick={handleAddField}
              disabled={!newField.key.trim() || !newField.value.trim()}
            >
              {t('common.add')}
            </Button>
          </div>
        </div>
      </Modal>
    </CollapsibleCard>
  );
};

export default CustomerCustomFields;
