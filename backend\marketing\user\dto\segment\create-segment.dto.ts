import { IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { SegmentCriteriaDto } from './segment-criteria.dto';

/**
 * DTO cho việc tạo segment mới
 */
export class CreateSegmentDto {
  /**
   * Tên segment
   * @example "Khách hàng tiềm năng"
   */
  @ApiProperty({
    description: 'Tên segment',
    example: 'Khách hàng tiềm năng',
  })
  @IsNotEmpty({ message: 'Tên segment không được để trống' })
  @IsString({ message: 'Tên segment phải là chuỗi' })
  name: string;

  /**
   * Mô tả segment
   * @example "Khách hàng có khả năng mua hàng cao"
   */
  @ApiProperty({
    description: 'Mô tả segment',
    example: '<PERSON>h<PERSON>ch hàng có khả năng mua hàng cao',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON>ô tả phải là chuỗi' })
  description?: string;

  /**
   * Điều kiện lọc khách hàng
   */
  @ApiProperty({
    description: 'Điều kiện lọc khách hàng',
    type: SegmentCriteriaDto,
  })
  @IsNotEmpty({ message: 'Điều kiện lọc không được để trống' })
  @ValidateNested()
  @Type(() => SegmentCriteriaDto)
  criteria: SegmentCriteriaDto;
}
