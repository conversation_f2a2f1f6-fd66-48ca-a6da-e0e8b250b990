import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { Typography, Card, Alert } from '@/shared/components/common';
import { useAdminToolDetail } from '@/modules/admin/tool/hooks/useTool';
import { useAdminToolVersionDetail } from '@/modules/admin/tool/hooks/useToolVersion';

import IntegrationForm from '../components/IntegrationForm';
import { ToolDetail } from '@/modules/admin/tool/types/tool.types';

/**
 * Trang quản lý tool integration
 */
const IntegrationPage: React.FC = () => {
  const { t } = useTranslation();
  const { toolId, versionId } = useParams<{ toolId: string; versionId: string }>();
  const navigate = useNavigate();

  const [tool, setTool] = useState<ToolDetail | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch tool detail
  const { data: toolDetail, isLoading: isLoadingTool } = useAdminToolDetail(toolId || '');

  // Fetch version detail if versionId is provided
  const { data: versionDetail, isLoading: isLoadingVersion } = useAdminToolVersionDetail(
    toolId || '',
    versionId || ''
  );

  // Integration mutation
  const { mutate: integrateTool, isLoading: isIntegrating } = use();

  // Update tool state when data is loaded
  useEffect(() => {
    if (toolDetail) {
      setTool(toolDetail);
    }
  }, [toolDetail]);

  // Update tool state when version is loaded
  useEffect(() => {
    if (versionDetail && tool) {
      setTool(prev => {
        if (!prev) return null;
        return {
          ...prev,
          defaultVersion: versionDetail,
        };
      });
    }
  }, [versionDetail, tool]);

  // Handle form submission
  const handleSubmit = (values: Record<string, unknown>) => {
    if (!toolId) {
      setError(t('tools.integration.error.noToolId', 'No tool ID provided'));
      return;
    }

    integrateTool(
      {
        toolId,
        versionId: versionId || tool?.defaultVersion?.id,
        parameters: values,
      },
      {
        onSuccess: () => {
          navigate(`/tools/${toolId}`);
        },
        onError: (error: Error) => {
          setError(error.message);
        },
      }
    );
  };

  // Handle cancel
  const handleCancel = () => {
    navigate(`/tools/${toolId}`);
  };

  if (isLoadingTool || isLoadingVersion) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Typography variant="body1" className="text-gray-500">
          {t('common.loading', 'Loading...')}
        </Typography>
      </div>
    );
  }

  if (!tool) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Typography variant="body1" className="text-red-500">
          {t('tools.integration.error.toolNotFound', 'Tool not found')}
        </Typography>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <Card className="p-6">
          {error && (
            <Alert variant="error" className="mb-4">
              {error}
            </Alert>
          )}

          <IntegrationForm
            tool={tool}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isLoading={isIntegrating}
          />
        </Card>
      </div>
    </div>
  );
};

export default IntegrationPage;
