import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng google_ads_ad_groups trong cơ sở dữ liệu
 * Lưu trữ thông tin nhóm quảng cáo Google Ads
 */
@Entity('google_ads_ad_groups')
export class GoogleAdsAdGroup {
  /**
   * ID của nhóm quảng cáo trong hệ thống
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })
  userId: number;

  /**
   * ID của chiến dịch trong hệ thống
   */
  @Column({ name: 'campaign_id', nullable: false, comment: 'ID của chiến dịch trong hệ thống' })
  campaignId: number;

  /**
   * ID của nhóm quảng cáo trên Google Ads
   */
  @Column({ name: 'ad_group_id', nullable: false, comment: 'ID của nhóm quảng cáo trên Google Ads' })
  adGroupId: string;

  /**
   * Tên nhóm quảng cáo
   */
  @Column({ name: 'name', length: 255, nullable: false, comment: 'Tên nhóm quảng cáo' })
  name: string;

  /**
   * Trạng thái nhóm quảng cáo
   */
  @Column({ name: 'status', length: 20, nullable: false, comment: 'Trạng thái nhóm quảng cáo' })
  status: string;

  /**
   * CPC tối đa (micro amount)
   */
  @Column({ name: 'cpc_bid_micros', type: 'bigint', nullable: true, comment: 'CPC tối đa (micro amount)' })
  cpcBidMicros: number;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
